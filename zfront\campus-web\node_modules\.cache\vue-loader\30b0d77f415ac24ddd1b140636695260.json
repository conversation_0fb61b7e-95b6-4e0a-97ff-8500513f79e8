{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\Role.vue?vue&type=style&index=0&id=115c3ebc&scoped=true&lang=less&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\Role.vue", "mtime": 1748712855482}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1737774014010}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1737774014071}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1737774014048}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1737774014037}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouY29udGVudCB7CiAgICBwYWRkaW5nOiAwIDElOwp9Cg=="}, {"version": 3, "sources": ["Role.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsUA;AACA;AACA", "file": "Role.vue", "sourceRoot": "src/views/admin/children", "sourcesContent": ["<template>\n    <div class=\"content\">\n        <el-table\n                :data=\"dataList\"\n                style=\"width: 100%;margin-bottom: 20px;\"\n                row-key=\"uuid\"\n                border\n                :default-expand-all = 'false'\n                :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\">\n            <el-table-column\n                    prop=\"name\"\n                    label=\"角色\"\n                    sortable\n                    width=\"230\">\n            </el-table-column>\n            <el-table-column\n                    prop=\"deptname\"\n                    label=\"类别\"\n                    sortable\n                    width=\"180\">\n            </el-table-column>\n            <el-table-column\n                    prop=\"classesname\"\n                    label=\"子类别\">\n            </el-table-column>\n\n            <el-table-column label=\"操作\">\n                <template slot-scope=\"scope\">\n                    <el-button\n                            icon=\"el-icon-plus\"\n                            size=\"mini\"\n                            @click=\"handleAdd(scope.$index, scope.row)\">增加\n                    </el-button>\n\n                    <el-button\n                            icon=\"el-icon-edit\"\n                            size=\"mini\"\n                            @click=\"handleUpdate(scope.$index, scope.row)\">修改\n                    </el-button>\n\n                    <el-button\n                            icon=\"el-icon-delete\"\n                            size=\"mini\"\n                            type=\"danger\"\n                            @click=\"handleDelete(scope.$index, scope.row)\">删除\n                    </el-button>\n                </template>\n            </el-table-column>\n\n            <!-- ------------新加入---------- -->\n\n\n            <!-- 添加或修改角色管理对话框 -->\n            <el-dialog :title=\"title\"  :visible.sync=\"open\" width=\"500px\" append-to-body>\n                <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n                        <el-form-item label=\"角色\" prop=\"name\">\n                        <el-input v-model=\"form.name\" placeholder=\"请输入角色\" />\n                        </el-form-item>\n                        <el-form-item label=\"类别\" prop=\"deptname\">\n                        <el-input v-model=\"form.deptname\" placeholder=\"请输入类别\" />\n                        </el-form-item>\n                        <el-form-item label=\"子类别\"  prop=\"classesname\">\n                        <el-input  placeholder=\"请输入子类别\" v-model=\"form.classesname\"/>\n                        </el-form-item>\n                    </el-form>\n                         <div slot=\"footer\" class=\"dialog-footer\" >\n                            <el-button type=\"primary\" @click=\"submit()\">确 定</el-button>\n                            <el-button @click=\"cancel\">取 消</el-button>\n                        </div>\n            </el-dialog>\n\n        </el-table>\n\n\n        <!-- ------------新加入---------- -->\n    </div>\n</template>\n\n<script>\n    export default {\n        name: \"Role\",\n        data() {\n            return {\n                value:null,\n\n                roles : [],\n                // 弹出层标题\n                title: \"\",\n                // 是否显示弹出层\n                open: false,\n                // 表单参数\n                form: {\n                    id: null,\n                    name: '',\n                    deptname: '',\n                    classesname: ''\n                },\n                // 表单校验\n                rules: {\n                    name: [\n                        { required: true, message: '请输入角色名称', trigger: 'blur' }\n                    ],\n                    deptname: [\n                        { required: true, message: '请输入类别名称', trigger: 'blur' }\n                    ],\n                    classesname: [\n                        { required: true, message: '请输入子类别名称', trigger: 'blur' }\n                    ]\n                },\n            }\n        },\n        methods: {\n            guid2() {\n                function S4() {\n                    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);\n                }\n                return (S4() + S4() + \"-\" + S4() + \"-\" + S4() + \"-\" + S4() + \"-\" + S4() + S4() + S4());\n            },\n\n            //-------新加入---------\n\n            /** 新增按钮操作 */\n            handleAdd(a,b) {\n                console.log(b.children.length)\n                // console.log(b)\n                if(b.roleId != null){\n                    this.value=b;\n                    this.reset();\n                    this.title = \"添加角色信息\";\n                    this.open = true;\n                }\n            },\n\n            /** 提交按钮 */\n            submit() {\n                this.$refs[\"form\"].validate(valid => {\n                    if (valid) {\n                        if (this.form.id != null) {\n                            // 修改角色信息\n                            this.$put(\"/role\", this.form).then(response => {\n                                this.$message(\"修改成功\");\n                                this.open = false;\n                                this.newList();\n                            }).catch(error => {\n                                this.$message.error(\"修改失败：\" + error.message);\n                            });\n                        } else {\n                            // 先添加角色\n                            this.$post(\"/role\", {\n                                \"name\": this.form.name,\n                            }).then(response => {\n                                this.$message(\"新增角色成功\");\n                                this.newList();\n\n                                // 添加系别\n                                this.$post(\"/dept\", {\n                                    \"roleId\": this.value.roleId,\n                                    \"name\": this.form.deptname,\n                                }).then(response => {\n                                    const name = this.form.deptname;\n                                    // 遍历查找匹配的系别\n                                    for (let i = 0; i < this.value.children.length; i++) {\n                                        if (this.value.children[i].deptname === name) {\n                                            this.$message(\"新增专业成功\");\n                                            this.newList();\n\n                                            // 添加班级\n                                            this.$post(\"/class\", {\n                                                \"roleId\": this.value.roleId,\n                                                \"deptId\": this.value.children[i].deptsId,\n                                                \"name\": this.form.classesname,\n                                            }).then(response => {\n                                                this.$message(\"新增班级成功\");\n                                                this.newList();\n                                                this.open = false;\n                                            }).catch(error => {\n                                                this.$message.error(\"添加班级失败：\" + error.message);\n                                            });\n                                            break;\n                                        }\n                                    }\n                                }).catch(error => {\n                                    this.$message.error(\"添加系别失败：\" + error.message);\n                                });\n                            }).catch(error => {\n                                this.$message.error(\"添加角色失败：\" + error.message);\n                            });\n                        }\n                    }\n                });\n            },\n\n\n\n            // 取消按钮\n            cancel() {\n            this.open = false;\n            },\n\n            handleDelete(a,b){\n                console.log(b);\n                if(b.roleId != null){\n                    this.$del(\"/role/\"+b.roleId)\n                    .then((res) => {\n                        this.$notifyMsg(\"成功\", res.data.msg, \"success\")\n                        this.newList()\n                    })\n                }else if(b.deptsId != null){\n                     this.$del(\"/dept/\"+b.deptsId)\n                        .then((res) => {\n                            this.$notifyMsg(\"成功\", res.data.msg, \"success\")\n                            this.newList()\n                        })\n                }else{\n                    this.$del(\"/class/\"+b.classesId)\n                        .then((res) => {\n                            this.$notifyMsg(\"成功\", res.data.msg, \"success\")\n                            this.newList()\n                        })\n                }\n            },\n\n\n            /** 修改按钮操作 */\n            handleUpdate(a,b) {\n                // console.log(b);\n                this.$get(\"/role/\"+b.roleId).then((res) => {\n                    this.form = res.data.role;\n                    this.open = true;\n                    this.title = \"修改角色管理\";\n                });\n            },\n\n            newList(){\n                this.$get('/role')\n                .then(res => {\n                    // console.log(res.data)\n                    // console.log(this.roles)\n                    if (res.data && res.data.role) {\n                        this.roles = res.data.role;\n                    } else {\n                        this.roles = [];\n                        console.warn('获取角色数据失败，返回数据格式不正确');\n                    }\n                })\n                .catch(error => {\n                    console.error('获取角色列表失败:', error);\n                    this.roles = [];\n                    this.$message.error('获取角色列表失败，请稍后重试');\n                });\n            },\n\n            // 表单重置\n            reset() {\n                this.form = {\n                    id: null,\n                    name: '',\n                    deptname: '',\n                    classesname: ''\n                };\n                if (this.$refs.form) {\n                    this.$refs.form.resetFields();\n                }\n            },\n            //-------新加入---------\n        },\n\n        created() {\n            this.newList();\n        },\n\n        computed:{\n            dataList(){\n                if (!this.roles || !Array.isArray(this.roles)) {\n                    return [];\n                }\n\n                return this.roles.map(item => {\n                    if (!item) return null;\n\n                    let depts = [];\n                    const itemDepts = item['depts'] || [];\n\n                    for (let i = 0; i < itemDepts.length; i++){\n                        const dept = itemDepts[i];\n                        if (!dept) continue;\n\n                        // console.log(dept['classes'])\n                        depts.push(\n                            {\n                                deptsId : dept.id,\n                                deptname : dept.name || '',\n                                uuid : this.guid2()\n                            }\n                        )\n                        // console.log(depts)\n                        let children = [];\n                        const deptClasses = dept['classes'] || [];\n\n                        for (let j = 0; j < deptClasses.length; j++){\n                            const classItem = deptClasses[j];\n                            if (!classItem) continue;\n\n                            children.push(\n                                {\n                                    classesId : classItem.id,\n                                    classesname: classItem.name || '',\n                                    uuid : this.guid2()\n                                }\n                            )\n                        }\n                        depts[i].children = children\n                    }\n                    return{\n                            roleId : item.id,\n                            name : item.name || '',\n                            children : depts,\n                            uuid : this.guid2()\n                    }\n                }).filter(item => item !== null);\n            }\n        }\n    }\n</script>\n\n<style scoped lang=\"less\">\n    .content {\n        padding: 0 1%;\n    }\n</style>\n"]}]}