{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\Role.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\Role.vue", "mtime": 1748712855482}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Role.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+EA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Role.vue", "sourceRoot": "src/views/admin/children", "sourcesContent": ["<template>\n    <div class=\"content\">\n        <el-table\n                :data=\"dataList\"\n                style=\"width: 100%;margin-bottom: 20px;\"\n                row-key=\"uuid\"\n                border\n                :default-expand-all = 'false'\n                :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\">\n            <el-table-column\n                    prop=\"name\"\n                    label=\"角色\"\n                    sortable\n                    width=\"230\">\n            </el-table-column>\n            <el-table-column\n                    prop=\"deptname\"\n                    label=\"类别\"\n                    sortable\n                    width=\"180\">\n            </el-table-column>\n            <el-table-column\n                    prop=\"classesname\"\n                    label=\"子类别\">\n            </el-table-column>\n\n            <el-table-column label=\"操作\">\n                <template slot-scope=\"scope\">\n                    <el-button\n                            icon=\"el-icon-plus\"\n                            size=\"mini\"\n                            @click=\"handleAdd(scope.$index, scope.row)\">增加\n                    </el-button>\n\n                    <el-button\n                            icon=\"el-icon-edit\"\n                            size=\"mini\"\n                            @click=\"handleUpdate(scope.$index, scope.row)\">修改\n                    </el-button>\n\n                    <el-button\n                            icon=\"el-icon-delete\"\n                            size=\"mini\"\n                            type=\"danger\"\n                            @click=\"handleDelete(scope.$index, scope.row)\">删除\n                    </el-button>\n                </template>\n            </el-table-column>\n\n            <!-- ------------新加入---------- -->\n\n\n            <!-- 添加或修改角色管理对话框 -->\n            <el-dialog :title=\"title\"  :visible.sync=\"open\" width=\"500px\" append-to-body>\n                <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n                        <el-form-item label=\"角色\" prop=\"name\">\n                        <el-input v-model=\"form.name\" placeholder=\"请输入角色\" />\n                        </el-form-item>\n                        <el-form-item label=\"类别\" prop=\"deptname\">\n                        <el-input v-model=\"form.deptname\" placeholder=\"请输入类别\" />\n                        </el-form-item>\n                        <el-form-item label=\"子类别\"  prop=\"classesname\">\n                        <el-input  placeholder=\"请输入子类别\" v-model=\"form.classesname\"/>\n                        </el-form-item>\n                    </el-form>\n                         <div slot=\"footer\" class=\"dialog-footer\" >\n                            <el-button type=\"primary\" @click=\"submit()\">确 定</el-button>\n                            <el-button @click=\"cancel\">取 消</el-button>\n                        </div>\n            </el-dialog>\n\n        </el-table>\n\n\n        <!-- ------------新加入---------- -->\n    </div>\n</template>\n\n<script>\n    export default {\n        name: \"Role\",\n        data() {\n            return {\n                value:null,\n\n                roles : [],\n                // 弹出层标题\n                title: \"\",\n                // 是否显示弹出层\n                open: false,\n                // 表单参数\n                form: {\n                    id: null,\n                    name: '',\n                    deptname: '',\n                    classesname: ''\n                },\n                // 表单校验\n                rules: {\n                    name: [\n                        { required: true, message: '请输入角色名称', trigger: 'blur' }\n                    ],\n                    deptname: [\n                        { required: true, message: '请输入类别名称', trigger: 'blur' }\n                    ],\n                    classesname: [\n                        { required: true, message: '请输入子类别名称', trigger: 'blur' }\n                    ]\n                },\n            }\n        },\n        methods: {\n            guid2() {\n                function S4() {\n                    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);\n                }\n                return (S4() + S4() + \"-\" + S4() + \"-\" + S4() + \"-\" + S4() + \"-\" + S4() + S4() + S4());\n            },\n\n            //-------新加入---------\n\n            /** 新增按钮操作 */\n            handleAdd(a,b) {\n                console.log(b.children.length)\n                // console.log(b)\n                if(b.roleId != null){\n                    this.value=b;\n                    this.reset();\n                    this.title = \"添加角色信息\";\n                    this.open = true;\n                }\n            },\n\n            /** 提交按钮 */\n            submit() {\n                this.$refs[\"form\"].validate(valid => {\n                    if (valid) {\n                        if (this.form.id != null) {\n                            // 修改角色信息\n                            this.$put(\"/role\", this.form).then(response => {\n                                this.$message(\"修改成功\");\n                                this.open = false;\n                                this.newList();\n                            }).catch(error => {\n                                this.$message.error(\"修改失败：\" + error.message);\n                            });\n                        } else {\n                            // 先添加角色\n                            this.$post(\"/role\", {\n                                \"name\": this.form.name,\n                            }).then(response => {\n                                this.$message(\"新增角色成功\");\n                                this.newList();\n\n                                // 添加系别\n                                this.$post(\"/dept\", {\n                                    \"roleId\": this.value.roleId,\n                                    \"name\": this.form.deptname,\n                                }).then(response => {\n                                    const name = this.form.deptname;\n                                    // 遍历查找匹配的系别\n                                    for (let i = 0; i < this.value.children.length; i++) {\n                                        if (this.value.children[i].deptname === name) {\n                                            this.$message(\"新增专业成功\");\n                                            this.newList();\n\n                                            // 添加班级\n                                            this.$post(\"/class\", {\n                                                \"roleId\": this.value.roleId,\n                                                \"deptId\": this.value.children[i].deptsId,\n                                                \"name\": this.form.classesname,\n                                            }).then(response => {\n                                                this.$message(\"新增班级成功\");\n                                                this.newList();\n                                                this.open = false;\n                                            }).catch(error => {\n                                                this.$message.error(\"添加班级失败：\" + error.message);\n                                            });\n                                            break;\n                                        }\n                                    }\n                                }).catch(error => {\n                                    this.$message.error(\"添加系别失败：\" + error.message);\n                                });\n                            }).catch(error => {\n                                this.$message.error(\"添加角色失败：\" + error.message);\n                            });\n                        }\n                    }\n                });\n            },\n\n\n\n            // 取消按钮\n            cancel() {\n            this.open = false;\n            },\n\n            handleDelete(a,b){\n                console.log(b);\n                if(b.roleId != null){\n                    this.$del(\"/role/\"+b.roleId)\n                    .then((res) => {\n                        this.$notifyMsg(\"成功\", res.data.msg, \"success\")\n                        this.newList()\n                    })\n                }else if(b.deptsId != null){\n                     this.$del(\"/dept/\"+b.deptsId)\n                        .then((res) => {\n                            this.$notifyMsg(\"成功\", res.data.msg, \"success\")\n                            this.newList()\n                        })\n                }else{\n                    this.$del(\"/class/\"+b.classesId)\n                        .then((res) => {\n                            this.$notifyMsg(\"成功\", res.data.msg, \"success\")\n                            this.newList()\n                        })\n                }\n            },\n\n\n            /** 修改按钮操作 */\n            handleUpdate(a,b) {\n                // console.log(b);\n                this.$get(\"/role/\"+b.roleId).then((res) => {\n                    this.form = res.data.role;\n                    this.open = true;\n                    this.title = \"修改角色管理\";\n                });\n            },\n\n            newList(){\n                this.$get('/role')\n                .then(res => {\n                    // console.log(res.data)\n                    // console.log(this.roles)\n                    if (res.data && res.data.role) {\n                        this.roles = res.data.role;\n                    } else {\n                        this.roles = [];\n                        console.warn('获取角色数据失败，返回数据格式不正确');\n                    }\n                })\n                .catch(error => {\n                    console.error('获取角色列表失败:', error);\n                    this.roles = [];\n                    this.$message.error('获取角色列表失败，请稍后重试');\n                });\n            },\n\n            // 表单重置\n            reset() {\n                this.form = {\n                    id: null,\n                    name: '',\n                    deptname: '',\n                    classesname: ''\n                };\n                if (this.$refs.form) {\n                    this.$refs.form.resetFields();\n                }\n            },\n            //-------新加入---------\n        },\n\n        created() {\n            this.newList();\n        },\n\n        computed:{\n            dataList(){\n                if (!this.roles || !Array.isArray(this.roles)) {\n                    return [];\n                }\n\n                return this.roles.map(item => {\n                    if (!item) return null;\n\n                    let depts = [];\n                    const itemDepts = item['depts'] || [];\n\n                    for (let i = 0; i < itemDepts.length; i++){\n                        const dept = itemDepts[i];\n                        if (!dept) continue;\n\n                        // console.log(dept['classes'])\n                        depts.push(\n                            {\n                                deptsId : dept.id,\n                                deptname : dept.name || '',\n                                uuid : this.guid2()\n                            }\n                        )\n                        // console.log(depts)\n                        let children = [];\n                        const deptClasses = dept['classes'] || [];\n\n                        for (let j = 0; j < deptClasses.length; j++){\n                            const classItem = deptClasses[j];\n                            if (!classItem) continue;\n\n                            children.push(\n                                {\n                                    classesId : classItem.id,\n                                    classesname: classItem.name || '',\n                                    uuid : this.guid2()\n                                }\n                            )\n                        }\n                        depts[i].children = children\n                    }\n                    return{\n                            roleId : item.id,\n                            name : item.name || '',\n                            children : depts,\n                            uuid : this.guid2()\n                    }\n                }).filter(item => item !== null);\n            }\n        }\n    }\n</script>\n\n<style scoped lang=\"less\">\n    .content {\n        padding: 0 1%;\n    }\n</style>\n"]}]}