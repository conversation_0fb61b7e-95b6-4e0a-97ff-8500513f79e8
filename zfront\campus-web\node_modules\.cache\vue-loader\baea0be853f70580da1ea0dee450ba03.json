{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\Role.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\Role.vue", "mtime": 1748715510969}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICAgIG5hbWU6ICJSb2xlIiwKICAgIGRhdGEoKSB7CiAgICAgICAgcmV0dXJuIHsKICAgICAgICAgICAgcm9sZXM6IFtdLAogICAgICAgICAgICByb2xlT3B0aW9uczogW10sIC8vIOinkuiJsumAiemhueWIl+ihqAoKICAgICAgICAgICAgLy8g57u05L+u57G75Yir5by556qX55u45YWzCiAgICAgICAgICAgIGNhdGVnb3J5RGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgICAgICAgIGNhdGVnb3J5RGlhbG9nVGl0bGU6ICcnLAogICAgICAgICAgICBpc0NhdGVnb3J5RWRpdDogZmFsc2UsIC8vIOaYr+WQpuS4uue8lui+keaooeW8jwoKICAgICAgICAgICAgLy8g57u05L+u57G75Yir6KGo5Y2V5Y+C5pWwCiAgICAgICAgICAgIGNhdGVnb3J5Rm9ybTogewogICAgICAgICAgICAgICAgaWQ6IG51bGwsCiAgICAgICAgICAgICAgICBuYW1lOiAnJywKICAgICAgICAgICAgICAgIHJvbGVJZDogbnVsbAogICAgICAgICAgICB9LAoKICAgICAgICAgICAgLy8g57u05L+u57G75Yir6KGo5Y2V5qCh6aqMCiAgICAgICAgICAgIGNhdGVnb3J5UnVsZXM6IHsKICAgICAgICAgICAgICAgIG5hbWU6IFsKICAgICAgICAgICAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl57u05L+u57G75Yir5ZCN56ewJywgdHJpZ2dlcjogJ2JsdXInIH0KICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICByb2xlSWQ6IFsKICAgICAgICAgICAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup6KeS6ImyJywgdHJpZ2dlcjogJ2NoYW5nZScgfQogICAgICAgICAgICAgICAgXQogICAgICAgICAgICB9LAoKICAgICAgICAgICAgLy8g5oq95bGJ55u45YWz77yI5LuF55So5LqO5a2Q57G75Yir77yJCiAgICAgICAgICAgIGRyYXdlclZpc2libGU6IGZhbHNlLAogICAgICAgICAgICBkcmF3ZXJUaXRsZTogJycsCiAgICAgICAgICAgIGlzRWRpdDogZmFsc2UsIC8vIOaYr+WQpuS4uue8lui+keaooeW8jwoKICAgICAgICAgICAgLy8g5a2Q57G75Yir6KGo5Y2V5Y+C5pWwCiAgICAgICAgICAgIGZvcm06IHsKICAgICAgICAgICAgICAgIGlkOiBudWxsLAogICAgICAgICAgICAgICAgbmFtZTogJycsCiAgICAgICAgICAgICAgICByb2xlSWQ6IG51bGwsCiAgICAgICAgICAgICAgICBkZXB0SWQ6IG51bGwKICAgICAgICAgICAgfSwKCiAgICAgICAgICAgIC8vIOeItue6p+S/oeaBr++8iOeUqOS6juWtkOexu+WIq+ihqOWNleaYvuekuu+8iQogICAgICAgICAgICBwYXJlbnRSb2xlTmFtZTogJycsCiAgICAgICAgICAgIHBhcmVudENhdGVnb3J5TmFtZTogJycsCiAgICAgICAgICAgIHBhcmVudENhdGVnb3J5SWQ6IG51bGwsCiAgICAgICAgICAgIHBhcmVudFJvbGVJZDogbnVsbCwKCiAgICAgICAgICAgIC8vIOWtkOexu+WIq+ihqOWNleagoemqjAogICAgICAgICAgICBydWxlczogewogICAgICAgICAgICAgICAgbmFtZTogWwogICAgICAgICAgICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXnu7Tkv67lrZDnsbvliKvlkI3np7AnLCB0cmlnZ2VyOiAnYmx1cicgfQogICAgICAgICAgICAgICAgXQogICAgICAgICAgICB9LAogICAgICAgIH0KICAgIH0sCiAgICBtZXRob2RzOiB7CiAgICAgICAgZ3VpZDIoKSB7CiAgICAgICAgICAgIGZ1bmN0aW9uIFM0KCkgewogICAgICAgICAgICAgICAgcmV0dXJuICgoKDEgKyBNYXRoLnJhbmRvbSgpKSAqIDB4MTAwMDApIHwgMCkudG9TdHJpbmcoMTYpLnN1YnN0cmluZygxKTsKICAgICAgICAgICAgfQogICAgICAgICAgICByZXR1cm4gKFM0KCkgKyBTNCgpICsgIi0iICsgUzQoKSArICItIiArIFM0KCkgKyAiLSIgKyBTNCgpICsgIi0iICsgUzQoKSArIFM0KCkgKyBTNCgpKTsKICAgICAgICB9LAoKICAgICAgICAvLy0tLS0tLS3mlrDnmoTmlrnms5UtLS0tLS0tLS0KCiAgICAgICAgLy8g5re75Yqg57u05L+u57G75YirCiAgICAgICAgaGFuZGxlQWRkQ2F0ZWdvcnkoKSB7CiAgICAgICAgICAgIHRoaXMucmVzZXRDYXRlZ29yeUZvcm0oKTsKICAgICAgICAgICAgdGhpcy5pc0NhdGVnb3J5RWRpdCA9IGZhbHNlOwogICAgICAgICAgICB0aGlzLmNhdGVnb3J5RGlhbG9nVGl0bGUgPSAn5re75Yqg57u05L+u57G75YirJzsKICAgICAgICAgICAgdGhpcy5jYXRlZ29yeURpYWxvZ1Zpc2libGUgPSB0cnVlOwogICAgICAgIH0sCgogICAgICAgIC8vIOa3u+WKoOWtkOexu+WIqwogICAgICAgIGhhbmRsZUFkZFN1YmNhdGVnb3J5KGNhdGVnb3J5Um93KSB7CiAgICAgICAgICAgIHRoaXMucmVzZXRGb3JtKCk7CiAgICAgICAgICAgIHRoaXMuZm9ybVR5cGUgPSAnc3ViY2F0ZWdvcnknOwogICAgICAgICAgICB0aGlzLmlzRWRpdCA9IGZhbHNlOwogICAgICAgICAgICB0aGlzLmRyYXdlclRpdGxlID0gJ+a3u+WKoOe7tOS/ruWtkOexu+WIqyc7CgogICAgICAgICAgICAvLyDorr7nva7niLbnuqfkv6Hmga8KICAgICAgICAgICAgdGhpcy5wYXJlbnRDYXRlZ29yeUlkID0gY2F0ZWdvcnlSb3cuZGVwdHNJZDsKICAgICAgICAgICAgdGhpcy5wYXJlbnRDYXRlZ29yeU5hbWUgPSBjYXRlZ29yeVJvdy5kZXB0bmFtZTsKICAgICAgICAgICAgdGhpcy5wYXJlbnRSb2xlSWQgPSBjYXRlZ29yeVJvdy5yb2xlSWQ7CiAgICAgICAgICAgIHRoaXMucGFyZW50Um9sZU5hbWUgPSBjYXRlZ29yeVJvdy5yb2xlTmFtZTsKCiAgICAgICAgICAgIHRoaXMuZHJhd2VyVmlzaWJsZSA9IHRydWU7CiAgICAgICAgfSwKCiAgICAgICAgLy8g5L+u5pS557u05L+u57G75YirCiAgICAgICAgaGFuZGxlVXBkYXRlQ2F0ZWdvcnkoY2F0ZWdvcnlSb3cpIHsKICAgICAgICAgICAgdGhpcy5pc0NhdGVnb3J5RWRpdCA9IHRydWU7CiAgICAgICAgICAgIHRoaXMuY2F0ZWdvcnlEaWFsb2dUaXRsZSA9ICfkv67mlLnnu7Tkv67nsbvliKsnOwoKICAgICAgICAgICAgdGhpcy5jYXRlZ29yeUZvcm0gPSB7CiAgICAgICAgICAgICAgICBpZDogY2F0ZWdvcnlSb3cuZGVwdHNJZCwKICAgICAgICAgICAgICAgIG5hbWU6IGNhdGVnb3J5Um93LmRlcHRuYW1lLAogICAgICAgICAgICAgICAgcm9sZUlkOiBjYXRlZ29yeVJvdy5yb2xlSWQKICAgICAgICAgICAgfTsKCiAgICAgICAgICAgIHRoaXMuY2F0ZWdvcnlEaWFsb2dWaXNpYmxlID0gdHJ1ZTsKICAgICAgICB9LAoKICAgICAgICAvLyDkv67mlLnnu7Tkv67lrZDnsbvliKsKICAgICAgICBoYW5kbGVVcGRhdGVTdWJjYXRlZ29yeShzdWJjYXRlZ29yeVJvdykgewogICAgICAgICAgICB0aGlzLmlzRWRpdCA9IHRydWU7CiAgICAgICAgICAgIHRoaXMuZHJhd2VyVGl0bGUgPSAn5L+u5pS557u05L+u5a2Q57G75YirJzsKCiAgICAgICAgICAgIC8vIOS7jueItue6p+aVsOaNruS4reiOt+WPluS/oeaBrwogICAgICAgICAgICBjb25zdCBwYXJlbnRDYXRlZ29yeSA9IHRoaXMuZmluZFBhcmVudENhdGVnb3J5KHN1YmNhdGVnb3J5Um93LmNsYXNzZXNJZCk7CiAgICAgICAgICAgIGlmIChwYXJlbnRDYXRlZ29yeSkgewogICAgICAgICAgICAgICAgdGhpcy5wYXJlbnRDYXRlZ29yeUlkID0gcGFyZW50Q2F0ZWdvcnkuZGVwdHNJZDsKICAgICAgICAgICAgICAgIHRoaXMucGFyZW50Q2F0ZWdvcnlOYW1lID0gcGFyZW50Q2F0ZWdvcnkuZGVwdG5hbWU7CiAgICAgICAgICAgICAgICB0aGlzLnBhcmVudFJvbGVJZCA9IHBhcmVudENhdGVnb3J5LnJvbGVJZDsKICAgICAgICAgICAgICAgIHRoaXMucGFyZW50Um9sZU5hbWUgPSBwYXJlbnRDYXRlZ29yeS5yb2xlTmFtZTsKICAgICAgICAgICAgfQoKICAgICAgICAgICAgdGhpcy5mb3JtID0gewogICAgICAgICAgICAgICAgaWQ6IHN1YmNhdGVnb3J5Um93LmNsYXNzZXNJZCwKICAgICAgICAgICAgICAgIG5hbWU6IHN1YmNhdGVnb3J5Um93LmNsYXNzZXNuYW1lCiAgICAgICAgICAgIH07CgogICAgICAgICAgICB0aGlzLmRyYXdlclZpc2libGUgPSB0cnVlOwogICAgICAgIH0sCgogICAgICAgIC8vIOWIoOmZpOexu+WIqwogICAgICAgIGhhbmRsZURlbGV0ZUNhdGVnb3J5KGNhdGVnb3J5Um93KSB7CiAgICAgICAgICAgIHRoaXMuJGNvbmZpcm0oJ+ehruiupOWIoOmZpOivpee7tOS/ruexu+WIq+WQl++8n+WIoOmZpOWQjuWFtuS4i+eahOaJgOaciee7tOS/ruWtkOexu+WIq+S5n+Wwhuiiq+WIoOmZpOOAgicsICfmj5DnpLonLCB7CiAgICAgICAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICAgICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICAgICAgICAgIHRoaXMuJGRlbCgiL2RlcHQvIiArIGNhdGVnb3J5Um93LmRlcHRzSWQpCiAgICAgICAgICAgICAgICAgICAgLnRoZW4oKHJlcykgPT4gewogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOwogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLm5ld0xpc3QoKTsKICAgICAgICAgICAgICAgICAgICB9KQogICAgICAgICAgICAgICAgICAgIC5jYXRjaChlcnJvciA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuWIoOmZpOWksei0pe+8miIgKyBlcnJvci5tZXNzYWdlKTsKICAgICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgfSk7CiAgICAgICAgfSwKCiAgICAgICAgLy8g5Yig6Zmk5a2Q57G75YirCiAgICAgICAgaGFuZGxlRGVsZXRlU3ViY2F0ZWdvcnkoc3ViY2F0ZWdvcnlSb3cpIHsKICAgICAgICAgICAgdGhpcy4kY29uZmlybSgn56Gu6K6k5Yig6Zmk6K+l57u05L+u5a2Q57G75Yir5ZCX77yfJywgJ+aPkOekuicsIHsKICAgICAgICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgICAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgICAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgICAgICAgICAgdGhpcy4kZGVsKCIvY2xhc3MvIiArIHN1YmNhdGVnb3J5Um93LmNsYXNzZXNJZCkKICAgICAgICAgICAgICAgICAgICAudGhlbigocmVzKSA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7CiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMubmV3TGlzdCgpOwogICAgICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgICAgICAgLmNhdGNoKGVycm9yID0+IHsKICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5Yig6Zmk5aSx6LSl77yaIiArIGVycm9yLm1lc3NhZ2UpOwogICAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9KTsKICAgICAgICB9LAoKICAgICAgICAvLyDmj5DkuqTnu7Tkv67nsbvliKvooajljZUKICAgICAgICBzdWJtaXRDYXRlZ29yeUZvcm0oKSB7CiAgICAgICAgICAgIHRoaXMuJHJlZnNbImNhdGVnb3J5Rm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsKICAgICAgICAgICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgICAgICAgICAgIHRoaXMuc3VibWl0Q2F0ZWdvcnkoKTsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSk7CiAgICAgICAgfSwKCiAgICAgICAgLy8g5o+Q5Lqk5a2Q57G75Yir6KGo5Y2VCiAgICAgICAgc3VibWl0Rm9ybSgpIHsKICAgICAgICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsKICAgICAgICAgICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgICAgICAgICAgIHRoaXMuc3VibWl0U3ViY2F0ZWdvcnkoKTsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSk7CiAgICAgICAgfSwKCiAgICAgICAgLy8g5o+Q5Lqk57u05L+u57G75YirCiAgICAgICAgc3VibWl0Q2F0ZWdvcnkoKSB7CiAgICAgICAgICAgIGlmICh0aGlzLmlzQ2F0ZWdvcnlFZGl0KSB7CiAgICAgICAgICAgICAgICAvLyDkv67mlLnnu7Tkv67nsbvliKsKICAgICAgICAgICAgICAgIHRoaXMuJHB1dCgiL2RlcHQiLCB7CiAgICAgICAgICAgICAgICAgICAgaWQ6IHRoaXMuY2F0ZWdvcnlGb3JtLmlkLAogICAgICAgICAgICAgICAgICAgIG5hbWU6IHRoaXMuY2F0ZWdvcnlGb3JtLm5hbWUsCiAgICAgICAgICAgICAgICAgICAgcm9sZUlkOiB0aGlzLmNhdGVnb3J5Rm9ybS5yb2xlSWQKICAgICAgICAgICAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5L+u5pS557u05L+u57G75Yir5oiQ5YqfIik7CiAgICAgICAgICAgICAgICAgICAgdGhpcy5jYXRlZ29yeURpYWxvZ1Zpc2libGUgPSBmYWxzZTsKICAgICAgICAgICAgICAgICAgICB0aGlzLm5ld0xpc3QoKTsKICAgICAgICAgICAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsKICAgICAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLkv67mlLnlpLHotKXvvJoiICsgZXJyb3IubWVzc2FnZSk7CiAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgIC8vIOa3u+WKoOe7tOS/ruexu+WIqwogICAgICAgICAgICAgICAgdGhpcy4kcG9zdCgiL2RlcHQiLCB7CiAgICAgICAgICAgICAgICAgICAgbmFtZTogdGhpcy5jYXRlZ29yeUZvcm0ubmFtZSwKICAgICAgICAgICAgICAgICAgICByb2xlSWQ6IHRoaXMuY2F0ZWdvcnlGb3JtLnJvbGVJZAogICAgICAgICAgICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLmt7vliqDnu7Tkv67nsbvliKvmiJDlip8iKTsKICAgICAgICAgICAgICAgICAgICB0aGlzLmNhdGVnb3J5RGlhbG9nVmlzaWJsZSA9IGZhbHNlOwogICAgICAgICAgICAgICAgICAgIHRoaXMubmV3TGlzdCgpOwogICAgICAgICAgICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gewogICAgICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIua3u+WKoOWksei0pe+8miIgKyBlcnJvci5tZXNzYWdlKTsKICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9CiAgICAgICAgfSwKCiAgICAgICAgLy8g5o+Q5Lqk5a2Q57G75YirCiAgICAgICAgc3VibWl0U3ViY2F0ZWdvcnkoKSB7CiAgICAgICAgICAgIGlmICh0aGlzLmlzRWRpdCkgewogICAgICAgICAgICAgICAgLy8g5L+u5pS55a2Q57G75YirCiAgICAgICAgICAgICAgICB0aGlzLiRwdXQoIi9jbGFzcyIsIHsKICAgICAgICAgICAgICAgICAgICBpZDogdGhpcy5mb3JtLmlkLAogICAgICAgICAgICAgICAgICAgIG5hbWU6IHRoaXMuZm9ybS5uYW1lLAogICAgICAgICAgICAgICAgICAgIHJvbGVJZDogdGhpcy5wYXJlbnRSb2xlSWQsCiAgICAgICAgICAgICAgICAgICAgZGVwdElkOiB0aGlzLnBhcmVudENhdGVnb3J5SWQKICAgICAgICAgICAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5L+u5pS557u05L+u5a2Q57G75Yir5oiQ5YqfIik7CiAgICAgICAgICAgICAgICAgICAgdGhpcy5kcmF3ZXJWaXNpYmxlID0gZmFsc2U7CiAgICAgICAgICAgICAgICAgICAgdGhpcy5uZXdMaXN0KCk7CiAgICAgICAgICAgICAgICB9KS5jYXRjaChlcnJvciA9PiB7CiAgICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5L+u5pS55aSx6LSl77yaIiArIGVycm9yLm1lc3NhZ2UpOwogICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAvLyDmt7vliqDlrZDnsbvliKsKICAgICAgICAgICAgICAgIHRoaXMuJHBvc3QoIi9jbGFzcyIsIHsKICAgICAgICAgICAgICAgICAgICBuYW1lOiB0aGlzLmZvcm0ubmFtZSwKICAgICAgICAgICAgICAgICAgICByb2xlSWQ6IHRoaXMucGFyZW50Um9sZUlkLAogICAgICAgICAgICAgICAgICAgIGRlcHRJZDogdGhpcy5wYXJlbnRDYXRlZ29yeUlkCiAgICAgICAgICAgICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIua3u+WKoOe7tOS/ruWtkOexu+WIq+aIkOWKnyIpOwogICAgICAgICAgICAgICAgICAgIHRoaXMuZHJhd2VyVmlzaWJsZSA9IGZhbHNlOwogICAgICAgICAgICAgICAgICAgIHRoaXMubmV3TGlzdCgpOwogICAgICAgICAgICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gewogICAgICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIua3u+WKoOWksei0pe+8miIgKyBlcnJvci5tZXNzYWdlKTsKICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9CiAgICAgICAgfSwKCiAgICAgICAgLy8g5YWz6Zet57u05L+u57G75Yir5by556qXCiAgICAgICAgaGFuZGxlQ2F0ZWdvcnlEaWFsb2dDbG9zZSgpIHsKICAgICAgICAgICAgdGhpcy5jYXRlZ29yeURpYWxvZ1Zpc2libGUgPSBmYWxzZTsKICAgICAgICAgICAgdGhpcy5yZXNldENhdGVnb3J5Rm9ybSgpOwogICAgICAgIH0sCgogICAgICAgIC8vIOWFs+mXreWtkOexu+WIq+aKveWxiQogICAgICAgIGhhbmRsZURyYXdlckNsb3NlKCkgewogICAgICAgICAgICB0aGlzLmRyYXdlclZpc2libGUgPSBmYWxzZTsKICAgICAgICAgICAgdGhpcy5yZXNldEZvcm0oKTsKICAgICAgICB9LAoKICAgICAgICAvLyDojrflj5bmlbDmja7liJfooagKICAgICAgICBuZXdMaXN0KCl7CiAgICAgICAgICAgIHRoaXMuJGdldCgnL3JvbGUnKQogICAgICAgICAgICAudGhlbihyZXMgPT4gewogICAgICAgICAgICAgICAgaWYgKHJlcy5kYXRhICYmIHJlcy5kYXRhLnJvbGUpIHsKICAgICAgICAgICAgICAgICAgICB0aGlzLnJvbGVzID0gcmVzLmRhdGEucm9sZTsKICAgICAgICAgICAgICAgICAgICAvLyDmj5Dlj5bop5LoibLpgInpobkKICAgICAgICAgICAgICAgICAgICB0aGlzLnJvbGVPcHRpb25zID0gcmVzLmRhdGEucm9sZS5tYXAocm9sZSA9PiAoewogICAgICAgICAgICAgICAgICAgICAgICBpZDogcm9sZS5pZCwKICAgICAgICAgICAgICAgICAgICAgICAgbmFtZTogcm9sZS5uYW1lCiAgICAgICAgICAgICAgICAgICAgfSkpOwogICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgICB0aGlzLnJvbGVzID0gW107CiAgICAgICAgICAgICAgICAgICAgdGhpcy5yb2xlT3B0aW9ucyA9IFtdOwogICAgICAgICAgICAgICAgICAgIGNvbnNvbGUud2Fybign6I635Y+W6KeS6Imy5pWw5o2u5aSx6LSl77yM6L+U5Zue5pWw5o2u5qC85byP5LiN5q2j56GuJyk7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0pCiAgICAgICAgICAgIC5jYXRjaChlcnJvciA9PiB7CiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bop5LoibLliJfooajlpLHotKU6JywgZXJyb3IpOwogICAgICAgICAgICAgICAgdGhpcy5yb2xlcyA9IFtdOwogICAgICAgICAgICAgICAgdGhpcy5yb2xlT3B0aW9ucyA9IFtdOwogICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6I635Y+W6KeS6Imy5YiX6KGo5aSx6LSl77yM6K+356iN5ZCO6YeN6K+VJyk7CiAgICAgICAgICAgIH0pOwogICAgICAgIH0sCgogICAgICAgIC8vIOe7tOS/ruexu+WIq+ihqOWNlemHjee9rgogICAgICAgIHJlc2V0Q2F0ZWdvcnlGb3JtKCkgewogICAgICAgICAgICB0aGlzLmNhdGVnb3J5Rm9ybSA9IHsKICAgICAgICAgICAgICAgIGlkOiBudWxsLAogICAgICAgICAgICAgICAgbmFtZTogJycsCiAgICAgICAgICAgICAgICByb2xlSWQ6IG51bGwKICAgICAgICAgICAgfTsKCiAgICAgICAgICAgIGlmICh0aGlzLiRyZWZzLmNhdGVnb3J5Rm9ybSkgewogICAgICAgICAgICAgICAgdGhpcy4kcmVmcy5jYXRlZ29yeUZvcm0ucmVzZXRGaWVsZHMoKTsKICAgICAgICAgICAgfQogICAgICAgIH0sCgogICAgICAgIC8vIOWtkOexu+WIq+ihqOWNlemHjee9rgogICAgICAgIHJlc2V0Rm9ybSgpIHsKICAgICAgICAgICAgdGhpcy5mb3JtID0gewogICAgICAgICAgICAgICAgaWQ6IG51bGwsCiAgICAgICAgICAgICAgICBuYW1lOiAnJywKICAgICAgICAgICAgICAgIHJvbGVJZDogbnVsbCwKICAgICAgICAgICAgICAgIGRlcHRJZDogbnVsbAogICAgICAgICAgICB9OwogICAgICAgICAgICB0aGlzLnBhcmVudFJvbGVOYW1lID0gJyc7CiAgICAgICAgICAgIHRoaXMucGFyZW50Q2F0ZWdvcnlOYW1lID0gJyc7CiAgICAgICAgICAgIHRoaXMucGFyZW50Q2F0ZWdvcnlJZCA9IG51bGw7CiAgICAgICAgICAgIHRoaXMucGFyZW50Um9sZUlkID0gbnVsbDsKCiAgICAgICAgICAgIGlmICh0aGlzLiRyZWZzLmZvcm0pIHsKICAgICAgICAgICAgICAgIHRoaXMuJHJlZnMuZm9ybS5yZXNldEZpZWxkcygpOwogICAgICAgICAgICB9CiAgICAgICAgfSwKCgoKICAgICAgICAvLyDovoXliqnmlrnms5XvvJrmoLnmja7lrZDnsbvliKtJROafpeaJvueItue6p+e7tOS/ruexu+WIqwogICAgICAgIGZpbmRQYXJlbnRDYXRlZ29yeShjbGFzc0lkKSB7CiAgICAgICAgICAgIGZvciAobGV0IGRlcHQgb2YgdGhpcy5kYXRhTGlzdCkgewogICAgICAgICAgICAgICAgaWYgKGRlcHQuY2hpbGRyZW4pIHsKICAgICAgICAgICAgICAgICAgICBmb3IgKGxldCBjaGlsZCBvZiBkZXB0LmNoaWxkcmVuKSB7CiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChjaGlsZC5jbGFzc2VzSWQgPT09IGNsYXNzSWQpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBkZXB0OwogICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICAgIHJldHVybiBudWxsOwogICAgICAgIH0sCiAgICB9LAoKICAgIGNyZWF0ZWQoKSB7CiAgICAgICAgdGhpcy5uZXdMaXN0KCk7CiAgICB9LAoKICAgIGNvbXB1dGVkOnsKICAgICAgICBkYXRhTGlzdCgpewogICAgICAgICAgICBpZiAoIXRoaXMucm9sZXMgfHwgIUFycmF5LmlzQXJyYXkodGhpcy5yb2xlcykpIHsKICAgICAgICAgICAgICAgIHJldHVybiBbXTsKICAgICAgICAgICAgfQoKICAgICAgICAgICAgbGV0IGFsbERlcHRzID0gW107CgogICAgICAgICAgICAvLyDpgY3ljobmiYDmnInop5LoibLvvIzmlLbpm4bmiYDmnInnmoTnu7Tkv67nsbvliKsKICAgICAgICAgICAgdGhpcy5yb2xlcy5mb3JFYWNoKHJvbGUgPT4gewogICAgICAgICAgICAgICAgaWYgKCFyb2xlIHx8ICFyb2xlLmRlcHRzKSByZXR1cm47CgogICAgICAgICAgICAgICAgcm9sZS5kZXB0cy5mb3JFYWNoKGRlcHQgPT4gewogICAgICAgICAgICAgICAgICAgIGlmICghZGVwdCkgcmV0dXJuOwoKICAgICAgICAgICAgICAgICAgICBsZXQgY2hpbGRyZW4gPSBbXTsKICAgICAgICAgICAgICAgICAgICBjb25zdCBkZXB0Q2xhc3NlcyA9IGRlcHRbJ2NsYXNzZXMnXSB8fCBbXTsKCiAgICAgICAgICAgICAgICAgICAgLy8g5re75Yqg5a2Q57G75YirCiAgICAgICAgICAgICAgICAgICAgZGVwdENsYXNzZXMuZm9yRWFjaChjbGFzc0l0ZW0gPT4gewogICAgICAgICAgICAgICAgICAgICAgICBpZiAoIWNsYXNzSXRlbSkgcmV0dXJuOwoKICAgICAgICAgICAgICAgICAgICAgICAgY2hpbGRyZW4ucHVzaCh7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc2VzSWQ6IGNsYXNzSXRlbS5pZCwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzZXNuYW1lOiBjbGFzc0l0ZW0ubmFtZSB8fCAnJywKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHV1aWQ6IHRoaXMuZ3VpZDIoKQogICAgICAgICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgICAgICB9KTsKCiAgICAgICAgICAgICAgICAgICAgLy8g5re75Yqg57u05L+u57G75YirCiAgICAgICAgICAgICAgICAgICAgYWxsRGVwdHMucHVzaCh7CiAgICAgICAgICAgICAgICAgICAgICAgIGRlcHRzSWQ6IGRlcHQuaWQsCiAgICAgICAgICAgICAgICAgICAgICAgIGRlcHRuYW1lOiBkZXB0Lm5hbWUgfHwgJycsCiAgICAgICAgICAgICAgICAgICAgICAgIHJvbGVJZDogcm9sZS5pZCwgLy8g5L+d55WZ6KeS6ImySUTnlKjkuo7mk43kvZwKICAgICAgICAgICAgICAgICAgICAgICAgcm9sZU5hbWU6IHJvbGUubmFtZSwgLy8g5L+d55WZ6KeS6Imy5ZCN56ew55So5LqO6KGo5Y2V5pi+56S6CiAgICAgICAgICAgICAgICAgICAgICAgIGNoaWxkcmVuOiBjaGlsZHJlbiwKICAgICAgICAgICAgICAgICAgICAgICAgdXVpZDogdGhpcy5ndWlkMigpCiAgICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgfSk7CgogICAgICAgICAgICByZXR1cm4gYWxsRGVwdHM7CiAgICAgICAgfQogICAgfQp9Cg=="}, {"version": 3, "sources": ["Role.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+HA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "Role.vue", "sourceRoot": "src/views/admin/children", "sourcesContent": ["<template>\n    <div class=\"content\">\n        <!-- 顶部操作按钮 -->\n        <div class=\"top-actions\" style=\"margin-bottom: 20px;\">\n            <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"handleAddCategory\">添加维修类别</el-button>\n        </div>\n\n        <el-table\n                :data=\"dataList\"\n                style=\"width: 100%;margin-bottom: 20px;\"\n                row-key=\"uuid\"\n                border\n                :default-expand-all = 'false'\n                :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\">\n            <el-table-column\n                    prop=\"deptname\"\n                    label=\"维修类别\"\n                    sortable\n                    width=\"250\">\n            </el-table-column>\n            <el-table-column\n                    prop=\"classesname\"\n                    label=\"维修子类别\">\n            </el-table-column>\n\n            <el-table-column label=\"操作\" width=\"280\">\n                <template slot-scope=\"scope\">\n                    <!-- 维修类别级别：可以增加子类别、修改、删除 -->\n                    <template v-if=\"scope.row.deptsId\">\n                        <el-button\n                                icon=\"el-icon-plus\"\n                                size=\"mini\"\n                                type=\"primary\"\n                                @click=\"handleAddSubcategory(scope.row)\">增加维修子类别\n                        </el-button>\n                        <el-button\n                                icon=\"el-icon-edit\"\n                                size=\"mini\"\n                                @click=\"handleUpdateCategory(scope.row)\">修改\n                        </el-button>\n                        <el-button\n                                icon=\"el-icon-delete\"\n                                size=\"mini\"\n                                type=\"danger\"\n                                @click=\"handleDeleteCategory(scope.row)\">删除\n                        </el-button>\n                    </template>\n\n                    <!-- 维修子类别级别：可以修改、删除 -->\n                    <template v-else-if=\"scope.row.classesId\">\n                        <el-button\n                                icon=\"el-icon-edit\"\n                                size=\"mini\"\n                                @click=\"handleUpdateSubcategory(scope.row)\">修改\n                        </el-button>\n                        <el-button\n                                icon=\"el-icon-delete\"\n                                size=\"mini\"\n                                type=\"danger\"\n                                @click=\"handleDeleteSubcategory(scope.row)\">删除\n                        </el-button>\n                    </template>\n                </template>\n            </el-table-column>\n        </el-table>\n\n        <!-- 添加维修类别弹窗 -->\n        <el-dialog\n            :title=\"categoryDialogTitle\"\n            :visible.sync=\"categoryDialogVisible\"\n            width=\"500px\"\n            :before-close=\"handleCategoryDialogClose\">\n\n            <el-form ref=\"categoryForm\" :model=\"categoryForm\" :rules=\"categoryRules\" label-width=\"120px\">\n                <el-form-item label=\"所属角色\" prop=\"roleId\">\n                    <el-select v-model=\"categoryForm.roleId\" placeholder=\"请选择角色\" style=\"width: 100%;\" :disabled=\"isCategoryEdit\">\n                        <el-option\n                            v-for=\"role in roleOptions\"\n                            :key=\"role.id\"\n                            :label=\"role.name\"\n                            :value=\"role.id\">\n                        </el-option>\n                    </el-select>\n                </el-form-item>\n                <el-form-item label=\"维修类别名称\" prop=\"name\">\n                    <el-input v-model=\"categoryForm.name\" placeholder=\"请输入维修类别名称\" />\n                </el-form-item>\n            </el-form>\n\n            <span slot=\"footer\" class=\"dialog-footer\">\n                <el-button @click=\"handleCategoryDialogClose\">取 消</el-button>\n                <el-button type=\"primary\" @click=\"submitCategoryForm\">确 定</el-button>\n            </span>\n        </el-dialog>\n\n        <!-- 抽屉式表单（仅用于子类别） -->\n        <el-drawer\n            :title=\"drawerTitle\"\n            :visible.sync=\"drawerVisible\"\n            direction=\"rtl\"\n            size=\"400px\"\n            :before-close=\"handleDrawerClose\">\n\n            <div style=\"padding: 20px;\">\n                <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\n                    <!-- 子类别表单 -->\n                    <el-form-item label=\"所属角色\">\n                        <el-input :value=\"parentRoleName\" disabled />\n                    </el-form-item>\n                    <el-form-item label=\"所属维修类别\">\n                        <el-input :value=\"parentCategoryName\" disabled />\n                    </el-form-item>\n                    <el-form-item label=\"维修子类别名称\" prop=\"name\">\n                        <el-input v-model=\"form.name\" placeholder=\"请输入维修子类别名称\" />\n                    </el-form-item>\n                </el-form>\n\n                <div class=\"drawer-footer\">\n                    <el-button @click=\"handleDrawerClose\">取 消</el-button>\n                    <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n                </div>\n            </div>\n        </el-drawer>\n    </div>\n</template>\n\n<script>\n    export default {\n        name: \"Role\",\n        data() {\n            return {\n                roles: [],\n                roleOptions: [], // 角色选项列表\n\n                // 维修类别弹窗相关\n                categoryDialogVisible: false,\n                categoryDialogTitle: '',\n                isCategoryEdit: false, // 是否为编辑模式\n\n                // 维修类别表单参数\n                categoryForm: {\n                    id: null,\n                    name: '',\n                    roleId: null\n                },\n\n                // 维修类别表单校验\n                categoryRules: {\n                    name: [\n                        { required: true, message: '请输入维修类别名称', trigger: 'blur' }\n                    ],\n                    roleId: [\n                        { required: true, message: '请选择角色', trigger: 'change' }\n                    ]\n                },\n\n                // 抽屉相关（仅用于子类别）\n                drawerVisible: false,\n                drawerTitle: '',\n                isEdit: false, // 是否为编辑模式\n\n                // 子类别表单参数\n                form: {\n                    id: null,\n                    name: '',\n                    roleId: null,\n                    deptId: null\n                },\n\n                // 父级信息（用于子类别表单显示）\n                parentRoleName: '',\n                parentCategoryName: '',\n                parentCategoryId: null,\n                parentRoleId: null,\n\n                // 子类别表单校验\n                rules: {\n                    name: [\n                        { required: true, message: '请输入维修子类别名称', trigger: 'blur' }\n                    ]\n                },\n            }\n        },\n        methods: {\n            guid2() {\n                function S4() {\n                    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);\n                }\n                return (S4() + S4() + \"-\" + S4() + \"-\" + S4() + \"-\" + S4() + \"-\" + S4() + S4() + S4());\n            },\n\n            //-------新的方法---------\n\n            // 添加维修类别\n            handleAddCategory() {\n                this.resetCategoryForm();\n                this.isCategoryEdit = false;\n                this.categoryDialogTitle = '添加维修类别';\n                this.categoryDialogVisible = true;\n            },\n\n            // 添加子类别\n            handleAddSubcategory(categoryRow) {\n                this.resetForm();\n                this.formType = 'subcategory';\n                this.isEdit = false;\n                this.drawerTitle = '添加维修子类别';\n\n                // 设置父级信息\n                this.parentCategoryId = categoryRow.deptsId;\n                this.parentCategoryName = categoryRow.deptname;\n                this.parentRoleId = categoryRow.roleId;\n                this.parentRoleName = categoryRow.roleName;\n\n                this.drawerVisible = true;\n            },\n\n            // 修改维修类别\n            handleUpdateCategory(categoryRow) {\n                this.isCategoryEdit = true;\n                this.categoryDialogTitle = '修改维修类别';\n\n                this.categoryForm = {\n                    id: categoryRow.deptsId,\n                    name: categoryRow.deptname,\n                    roleId: categoryRow.roleId\n                };\n\n                this.categoryDialogVisible = true;\n            },\n\n            // 修改维修子类别\n            handleUpdateSubcategory(subcategoryRow) {\n                this.isEdit = true;\n                this.drawerTitle = '修改维修子类别';\n\n                // 从父级数据中获取信息\n                const parentCategory = this.findParentCategory(subcategoryRow.classesId);\n                if (parentCategory) {\n                    this.parentCategoryId = parentCategory.deptsId;\n                    this.parentCategoryName = parentCategory.deptname;\n                    this.parentRoleId = parentCategory.roleId;\n                    this.parentRoleName = parentCategory.roleName;\n                }\n\n                this.form = {\n                    id: subcategoryRow.classesId,\n                    name: subcategoryRow.classesname\n                };\n\n                this.drawerVisible = true;\n            },\n\n            // 删除类别\n            handleDeleteCategory(categoryRow) {\n                this.$confirm('确认删除该维修类别吗？删除后其下的所有维修子类别也将被删除。', '提示', {\n                    confirmButtonText: '确定',\n                    cancelButtonText: '取消',\n                    type: 'warning'\n                }).then(() => {\n                    this.$del(\"/dept/\" + categoryRow.deptsId)\n                        .then((res) => {\n                            this.$message.success(\"删除成功\");\n                            this.newList();\n                        })\n                        .catch(error => {\n                            this.$message.error(\"删除失败：\" + error.message);\n                        });\n                });\n            },\n\n            // 删除子类别\n            handleDeleteSubcategory(subcategoryRow) {\n                this.$confirm('确认删除该维修子类别吗？', '提示', {\n                    confirmButtonText: '确定',\n                    cancelButtonText: '取消',\n                    type: 'warning'\n                }).then(() => {\n                    this.$del(\"/class/\" + subcategoryRow.classesId)\n                        .then((res) => {\n                            this.$message.success(\"删除成功\");\n                            this.newList();\n                        })\n                        .catch(error => {\n                            this.$message.error(\"删除失败：\" + error.message);\n                        });\n                });\n            },\n\n            // 提交维修类别表单\n            submitCategoryForm() {\n                this.$refs[\"categoryForm\"].validate(valid => {\n                    if (valid) {\n                        this.submitCategory();\n                    }\n                });\n            },\n\n            // 提交子类别表单\n            submitForm() {\n                this.$refs[\"form\"].validate(valid => {\n                    if (valid) {\n                        this.submitSubcategory();\n                    }\n                });\n            },\n\n            // 提交维修类别\n            submitCategory() {\n                if (this.isCategoryEdit) {\n                    // 修改维修类别\n                    this.$put(\"/dept\", {\n                        id: this.categoryForm.id,\n                        name: this.categoryForm.name,\n                        roleId: this.categoryForm.roleId\n                    }).then(() => {\n                        this.$message.success(\"修改维修类别成功\");\n                        this.categoryDialogVisible = false;\n                        this.newList();\n                    }).catch(error => {\n                        this.$message.error(\"修改失败：\" + error.message);\n                    });\n                } else {\n                    // 添加维修类别\n                    this.$post(\"/dept\", {\n                        name: this.categoryForm.name,\n                        roleId: this.categoryForm.roleId\n                    }).then(() => {\n                        this.$message.success(\"添加维修类别成功\");\n                        this.categoryDialogVisible = false;\n                        this.newList();\n                    }).catch(error => {\n                        this.$message.error(\"添加失败：\" + error.message);\n                    });\n                }\n            },\n\n            // 提交子类别\n            submitSubcategory() {\n                if (this.isEdit) {\n                    // 修改子类别\n                    this.$put(\"/class\", {\n                        id: this.form.id,\n                        name: this.form.name,\n                        roleId: this.parentRoleId,\n                        deptId: this.parentCategoryId\n                    }).then(() => {\n                        this.$message.success(\"修改维修子类别成功\");\n                        this.drawerVisible = false;\n                        this.newList();\n                    }).catch(error => {\n                        this.$message.error(\"修改失败：\" + error.message);\n                    });\n                } else {\n                    // 添加子类别\n                    this.$post(\"/class\", {\n                        name: this.form.name,\n                        roleId: this.parentRoleId,\n                        deptId: this.parentCategoryId\n                    }).then(() => {\n                        this.$message.success(\"添加维修子类别成功\");\n                        this.drawerVisible = false;\n                        this.newList();\n                    }).catch(error => {\n                        this.$message.error(\"添加失败：\" + error.message);\n                    });\n                }\n            },\n\n            // 关闭维修类别弹窗\n            handleCategoryDialogClose() {\n                this.categoryDialogVisible = false;\n                this.resetCategoryForm();\n            },\n\n            // 关闭子类别抽屉\n            handleDrawerClose() {\n                this.drawerVisible = false;\n                this.resetForm();\n            },\n\n            // 获取数据列表\n            newList(){\n                this.$get('/role')\n                .then(res => {\n                    if (res.data && res.data.role) {\n                        this.roles = res.data.role;\n                        // 提取角色选项\n                        this.roleOptions = res.data.role.map(role => ({\n                            id: role.id,\n                            name: role.name\n                        }));\n                    } else {\n                        this.roles = [];\n                        this.roleOptions = [];\n                        console.warn('获取角色数据失败，返回数据格式不正确');\n                    }\n                })\n                .catch(error => {\n                    console.error('获取角色列表失败:', error);\n                    this.roles = [];\n                    this.roleOptions = [];\n                    this.$message.error('获取角色列表失败，请稍后重试');\n                });\n            },\n\n            // 维修类别表单重置\n            resetCategoryForm() {\n                this.categoryForm = {\n                    id: null,\n                    name: '',\n                    roleId: null\n                };\n\n                if (this.$refs.categoryForm) {\n                    this.$refs.categoryForm.resetFields();\n                }\n            },\n\n            // 子类别表单重置\n            resetForm() {\n                this.form = {\n                    id: null,\n                    name: '',\n                    roleId: null,\n                    deptId: null\n                };\n                this.parentRoleName = '';\n                this.parentCategoryName = '';\n                this.parentCategoryId = null;\n                this.parentRoleId = null;\n\n                if (this.$refs.form) {\n                    this.$refs.form.resetFields();\n                }\n            },\n\n\n\n            // 辅助方法：根据子类别ID查找父级维修类别\n            findParentCategory(classId) {\n                for (let dept of this.dataList) {\n                    if (dept.children) {\n                        for (let child of dept.children) {\n                            if (child.classesId === classId) {\n                                return dept;\n                            }\n                        }\n                    }\n                }\n                return null;\n            },\n        },\n\n        created() {\n            this.newList();\n        },\n\n        computed:{\n            dataList(){\n                if (!this.roles || !Array.isArray(this.roles)) {\n                    return [];\n                }\n\n                let allDepts = [];\n\n                // 遍历所有角色，收集所有的维修类别\n                this.roles.forEach(role => {\n                    if (!role || !role.depts) return;\n\n                    role.depts.forEach(dept => {\n                        if (!dept) return;\n\n                        let children = [];\n                        const deptClasses = dept['classes'] || [];\n\n                        // 添加子类别\n                        deptClasses.forEach(classItem => {\n                            if (!classItem) return;\n\n                            children.push({\n                                classesId: classItem.id,\n                                classesname: classItem.name || '',\n                                uuid: this.guid2()\n                            });\n                        });\n\n                        // 添加维修类别\n                        allDepts.push({\n                            deptsId: dept.id,\n                            deptname: dept.name || '',\n                            roleId: role.id, // 保留角色ID用于操作\n                            roleName: role.name, // 保留角色名称用于表单显示\n                            children: children,\n                            uuid: this.guid2()\n                        });\n                    });\n                });\n\n                return allDepts;\n            }\n        }\n    }\n</script>\n\n<style scoped lang=\"less\">\n    .content {\n        padding: 0 1%;\n    }\n\n    .top-actions {\n        text-align: left;\n        margin-bottom: 20px;\n    }\n\n    .drawer-footer {\n        text-align: right;\n        margin-top: 30px;\n        padding-top: 20px;\n        border-top: 1px solid #e8e8e8;\n    }\n\n    .drawer-footer .el-button {\n        margin-left: 10px;\n    }\n\n    // 操作按钮样式优化\n    .el-table .el-button--mini {\n        margin-right: 5px;\n        margin-bottom: 5px;\n    }\n\n    // 角色不可操作提示样式\n    .el-table .role-no-operation {\n        color: #909399;\n        font-size: 12px;\n        font-style: italic;\n    }\n</style>\n"]}]}