<template>
    <div class="content">
<!--        搜索栏-->
        <!-- <div class="center">
            <el-input placeholder="请输入内容" v-model="input" class="input-with-select" @keydown.enter.native="clickSearch">
                <el-select v-model="select" slot="prepend" placeholder="请选择" value="1" @change="inquiry">
                    <el-option value="entire" label="全部"></el-option>
                    <el-option :value="item.id" v-for="item in roles" :label="item.name"></el-option>
                </el-select>
                <el-button slot="append" icon="el-icon-search" @click="clickSearch"></el-button>
            </el-input>
        </div> -->
        <div class="bottom">
            <el-table
                    :data="tasks"
                    :resizable="false"
                    style="width: 100%  ">
                <el-table-column
                        prop="publish.username"
                        label="发布人"
                        min-width="140">
                </el-table-column>
                <el-table-column
                        label="维修员"
                        min-width="140">
                    <template slot-scope="scope">
                        {{scope.row.accept ? scope.row.accept.username : '暂无服务'}}
                    </template>
                </el-table-column>
                <el-table-column
                        prop="reward"
                        label="任务金额"
                        min-width="110">
                </el-table-column>
                <el-table-column
                        label="任务所在类别"
                        min-width="120">
                    <template slot-scope="scope">
                        {{scope.row.dept ? scope.row.dept.name : '未分类'}}
                    </template>
                </el-table-column>
                <el-table-column
                        label="任务子类别"
                        min-width="120">
                    <template slot-scope="scope">
                        {{scope.row.type ? scope.row.type.name : '未分类'}}
                    </template>
                </el-table-column>
                <el-table-column
                        label="地址"
                        min-width="180">
                    <template slot-scope="scope">
                        <span v-if="scope.row.province">
                            {{scope.row.province}} {{scope.row.city}} {{scope.row.district}}
                        </span>
                        <span v-else>未设置</span>
                    </template>
                </el-table-column>
                <el-table-column
                        label="详细地址"
                        min-width="180">
                    <template slot-scope="scope">
                        {{scope.row.address || '未设置'}}
                    </template>
                </el-table-column>
                <el-table-column
                        prop="taskTitle"
                        label="标题"
                        min-width="150">
                </el-table-column>
                <el-table-column
                        label="发布时间"
                        min-width="140">
                    <template slot-scope="scope">
                        {{scope.row.createTime ? transform(scope.row.createTime) : '暂无时间'}}
                    </template>
                </el-table-column>
                <el-table-column
                        label="服务时间"
                        min-width="140">
                    <template slot-scope="scope">
                        {{scope.row.orderTime ? transform(scope.row.orderTime) : '暂无时间'}}
                    </template>
                </el-table-column>
                <el-table-column
                        prop="balance"
                        label="完成时间"
                        min-width="140">
                    <template slot-scope="scope">
                        {{scope.row.endTime ? transform(scope.row.endTime) : '暂无时间'}}
                    </template>
                </el-table-column>
                <el-table-column
                        prop="state"
                        label="任务状态"
                        min-width="90">
                    <template slot-scope="scope">
                        <el-tag :type="getStateType(scope.row.state)">
                            {{ getStateText(scope.row.state) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column
                        label="操作"
                        width="180">
                    <template slot-scope="scope">
                        <el-button-group>
                            <!-- <el-button
                                    size="mini"
                                    :type="scope.row.isTop ? 'warning' : 'success'"
                                    @click="handleTopToggle(scope.row)">
                                {{ scope.row.isTop ? '取消置顶' : '置顶' }}
                            </el-button> -->
                            <el-button
                                    size="mini"
                                    type="danger"
                                    @click="handleDelete(scope.row)">
                                删除
                            </el-button>
                        </el-button-group>
                    </template>
                </el-table-column>
            </el-table>

        </div>
    </div>
</template>

<script>
    import {formatDate} from '@/util/date';
    export default {
        name: "Task",
        methods: {
            inquiry(){
                if (this.select == 'entire'){
                    this.newList();
                }else {
                    this.$post("/task/api/findTaskByRoleId",{"id":this.select})
                    .then(res => {
                        this.tasks = res.data.tasks
                    })
                }
            },
            clickSearch() {
                if(this.input == '已完成'){

                }
            },

            getStateType(state) {
                const types = {
                    0: 'info',    // 待接单
                    1: 'warning', // 进行中
                    2: 'success'  // 已完成
                }
                return types[state] || 'info'
            },
            getStateText(state) {
                const texts = {
                    0: '待接单',
                    1: '进行中',
                    2: '已完成'
                }
                return texts[state] || '未知'
            },
            transform(time) {
                let date = new Date(time);
                return formatDate(date, 'yyyy-MM-dd hh:mm');
            },
            handleDelete(row) {
                this.$confirm('确认删除该任务?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$del(`/task/${row.id}`).then(res => {
                        if (res.data.status) {
                            this.$message.success('删除成功');
                            this.newList();
                        } else {
                            this.$message.error(res.data.msg || '删除失败');
                        }
                    })
                }).catch(() => {})
            },
            handleTopToggle(row) {
                const action = row.isTop ? 'cancelTop' : 'top';
                const message = row.isTop ? '取消置顶' : '置顶';

                this.$put(`/task/${action}/${row.id}`).then(res => {
                    if (res.data.status) {
                        this.$message.success(`${message}成功`);
                        row.isTop = !row.isTop;
                        this.newList();
                    } else {
                        this.$message.error(res.data.msg || `${message}失败`);
                    }
                }).catch(err => {
                    console.error(`${message}失败:`, err);
                    this.$message.error(`${message}失败，请稍后重试`);
                });
            },
            newList() {
                this.$get("/task")
                .then((rs) => {
                    if (rs.data.status) {
                        this.tasks = rs.data.task.sort((a, b) => {
                            if (a.isTop && !b.isTop) return -1;
                            if (!a.isTop && b.isTop) return 1;
                            return new Date(b.createTime) - new Date(a.createTime);
                        });
                    } else {
                        this.$message.error(rs.data.msg || '获取任务列表失败');
                    }
                })
                .catch(err => {
                    console.error('获取任务列表失败:', err);
                    this.$message.error('获取任务列表失败，请稍后重试');
                });
            }
        },
        data() {
            return {
                //学校
                roles:[],
                tasks: [],
                ruleForm: {
                    id: 0,
                    state: true
                },
                options: [
                    {label: "正常", value: "0"},
                    {label: "禁用", value: "1"}

                ],
                input:"",
                select: 'entire'
            }
        },
        created() {
            this.newList();
            this.$get("role")
            .then(res => {
                this.roles = res.data.role
            })
        },

        filters: {
            formatDate(time) {
                let date = new Date(time);
                return formatDate(date, 'yyyy-MM-dd hh:mm');
            }
        }

    }
</script>

<style scoped lang="less">
    .content {
        padding: 0 1%;

    }

    .center {
        width: 80%;
        margin-bottom: 30px;
    }

    /deep/ .el-select .el-input {
        width: 200px;
    }

    /deep/ .input-with-select .el-input-group__prepend {
        background-color: #fff;
    }



    .form {
        margin: 0 22px;
    }

    .el-button-group {
        .el-button {
            margin-left: 0;
            margin-right: 0;

            &:first-child {
                border-right: 1px solid rgba(255, 255, 255, 0.5);
            }
        }
    }
</style>