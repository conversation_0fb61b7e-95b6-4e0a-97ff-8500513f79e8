<template>
  <div class="recharge-container">
    <el-card class="recharge-card">
      <div slot="header" class="card-header">
        <span class="card-title">账户充值</span>
        <span class="balance-info">当前余额：¥{{ userBalance }}</span>
      </div>

      <div class="recharge-content">
        <!-- 快捷金额选择 -->
        <div class="quick-amount-section">
          <h3>选择充值金额</h3>
          <div class="amount-buttons">
            <el-button
              v-for="amount in quickAmounts"
              :key="amount"
              :type="selectedAmount === amount ? 'primary' : 'default'"
              @click="selectAmount(amount)"
              class="amount-btn">
              ¥{{ amount }}
            </el-button>
          </div>
        </div>

        <!-- 自定义金额输入 -->
        <div class="custom-amount-section">
          <h3>或输入自定义金额</h3>
          <el-input
            v-model="customAmount"
            placeholder="请输入充值金额"
            type="number"
            :min="0.01"
            :max="10000"
            @input="onCustomAmountChange"
            class="amount-input">
            <template slot="prepend">¥</template>
          </el-input>
          <div class="amount-tips">
            <span>最低充值金额：¥0.01</span>
            <span>最高充值金额：¥10,000</span>
          </div>
        </div>

        <!-- 支付方式选择 -->
        <div class="payment-method-section">
          <h3>选择支付方式</h3>
          <el-radio-group v-model="paymentMethod" class="payment-methods">
            <el-radio label="alipay" class="payment-radio">
              <div class="payment-option">
                <span>💰</span>
                <span>支付宝</span>
              </div>
            </el-radio>
          </el-radio-group>
        </div>

        <!-- 充值按钮 -->
        <div class="recharge-action">
          <el-button
            type="primary"
            size="large"
            :loading="loading"
            :disabled="!canPay"
            @click="handleRecharge"
            class="recharge-btn">
            {{ loading ? '正在创建订单...' : `立即充值 ¥${finalAmount}` }}
          </el-button>
        </div>

        <!-- 充值说明 -->
        <div class="recharge-notice">
          <h4>充值说明：</h4>
          <ul>
            <li>充值金额将实时到账，可用于平台内各项服务</li>
            <li>支持支付宝安全支付，资金安全有保障</li>
            <li>如有疑问，请联系客服</li>
          </ul>
        </div>
      </div>
    </el-card>

    <!-- 充值记录 -->
    <el-card class="record-card" style="margin-top: 20px;">
      <div slot="header">
        <span>充值记录</span>
        <el-button type="text" @click="refreshRecords" style="float: right;">刷新</el-button>
      </div>

      <el-table :data="rechargeRecords" style="width: 100%" v-loading="recordLoading">
        <el-table-column prop="orderNo" label="订单号" width="200"></el-table-column>
        <el-table-column prop="amount" label="充值金额" width="120">
          <template slot-scope="scope">
            ¥{{ scope.row.amount }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="paymentMethod" label="支付方式" width="100">
          <template slot-scope="scope">
            {{ scope.row.paymentMethod === 'alipay' ? '支付宝' : scope.row.paymentMethod }}
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180">
          <template slot-scope="scope">
            {{ formatDate(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="payTime" label="支付时间" width="180">
          <template slot-scope="scope">
            {{ scope.row.payTime ? formatDate(scope.row.payTime) : '-' }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'Recharge',
  data() {
    return {
      userBalance: 0,
      selectedAmount: null,
      customAmount: '',
      paymentMethod: 'alipay',
      loading: false,
      recordLoading: false,
      quickAmounts: [10, 20, 50, 100, 200, 500],
      rechargeRecords: []
    }
  },
  computed: {
    finalAmount() {
      if (this.customAmount && parseFloat(this.customAmount) > 0) {
        return parseFloat(this.customAmount).toFixed(2);
      }
      return this.selectedAmount || 0;
    },
    canPay() {
      return this.finalAmount > 0 && this.finalAmount >= 0.01 && this.finalAmount <= 10000;
    }
  },
  mounted() {
    this.loadUserInfo();
    this.loadRechargeRecords();
    this.checkPaymentResult();
  },
  methods: {
    async loadUserInfo() {
      try {
        // 从本地存储获取用户信息
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        if (!user.id) return;

        // 从服务器获取最新的用户信息
        const response = await this.$get(`/user/info/${user.id}`);
        if (response.data.status) {
          const updatedUser = response.data.user;
          this.userBalance = updatedUser.balance || 0;

          // 更新本地存储的用户信息
          localStorage.setItem('user', JSON.stringify(updatedUser));

          // 如果有全局状态管理，也更新全局状态
          if (this.$store && this.$store.commit) {
            this.$store.commit('user/setUser', updatedUser);
          }
        } else {
          // 如果获取失败，使用本地存储的信息
          this.userBalance = user.balance || 0;
        }
      } catch (error) {
        console.error('获取用户信息失败:', error);
        // 如果获取失败，使用本地存储的信息
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        this.userBalance = user.balance || 0;
      }
    },

    selectAmount(amount) {
      this.selectedAmount = amount;
      this.customAmount = '';
    },

    onCustomAmountChange() {
      this.selectedAmount = null;
    },

    async handleRecharge() {
      if (!this.canPay) {
        this.$message.warning('请输入有效的充值金额');
        return;
      }

      this.loading = true;

      try {
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        const paymentData = {
          userId: user.id,
          totalAmount: parseFloat(this.finalAmount),
          subject: '用户充值'
        };

        // 创建支付订单
        const response = await this.$post('/api/alipay/create', paymentData);

        if (response.data.status) {
          // 在新窗口中打开支付页面
          const payWindow = window.open('', '_blank');
          payWindow.document.write(response.data.payForm);
          payWindow.document.close();

          // 监听支付窗口关闭
          const checkClosed = setInterval(() => {
            if (payWindow.closed) {
              clearInterval(checkClosed);
              this.refreshRecords();
              this.loadUserInfo();
            }
          }, 1000);

        } else {
          this.$message.error(response.data.msg || '创建支付订单失败');
        }

      } catch (error) {
        console.error('创建支付订单失败:', error);
        this.$message.error('创建支付订单失败，请稍后重试');
      } finally {
        this.loading = false;
      }
    },

    async loadRechargeRecords() {
      try {
        this.recordLoading = true;
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        if (!user.id) return;

        const response = await this.$get('/recharge/records', {
          userId: user.id,
          page: 1,
          size: 10
        });

        if (response.data.status) {
          this.rechargeRecords = response.data.data.list || [];
        } else {
          this.$message.error(response.data.msg || '加载充值记录失败');
        }
      } catch (error) {
        console.error('加载充值记录失败:', error);
        this.$message.error('加载充值记录失败，请稍后重试');
      } finally {
        this.recordLoading = false;
      }
    },

    refreshRecords() {
      this.loadRechargeRecords();
    },

    checkPaymentResult() {
      // 检查URL参数，看是否是支付返回
      const urlParams = new URLSearchParams(window.location.search);
      const status = urlParams.get('status');
      const orderNo = urlParams.get('orderNo');

      if (status === 'success' && orderNo) {
        this.$message.success('支付成功！');
        this.refreshRecords();
        this.loadUserInfo();
      } else if (status === 'fail') {
        this.$message.error('支付失败，请重试');
      }
    },

    getStatusType(status) {
      const types = {
        0: 'warning', // 待支付
        1: 'success', // 支付成功
        2: 'danger',  // 支付失败
        3: 'info'     // 已取消
      };
      return types[status] || 'info';
    },

    getStatusText(status) {
      const texts = {
        0: '待支付',
        1: '支付成功',
        2: '支付失败',
        3: '已取消'
      };
      return texts[status] || '未知';
    },

    formatDate(dateStr) {
      if (!dateStr) return '-';
      const date = new Date(dateStr);
      return date.toLocaleString('zh-CN');
    }
  }
}
</script>

<style scoped>
.recharge-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.recharge-card {
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: bold;
}

.balance-info {
  color: #409EFF;
  font-weight: bold;
}

.recharge-content {
  padding: 20px 0;
}

.quick-amount-section,
.custom-amount-section,
.payment-method-section {
  margin-bottom: 30px;
}

.quick-amount-section h3,
.custom-amount-section h3,
.payment-method-section h3 {
  margin-bottom: 15px;
  color: #333;
}

.amount-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.amount-btn {
  min-width: 80px;
}

.amount-input {
  width: 300px;
}

.amount-tips {
  margin-top: 10px;
  font-size: 12px;
  color: #999;
}

.amount-tips span {
  margin-right: 20px;
}

.payment-methods {
  display: flex;
  gap: 20px;
}

.payment-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.recharge-action {
  text-align: center;
  margin: 30px 0;
}

.recharge-btn {
  width: 300px;
  height: 50px;
  font-size: 16px;
}

.recharge-notice {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  margin-top: 20px;
}

.recharge-notice h4 {
  margin-bottom: 10px;
  color: #333;
}

.recharge-notice ul {
  margin: 0;
  padding-left: 20px;
}

.recharge-notice li {
  margin-bottom: 5px;
  color: #666;
}

.record-card {
  border-radius: 8px;
}
</style>
