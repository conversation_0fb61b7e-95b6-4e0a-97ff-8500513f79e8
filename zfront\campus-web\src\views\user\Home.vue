<template>
    <div class="main">
        <div class="left" :style="{width:isCollapse?'64px':'200px',background:themeColor.bg,color:themeColor.color}" style="transition: .3s;">
            <div class="logo">
<!--                <img src="@s/assets/logo.jpg" style="width: 26%">-->
            </div>
            <el-menu
                    :collapse-transition="false"
                    :collapse="isCollapse"
                    :router="true"
                    :default-active="$route.path"
                    :background-color="themeColor.bg"
                    :text-color="themeColor.color"
                    :unique-opened="true">
                <el-menu-item index="/home/">
                    <i class="el-icon-s-home"></i>
                    <span>首页</span>
                </el-menu-item>

                <el-submenu index="1">
                    <template slot="title">
                        <i class="el-icon-office-building"></i>
                        <span>任务管理</span>
                    </template>
                    <el-menu-item v-if="user.role.id === 14" index="/home/<USER>">
                        <i class="el-icon-s-order"></i>
                        <span>发布任务</span>
                    </el-menu-item>
                    <el-menu-item v-if="user.role.id === 13" index="/home/<USER>">
                        <i class="el-icon-s-order"></i>
                        <span>接受任务</span>
                    </el-menu-item>
                </el-submenu>


                <el-submenu index="2">
                    <template slot="title">
                        <i class="el-icon-paperclip"></i>
                        <span>订单管理</span>
                    </template>
                    <el-menu-item v-if="user.role.id === 14" index="/home/<USER>">
                        <i class="el-icon-s-order"></i>
                        <span>已发布任务</span>
                    </el-menu-item>

                    <el-menu-item v-if="user.role.id === 13" index="/home/<USER>">
                        <i class="el-icon-s-order"></i>
                        <span>已接受任务</span>
                    </el-menu-item>
                </el-submenu>


                <el-submenu index="3">
                    <template slot="title">
                        <i class="el-icon-s-custom"></i>
                        <span>公告管理</span>
                    </template>
                    <el-menu-item index="/home/<USER>">
                    <i class="el-icon-s-order"></i>
                        <span>查看公告</span>
                    </el-menu-item>
                </el-submenu>

                <el-submenu index="9">
                    <template slot="title">
                        <i class="el-icon-s-custom"></i>
                        <span>评价管理</span>
                    </template>
                     <el-menu-item v-if="user.role.id === 14" index="/home/<USER>">
                        <i class="el-icon-s-order"></i>
                        <span>我发布的评价</span>
                    </el-menu-item>
                    <el-menu-item v-if="user.role.id === 13" index="/home/<USER>">
                    <i class="el-icon-s-order"></i>
                    <span>查看评价</span>
                    </el-menu-item>
                </el-submenu>

                <!-- 论坛入口，只对维修员和管理员可见，用户角色ID是14 -->
                <el-submenu index="10" v-if="user.role.id !== 14">
                    <template slot="title">
                        <i class="el-icon-chat-dot-square"></i>
                        <span>维修员论坛</span>
                    </template>
                    <el-menu-item index="/home/<USER>/posts">
                        <i class="el-icon-document"></i>
                        <span>帖子列表</span>
                    </el-menu-item>
                    <el-menu-item index="/home/<USER>/my-posts">
                        <i class="el-icon-s-order"></i>
                        <span>我的帖子</span>
                    </el-menu-item>
                    <!-- 管理员角色不是14（用户角色）也不是13（维修员角色） -->
                    <el-menu-item v-if="user.role.id !== 14 && user.role.id !== 13" index="/home/<USER>/audit">
                        <i class="el-icon-s-check"></i>
                        <span>帖子审核</span>
                    </el-menu-item>
                    <el-menu-item index="/home/<USER>/notifications">
                        <i class="el-icon-bell"></i>
                        <span>消息通知</span>
                        <el-badge v-if="unreadCount > 0" :value="unreadCount" class="notification-badge" />
                    </el-menu-item>
                </el-submenu>

                <el-submenu index="11">
                    <template slot="title">
                        <i class="el-icon-s-custom"></i>
                        <span>个人中心</span>
                    </template>
                    <el-menu-item index="/home/<USER>">个人信息
                    </el-menu-item>
                </el-submenu>

                <el-menu-item v-if="user.role.id === 14">
                    <span>当前余额: {{ user.balance }}元</span>
                    <!-- <el-button type="text" @click="recharge(user.studentId)">充值余额</el-button> -->
                </el-menu-item>
            </el-menu>
        </div>

        <div class="right"
             :style="{width:isCollapse?windowWidth-64+'px':windowWidth-200+'px',left:isCollapse?'64px':'200px'}">
            <div class="top"
                 :style="{width:isCollapse?windowWidth-64+'px':windowWidth-200+'px',left:isCollapse?'64px':'200px',background:themeColor.bg}">
                <div class="icon" @click="isCollapse = !isCollapse">
                    <i :class="isCollapse?'el-icon-s-unfold':'el-icon-s-fold'" :style="{color:themeColor.color}"></i>
                </div>
                <el-menu

                        :unique-opened="true"
                        :default-active="activeIndex"
                        class="el-menu-demo"
                        mode="horizontal"
                        :background-color="themeColor.bg"
                        :text-color="themeColor.color"
                        :active-text-color="themeColor.color"
                        menu-trigger="click">

                    <el-menu-item v-if="user.role.id === 14" @click="recharge(user.studentId)">充值余额</el-menu-item>
<!--                    <el-menu-item @click="recharge(user.studentId)">充值余额</el-menu-item>-->

                    <el-submenu index="1">
                        <template slot="title">更换主题</template>
                        <el-menu-item v-for="item in theme" @click="changeColor(item)">
                            {{item.name}}
                        </el-menu-item>
                    </el-submenu>
                    <el-submenu index="2">
<!--                        <template slot="title">{{user.username}}</template>-->
                        <el-avatar slot="title" style="background: #65c4a6; user-select: none;">{{firstName}}</el-avatar>
                        <el-menu-item index="2-1" @click="exit">退出</el-menu-item>
                        <el-menu-item index="2-2" @click="updPassword(user.id)">修改密码</el-menu-item>
                        <el-menu-item index="2-3" @click="personalInformation()">修改个人信息</el-menu-item>
                    </el-submenu>
                </el-menu>

            </div>
            <div class="bottom">
                <div class="bottom_top">
                    <el-breadcrumb separator-class="el-icon-arrow-right">
                        <el-breadcrumb-item v-for="item in breadList" :to="item.path" v-if="item.meta.title">
                            {{item.meta.title}}
                        </el-breadcrumb-item>
                    </el-breadcrumb>
                </div>
                <transition name="el-fade-in" mode="out-in">
                    <router-view @personalInformation="personalInformation"></router-view>
                </transition>
            </div>
        </div>

        <el-drawer
                title="完善信息"
                :visible.sync="drawer"
                direction="rtl"
                closeDrawer="false"
                :show-close="false"
                :before-close="handleClose">
            <el-form :model="ruleForm" status-icon :rules="rules" ref="ruleForm" label-width="100px"
                     class="demo-ruleForm ruleform">

                <!-- 根据角色 ID 动态显示班级信息 -->
                <el-form-item label="类别" v-if="user.role.id !== 14">
                    <el-cascader
                            v-model="value"
                            :options="role.depts"
                            :props="{
                children:'classes',
                label:'name',
                value:'id'
            }"
                    ></el-cascader>
                </el-form-item>

                <el-form-item label="姓名" prop="username">
                    <el-input v-model="ruleForm.username"></el-input>
                </el-form-item>
                <el-form-item label="工号" prop="phone">
                    <el-input v-model.number="ruleForm.phone"></el-input>
                </el-form-item>

                <el-form-item label="性别">
                    <el-radio-group v-model="sex">
                        <el-radio label="0">男</el-radio>
                        <el-radio label="1">女</el-radio>
                    </el-radio-group>
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" @click="submitForm('ruleForm')">提交</el-button>
                </el-form-item>
            </el-form>
        </el-drawer>

        <el-dialog title="修改信息" :visible.sync="dialogVisible" :close-on-click-modal="false">
            <el-form :model="ruleForm" :rules="rules" ref="ruleForm">
                <el-form-item label="姓名" prop="username">
                    <el-input v-model.number="ruleForm.username"></el-input>
                </el-form-item>
                <el-form-item label="手机号" prop="phone">
                    <el-input v-model.number="ruleForm.phone" oninput="if(value.length>11)value=value.slice(0,11)"></el-input>
                </el-form-item>

                <!-- 维修员可以修改类别和子类别 -->
                <template v-if="user.role && user.role.id === 13">
                    <el-form-item label="类别-子类别" prop="categoryValue">
                        <el-cascader
                            v-model="categoryValue"
                            :options="categoryOptions"
                            :props="{
                                children: 'children',
                                label: 'name',
                                value: 'id',
                                checkStrictly: false
                            }"
                            placeholder="请选择类别和子类别"
                            clearable
                        ></el-cascader>
                    </el-form-item>

                    <!-- 维修员地址信息 -->
                    <el-form-item label="省市区" prop="addressRegion">
                        <el-cascader
                            v-model="addressRegion"
                            :options="regionOptions"
                            placeholder="请选择省/市/区"
                            style="width: 100%"
                        ></el-cascader>
                    </el-form-item>
                    <el-form-item label="详细地址" prop="address">
                        <el-input
                            v-model="ruleForm.address"
                            type="textarea"
                            placeholder="请输入详细地址信息，如街道、门牌号等"
                            :rows="3"
                        ></el-input>
                    </el-form-item>
                </template>

            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="submitChanges">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    import {mapState, mapMutations} from "vuex"
    import user from "@s/store/module/user";
    import regionData from '@/assets/data/region.js';

    export default {
        name: "Home",
        methods: {
            ...mapMutations('user', ['setUser']),
            changeColor(val){
                sessionStorage.setItem("themeColor",JSON.stringify(val))
                this.themeColor = {'bg':val.value,'color':val.color};
            },
            //面包屑
            getBreadcrumb() {
                let matched = this.$route.matched;
                if (matched[0].name != 'home') {
                    matched = [{path: "/home/", meta: {title: '首页'}}].concat(matched)
                }
                this.breadList = matched;
            },
            //关闭抽屉触发的事件
            handleClose(done) {
                // 如果是首次登录且未完善信息，不允许关闭抽屉
                if (this.user.state === 0) {
                    this.$msg("首次登录必须完善信息", "error")
                } else {
                    // 如果不是首次登录或已完善信息，允许关闭抽屉
                    done()
                }
            },
            submitForm(formName) {
                this.$refs[formName].validate((valid) => {
                    if (valid) {
                        // 根据角色ID决定是否需要选择类别
                        if ((this.user.role.id !== 14 && this.value) || this.user.role.id === 14) {
                            const userData = {
                                id: this.user.id,
                                deptId: this.user.role.id !== 14 ? this.value[0] : null,
                                // classId 字段在数据库中不存在，移除该字段
                                username: this.ruleForm.username,
                                phone: this.ruleForm.phone,
                                sex: this.sex,
                                // 设置状态为已完善信息
                                state: 1
                            }

                            this.$put("/user", userData)
                                .then(res => {
                                    if (res.data.status) {
                                        this.drawer = false;
                                        this.$notifyMsg("成功", res.data.msg, "success")
                                        // 更新本地用户信息
                                        if (res.data.user) {
                                            sessionStorage.setItem('user', JSON.stringify(res.data.user))
                                            this.setUser(res.data.user)
                                        }
                                        this.newList(this.user.id)
                                    } else {
                                        this.$msg(res.data.msg, "error")
                                    }
                                })
                                .catch(err => {
                                    console.error('Update failed:', err)
                                    this.$msg(err.response?.data?.msg || "更新失败，请稍后重试", "error")
                                })
                        } else {
                            this.$notifyMsg("错误", "请选择类别", "error")
                        }
                    } else {
                        return false;
                    }
                });
            },
            //修改密码
            updPassword(id) {
                this.$prompt('请输入密码', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    inputType: 'password',
                    closeOnClickModal:false,
                    inputPattern: /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,16}$/,
                    inputErrorMessage: '格式不对，密码只能输入6-16位英文和数字'
                }).then((res) => {
                    // console.log(res);
                    this.$put('/user', {id: id, password: res.value})
                    .then((res) => {
                        this.$notifyMsg('成功', res.data.msg, 'success')
                    })
                }).catch(() => {
                })
            },
            recharge(studentId) {
                this.$prompt('请输入充值金额', '充值', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    inputPattern: /^(0\.\d{1,2}|[1-9]\d*(\.\d{1,2})?)$/,
                    inputErrorMessage: '请输入有效金额（最多两位小数）'
                }).then(({ value }) => {
                    const amount = parseFloat(value);

                    // 验证金额范围
                    if (amount < 0.01 || amount > 10000) {
                        this.$msg("充值金额必须在0.01-10000元之间", "error");
                        return;
                    }

                    // 创建支付宝支付订单
                    const payData = {
                        subject: '用户充值',
                        totalAmount: amount,
                        userId: this.user.id
                    };

                    // 调用后端创建支付订单接口
                    this.$post('api/alipay/create', payData)
                        .then(res => {
                            if (res.data.status) {
                                // 在新窗口中打开支付页面
                                const payWindow = window.open('', '_blank', 'width=800,height=600');
                                payWindow.document.write(res.data.data.payForm);
                                payWindow.document.close();

                                // 定义轮询次数和间隔
                                let pollCount = 0;
                                const maxPolls = 60; // 最多轮询60次
                                const pollInterval = 3000; // 每3秒轮询一次

                                // 获取订单号
                                const orderNo = res.data.data.orderNo;
                                
                                // 标记是否已经显示过成功消息
                                let hasShownSuccess = false;

                                // 开始轮询支付状态
                                const checkPayment = setInterval(() => {
                                    // 如果支付窗口关闭或达到最大轮询次数，停止轮询
                                    if (pollCount >= maxPolls || payWindow.closed) {
                                        clearInterval(checkPayment);
                                        
                                        // 最后查询一次支付状态
                                        this.$get(`api/alipay/query/${orderNo}`)
                                            .then(queryRes => {
                                                if (queryRes.data.status && queryRes.data.data.status === 1) {
                                                    // 支付成功，但只在未显示过成功消息时显示
                                                    if (!hasShownSuccess) {
                                                        this.newList(this.user.id);
                                                        this.$msg("充值成功", "success");
                                                        hasShownSuccess = true;
                                                    }
                                                } else if (!hasShownSuccess) {
                                                    // 支付未完成，且未显示过成功消息
                                                    this.$msg("支付未完成，如已支付请稍后刷新查看", "warning");
                                                }
                                            })
                                            .catch(() => {
                                                if (!hasShownSuccess) {
                                                    this.$msg("查询支付状态失败，如已支付请稍后刷新查看", "warning");
                                                }
                                            });
                                        return;
                                    }

                                    // 定期查询支付状态
                                    this.$get(`api/alipay/query/${orderNo}`)
                                        .then(queryRes => {
                                            if (queryRes.data.status && queryRes.data.data.status === 1 && !hasShownSuccess) {
                                                // 支付成功且未显示过成功消息
                                                clearInterval(checkPayment);
                                                this.newList(this.user.id);
                                                this.$msg("充值成功", "success");
                                                hasShownSuccess = true;
                                                if (!payWindow.closed) {
                                                    payWindow.close(); // 自动关闭支付窗口
                                                }
                                            }
                                        })
                                        .catch(err => {
                                            console.error('查询支付状态失败:', err);
                                        });

                                    pollCount++;
                                }, pollInterval);

                            } else {
                                this.$msg(res.data.msg || "创建支付订单失败", "error");
                            }
                        })
                        .catch(err => {
                            console.error('创建支付订单失败:', err);
                            this.$msg("创建支付订单失败，请稍后重试", "error");
                        });
                }).catch(() => {
                    this.$msg("已取消充值", "info");
                });
            },
            personalInformation() {
                this.dialogVisible = true;
                this.ruleForm.username = this.user.username;
                this.ruleForm.phone = this.user.phone;

                // 如果是维修员，加载类别和子类别数据
                if (this.user.role && this.user.role.id === 13) {
                    // 设置当前的类别和子类别
                    if (this.user.dept) {
                        this.ruleForm.deptId = this.user.dept.id;
                    }
                    if (this.user.type) {
                        this.ruleForm.classId = this.user.type.id;
                    }

                    // 设置级联选择器的初始值
                    if (this.user.dept && this.user.type) {
                        this.categoryValue = [this.user.dept.id, this.user.type.id];
                    } else if (this.user.dept) {
                        this.categoryValue = [this.user.dept.id];
                    } else {
                        this.categoryValue = [];
                    }

                    // 设置地址选择器的初始值
                    if (this.user.province && this.user.city && this.user.district) {
                        this.addressRegion = [this.user.province, this.user.city, this.user.district];
                        this.ruleForm.address = this.user.address || '';
                    } else {
                        this.addressRegion = [];
                        this.ruleForm.address = '';
                    }

                    // 加载所有可用的类别和子类别
                    this.loadCategoryOptions();
                }
            },

            // 加载类别和子类别的级联选项
            loadCategoryOptions() {
                // 先获取所有类别
                this.$get("/dept/list", { roleId: 13 })
                .then(res => {
                    if (res.data.status && res.data.dept) {
                        // 保存原始类别列表（兼容性考虑）
                        this.deptOptions = res.data.dept;
                        console.log('可用的类别列表:', this.deptOptions);

                        // 为每个类别加载子类别
                        const promises = res.data.dept.map(dept => {
                            return this.$get("/class/list", { deptId: dept.id })
                            .then(classRes => {
                                if (classRes.data.status && classRes.data.class) {
                                    // 返回带有子类别的类别对象
                                    return {
                                        ...dept,
                                        children: classRes.data.class
                                    };
                                }
                                // 如果没有子类别，返回原始类别对象
                                return {
                                    ...dept,
                                    children: []
                                };
                            })
                            .catch(() => {
                                // 出错时返回没有子类别的类别对象
                                return {
                                    ...dept,
                                    children: []
                                };
                            });
                        });

                        // 等待所有子类别加载完成
                        Promise.all(promises)
                        .then(categories => {
                            this.categoryOptions = categories;
                            console.log('级联选择器选项:', this.categoryOptions);
                        });
                    } else {
                        this.$msg("获取类别列表失败", "error");
                    }
                })
                .catch(err => {
                    console.error('获取类别列表失败:', err);
                    this.$msg("获取类别列表失败，请稍后重试", "error");
                });
            },

            // 保留原有的方法（兼容性考虑）
            loadDepartments() {
                this.$get("/dept/list", { roleId: 13 })
                .then(res => {
                    if (res.data.status && res.data.dept) {
                        this.deptOptions = res.data.dept;
                        console.log('可用的类别列表:', this.deptOptions);
                    } else {
                        this.$msg("获取类别列表失败", "error");
                    }
                })
                .catch(err => {
                    console.error('获取类别列表失败:', err);
                    this.$msg("获取类别列表失败，请稍后重试", "error");
                });
            },

            // 保留原有的方法（兼容性考虑）
            loadSubCategories(deptId) {
                if (!deptId) return;

                this.$get("/class/list", { deptId: deptId })
                .then(res => {
                    if (res.data.status && res.data.class) {
                        this.classOptions = res.data.class;
                        console.log('可用的子类别列表:', this.classOptions);

                        // 如果当前选择的子类别不在新的子类别列表中，清空选择
                        const exists = this.classOptions.some(item => item.id === this.ruleForm.classId);
                        if (!exists) {
                            this.ruleForm.classId = null;
                        }
                    } else {
                        this.classOptions = [];
                        this.ruleForm.classId = null;
                        this.$msg("获取子类别列表失败", "error");
                    }
                })
                .catch(err => {
                    console.error('获取子类别列表失败:', err);
                    this.classOptions = [];
                    this.ruleForm.classId = null;
                    this.$msg("获取子类别列表失败，请稍后重试", "error");
                });
            },
            // submitChanges(){
            //     this.$put("/user",{id:this.user.id,username:this.ruleForm.username,phone:this.ruleForm.phone})
            //     .then(res=>{
            //         this.$notifyMsg("成功",res.data.msg,"success",1000);
            //         this.dialogVisible = false;
            //         this.newList(this.user.id)
            //     })
            // },
            submitChanges() {
                // 准备要更新的用户数据
                const userData = {
                    id: this.user.id,
                    username: this.ruleForm.username,
                    phone: this.ruleForm.phone,
                    state: 1
                };

                // 如果是维修员，添加类别和子类别信息
                if (this.user.role && this.user.role.id === 13) {
                    // 从级联选择器中获取类别和子类别ID
                    if (this.categoryValue && this.categoryValue.length > 0) {
                        // 第一个值是类别ID
                        userData.deptId = this.categoryValue[0];

                        // 如果选择了子类别（数组长度大于1），第二个值是子类别ID
                        if (this.categoryValue.length > 1) {
                            userData.classId = this.categoryValue[1];
                        }
                    }
                    // 兼容旧版本的选择方式
                    else {
                        // 只有当选择了类别时才更新类别ID
                        if (this.ruleForm.deptId) {
                            userData.deptId = this.ruleForm.deptId;
                        }

                        // 只有当选择了子类别时才更新子类别ID
                        if (this.ruleForm.classId) {
                            userData.classId = this.ruleForm.classId;
                        }
                    }

                    // 处理地址信息
                    if (this.addressRegion && this.addressRegion.length === 3) {
                        userData.province = this.addressRegion[0];
                        userData.city = this.addressRegion[1];
                        userData.district = this.addressRegion[2];
                    }

                    // 如果有详细地址，添加到更新数据中
                    if (this.ruleForm.address) {
                        userData.address = this.ruleForm.address;
                    }
                }

                console.log('提交的用户数据:', userData);

                // 发送更新请求
                this.$put("/user", userData)
                    .then(res => {
                        if (res.data.status) {
                            this.$notifyMsg("成功", res.data.msg, "success");
                            this.dialogVisible = false;
                            this.newList(this.user.id);
                        } else {
                            this.$msg(res.data.msg || "更新失败", "error");
                        }
                    })
                    .catch(err => {
                        console.error('更新用户信息失败:', err);
                        this.$msg("更新失败，请稍后重试", "error");
                    });
            },
            //根据当前用户查询id
            newList(id) {
                this.$get("/user/" + id)
                .then((rs) => {
                    // 确保用户状态正确更新
                    if (rs.data.user) {
                        console.log('User data from API:', JSON.stringify(rs.data.user, null, 2));
                        console.log('User role ID:', rs.data.user.role ? rs.data.user.role.id : 'No role');
                        console.log('User dept:', rs.data.user.dept ? JSON.stringify(rs.data.user.dept, null, 2) : 'No dept data');
                        console.log('User type:', rs.data.user.type ? JSON.stringify(rs.data.user.type, null, 2) : 'No type data');

                        // 如果是维修员但没有type信息，尝试重新获取用户数据
                        if (rs.data.user.role && rs.data.user.role.id === 13 && !rs.data.user.type) {
                            console.log('维修员没有type信息，尝试修复...');
                            // 尝试手动设置type信息
                            if (rs.data.user.classId) {
                                console.log('找到classId:', rs.data.user.classId);
                                // 获取type信息
                                this.$get("/class/" + rs.data.user.classId)
                                .then(typeRes => {
                                    if (typeRes.data.class) {
                                        console.log('获取到type信息:', JSON.stringify(typeRes.data.class, null, 2));
                                        // 手动设置type信息
                                        rs.data.user.type = typeRes.data.class;
                                        // 更新用户信息
                                        sessionStorage.setItem("user", JSON.stringify(rs.data.user));
                                        this.setUser(rs.data.user);
                                    }
                                });
                            }
                        }

                        // 如果用户已完善信息，确保状态为1
                        if (rs.data.user.dept && rs.data.user.state === 0) {
                            rs.data.user.state = 1;
                            // 更新用户状态
                            this.$put("/user", {
                                id: rs.data.user.id,
                                state: 1
                            });
                        }
                        sessionStorage.setItem("user", JSON.stringify(rs.data.user))
                        this.setUser(JSON.parse(sessionStorage.getItem("user")))
                        // 修改完名字, 清空當前firstName; 避免出現疊加
                        this.firstName = '';
                        this.textAvatar(rs.data.user.username);
                    }
                })
            },
            exit(){
                sessionStorage.removeItem('user');
                this.$router.push('/')
            },
            // 文字頭像
            textAvatar(username) {
                let arr = username.split(' ');
                for (var i in arr) {
                    this.firstName += arr[i].substr(0,1);
                }
                this.firstName = this.firstName.toLocaleUpperCase();
                console.log('firstName->' + this.firstName);
            },

            // 获取未读通知数量
            getUnreadNotificationCount() {
                if (this.user && this.user.id) {
                    this.$get('/forum/notification/unread/count', { userId: this.user.id })
                    .then(res => {
                        if (res.data.status) {
                            this.unreadCount = res.data.count;
                        }
                    })
                    .catch(err => {
                        console.error('获取未读通知数量失败:', err);
                    });
                }
            }
        },
        computed: {
            ...mapState('user', ['user']),
            theme(){
                return this.$store.state.theme.theme
            },
        },
        data() {
            var validateUsername = (rule, value, callback) => {
                if (value === '') {
                    callback(new Error('请输入姓名'));
                } else {
                    callback();
                }
            };
            var validatePhone = (rule, value, callback) => {
                if (value === '') {
                    callback(new Error('请输入手机号'));
                } else {
                    callback();
                }
            };

            return {
                // 文字头像
                firstName:'',
                ruleForm: {
                    username: '',
                    phone: '',
                    deptId: null,
                    classId: null,
                    address: ''
                },
                // 级联选择器的值和选项
                categoryValue: [],
                categoryOptions: [],
                // 保留原有的选项（兼容性考虑）
                deptOptions: [],
                classOptions: [],

                // 地址相关
                addressRegion: [],
                regionOptions: [],
                rules: {
                    username: [
                        {validator: validateUsername, trigger: 'blur'}
                    ],
                    phone: [
                        {validator: validatePhone, trigger: 'blur'}
                    ]
                },
                //颜色
                themeColor : {'bg':'#fff','color':'#000'},
                //性别
                sex:'0',
                drawer: false,
                //当前路由
                breadList: [],
                //当前屏幕宽度
                windowWidth: document.documentElement.clientWidth,
                activeIndex: '1',
                //控制菜单是否展开
                isCollapse: false,
                admin: "",
                // role: [],
                role:[],
                //级联选择器的值
                value: "",
                dialogVisible:false,
                // 未读通知数量
                unreadCount: 0
            }
        },
        watch: {
            '$route'(to, form) {
                this.getBreadcrumb()
            }
        },
        created() {
            // 初始化地址数据
            this.regionOptions = regionData;

            let theme = JSON.parse(sessionStorage.getItem("themeColor"));
            if (theme){
                this.themeColor = {'bg':theme.value,'color':theme.color}
            }

            if (sessionStorage.getItem('user')){
                this.getBreadcrumb();
                const userData = JSON.parse(sessionStorage.getItem("user"));
                console.log('User data from session:', userData);
                this.setUser(userData);

                // 如果是维修员或管理员，获取未读通知数量
                if (this.user.role && (this.user.role.id === 13 || this.user.role.id === 14)) {
                    this.getUnreadNotificationCount();

                    // 每分钟获取一次未读通知数量
                    setInterval(() => {
                        this.getUnreadNotificationCount();
                    }, 60000);
                }
                //
                // 检查用户是否是首次登录（通过检查state字段）
                // state为0表示未完善信息，state为1表示已完善信息
                if (this.user.state === 0) {
                    this.$get("/role/" + this.user.role.id)
                    .then((res) => {
                        console.log(res.data)
                        this.role = res.data.role;
                        this.drawer = true
                        this.$msg("首次登录，请完善信息", "warning")
                    })
                }
            }else {
                this.$msg("您向未登陆,没有权限","error")
                this.$router.push("/")
            }

        },
        mounted() {
            // 获取当前屏幕宽度
            window.onresize = () => {
                this.windowWidth = document.documentElement.clientWidth
            }
            // 文字頭像
            this.textAvatar(this.user.username);
        }
    }
</script>

<style scoped lang="less">
    .main {
        display: flex;
        height: 90%;

        .left {
            position: fixed;
            height: 100%;

            .logo {
                width: 90%;
                /*color: white;*/
                font-size: 16px;
                text-align: center;
                padding: 8px 0;
                /*border: 1px solid white;*/
                margin: 9.1px auto 0 auto;
            }
        }

        .right {
            transition: all 0.3s ease 0s;
            position: relative;

            .top {
                transition: all 0.3s ease 0s;
                position: fixed;
                /*color: #fff;*/
                display: flex;
                align-items: center;
                justify-content: space-between;
                z-index: 9;

                .icon {
                    font-size: 20px;
                    cursor: pointer;
                    margin-left: 10px;
                }
            }

            .bottom {
                width: 100%;
                height: 100%;
                /*background: #fff;*/
                margin-top: 65px;
                .bottom_top {
                    padding: 20px;
                }
            }
        }

        .ruleform /deep/ .el-input {
            width: 80% !important;
        }

        /deep/ .el-cascader {
            width: 100% !important;
        }
    }
</style>
