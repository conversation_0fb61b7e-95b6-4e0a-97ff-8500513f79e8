<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yqn.mapper.UserMapper">

    <resultMap id="userResultMap" type="com.yqn.pojo.User">
        <id column="id" property="id"/>
        <result column="student_id" property="studentId"/>
        <result column="password" property="password"/>
        <result column="phone" property="phone"/>
        <result column="role_id" property="roleId"/>
        <result column="dept_id" property="deptId"/>
        <result column="type_id" property="classId"/>
        <result column="sex" property="sex"/>
        <result column="username" property="username"/>
        <result column="create_time" property="createTime"/>
        <result column="balance" property="balance"/>
        <result column="state" property="state"/>
        <result column="province" property="province"/>
        <result column="city" property="city"/>
        <result column="district" property="district"/>
        <result column="address" property="address"/>

        <association property="role" column="role_id" select="findEntityRoleById"/>
        <association property="dept" column="dept_id" select="findEntityDeptById"/>
        <association property="type" column="type_id" select="findEntityClassById"/>
    </resultMap>

    <select id="findEntityRoleById" resultType="com.yqn.pojo.Role">
        select * from role where id=#{id}
    </select>

    <select id="findEntityDeptById" resultType="com.yqn.pojo.Dept">
        select * from dept where id=#{id}
    </select>

    <select id="findEntityClassById" resultType="com.yqn.pojo.Class">
        select * from type where id=#{id}
    </select>

</mapper>
