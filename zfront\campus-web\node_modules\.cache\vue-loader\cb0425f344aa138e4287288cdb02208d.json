{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\user\\Home.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\user\\Home.vue", "mtime": 1748677982322}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7bWFwU3RhdGUsIG1hcE11dGF0aW9uc30gZnJvbSAidnVleCIKaW1wb3J0IHVzZXIgZnJvbSAiQHMvc3RvcmUvbW9kdWxlL3VzZXIiOwppbXBvcnQgcmVnaW9uRGF0YSBmcm9tICdAL2Fzc2V0cy9kYXRhL3JlZ2lvbi5qcyc7CgpleHBvcnQgZGVmYXVsdCB7CiAgICBuYW1lOiAiSG9tZSIsCiAgICBtZXRob2RzOiB7CiAgICAgICAgLi4ubWFwTXV0YXRpb25zKCd1c2VyJywgWydzZXRVc2VyJ10pLAogICAgICAgIGNoYW5nZUNvbG9yKHZhbCl7CiAgICAgICAgICAgIHNlc3Npb25TdG9yYWdlLnNldEl0ZW0oInRoZW1lQ29sb3IiLEpTT04uc3RyaW5naWZ5KHZhbCkpCiAgICAgICAgICAgIHRoaXMudGhlbWVDb2xvciA9IHsnYmcnOnZhbC52YWx1ZSwnY29sb3InOnZhbC5jb2xvcn07CiAgICAgICAgfSwKICAgICAgICAvL+mdouWMheWxkQogICAgICAgIGdldEJyZWFkY3J1bWIoKSB7CiAgICAgICAgICAgIGxldCBtYXRjaGVkID0gdGhpcy4kcm91dGUubWF0Y2hlZDsKICAgICAgICAgICAgaWYgKG1hdGNoZWRbMF0ubmFtZSAhPSAnaG9tZScpIHsKICAgICAgICAgICAgICAgIG1hdGNoZWQgPSBbe3BhdGg6ICIvaG9tZS8iLCBtZXRhOiB7dGl0bGU6ICfpppbpobUnfX1dLmNvbmNhdChtYXRjaGVkKQogICAgICAgICAgICB9CiAgICAgICAgICAgIHRoaXMuYnJlYWRMaXN0ID0gbWF0Y2hlZDsKICAgICAgICB9LAogICAgICAgIC8v5YWz6Zet5oq95bGJ6Kem5Y+R55qE5LqL5Lu2CiAgICAgICAgaGFuZGxlQ2xvc2UoZG9uZSkgewogICAgICAgICAgICAvLyDlpoLmnpzmmK/pppbmrKHnmbvlvZXkuJTmnKrlrozlloTkv6Hmga/vvIzkuI3lhYHorrjlhbPpl63mir3lsYkKICAgICAgICAgICAgaWYgKHRoaXMudXNlci5zdGF0ZSA9PT0gMCkgewogICAgICAgICAgICAgICAgdGhpcy4kbXNnKCLpppbmrKHnmbvlvZXlv4XpobvlrozlloTkv6Hmga8iLCAiZXJyb3IiKQogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgLy8g5aaC5p6c5LiN5piv6aaW5qyh55m75b2V5oiW5bey5a6M5ZaE5L+h5oGv77yM5YWB6K645YWz6Zet5oq95bGJCiAgICAgICAgICAgICAgICBkb25lKCkKICAgICAgICAgICAgfQogICAgICAgIH0sCiAgICAgICAgc3VibWl0Rm9ybShmb3JtTmFtZSkgewogICAgICAgICAgICB0aGlzLiRyZWZzW2Zvcm1OYW1lXS52YWxpZGF0ZSgodmFsaWQpID0+IHsKICAgICAgICAgICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgICAgICAgICAgIC8vIOagueaNruinkuiJsklE5Yaz5a6a5piv5ZCm6ZyA6KaB6YCJ5oup57G75YirCiAgICAgICAgICAgICAgICAgICAgaWYgKCh0aGlzLnVzZXIucm9sZS5pZCAhPT0gMTQgJiYgdGhpcy52YWx1ZSkgfHwgdGhpcy51c2VyLnJvbGUuaWQgPT09IDE0KSB7CiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHVzZXJEYXRhID0gewogICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ6IHRoaXMudXNlci5pZCwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRlcHRJZDogdGhpcy51c2VyLnJvbGUuaWQgIT09IDE0ID8gdGhpcy52YWx1ZVswXSA6IG51bGwsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBjbGFzc0lkIOWtl+auteWcqOaVsOaNruW6k+S4reS4jeWtmOWcqO+8jOenu+mZpOivpeWtl+autQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdXNlcm5hbWU6IHRoaXMucnVsZUZvcm0udXNlcm5hbWUsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBwaG9uZTogdGhpcy5ydWxlRm9ybS5waG9uZSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNleDogdGhpcy5zZXgsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyDorr7nva7nirbmgIHkuLrlt7LlrozlloTkv6Hmga8KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0YXRlOiAxCiAgICAgICAgICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJHB1dCgiL3VzZXIiLCB1c2VyRGF0YSkKICAgICAgICAgICAgICAgICAgICAgICAgICAgIC50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHJlcy5kYXRhLnN0YXR1cykgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmRyYXdlciA9IGZhbHNlOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRub3RpZnlNc2coIuaIkOWKnyIsIHJlcy5kYXRhLm1zZywgInN1Y2Nlc3MiKQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyDmm7TmlrDmnKzlnLDnlKjmiLfkv6Hmga8KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHJlcy5kYXRhLnVzZXIpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNlc3Npb25TdG9yYWdlLnNldEl0ZW0oJ3VzZXInLCBKU09OLnN0cmluZ2lmeShyZXMuZGF0YS51c2VyKSkKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuc2V0VXNlcihyZXMuZGF0YS51c2VyKQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMubmV3TGlzdCh0aGlzLnVzZXIuaWQpCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbXNnKHJlcy5kYXRhLm1zZywgImVycm9yIikKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KQogICAgICAgICAgICAgICAgICAgICAgICAgICAgLmNhdGNoKGVyciA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcignVXBkYXRlIGZhaWxlZDonLCBlcnIpCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbXNnKGVyci5yZXNwb25zZT8uZGF0YT8ubXNnIHx8ICLmm7TmlrDlpLHotKXvvIzor7fnqI3lkI7ph43or5UiLCAiZXJyb3IiKQogICAgICAgICAgICAgICAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRub3RpZnlNc2coIumUmeivryIsICLor7fpgInmi6nnsbvliKsiLCAiZXJyb3IiKQogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICB9KTsKICAgICAgICB9LAogICAgICAgIC8v5L+u5pS55a+G56CBCiAgICAgICAgdXBkUGFzc3dvcmQoaWQpIHsKICAgICAgICAgICAgdGhpcy4kcHJvbXB0KCfor7fovpPlhaXlr4bnoIEnLCAn5o+Q56S6JywgewogICAgICAgICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgICAgICAgICBpbnB1dFR5cGU6ICdwYXNzd29yZCcsCiAgICAgICAgICAgICAgICBjbG9zZU9uQ2xpY2tNb2RhbDpmYWxzZSwKICAgICAgICAgICAgICAgIGlucHV0UGF0dGVybjogL14oPyFbMC05XSskKSg/IVthLXpBLVpdKyQpWzAtOUEtWmEtel17NiwxNn0kLywKICAgICAgICAgICAgICAgIGlucHV0RXJyb3JNZXNzYWdlOiAn5qC85byP5LiN5a+577yM5a+G56CB5Y+q6IO96L6T5YWlNi0xNuS9jeiLseaWh+WSjOaVsOWtlycKICAgICAgICAgICAgfSkudGhlbigocmVzKSA9PiB7CiAgICAgICAgICAgICAgICAvLyBjb25zb2xlLmxvZyhyZXMpOwogICAgICAgICAgICAgICAgdGhpcy4kcHV0KCcvdXNlcicsIHtpZDogaWQsIHBhc3N3b3JkOiByZXMudmFsdWV9KQogICAgICAgICAgICAgICAgLnRoZW4oKHJlcykgPT4gewogICAgICAgICAgICAgICAgICAgIHRoaXMuJG5vdGlmeU1zZygn5oiQ5YqfJywgcmVzLmRhdGEubXNnLCAnc3VjY2VzcycpCiAgICAgICAgICAgICAgICB9KQogICAgICAgICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgICAgIH0pCiAgICAgICAgfSwKICAgICAgICByZWNoYXJnZShzdHVkZW50SWQpIHsKICAgICAgICAgICAgdGhpcy4kcHJvbXB0KCfor7fovpPlhaXlhYXlgLzph5Hpop0nLCAn5YWF5YC8JywgewogICAgICAgICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgICAgICAgICBpbnB1dFBhdHRlcm46IC9eKDBcLlxkezEsMn18WzEtOV1cZCooXC5cZHsxLDJ9KT8pJC8sCiAgICAgICAgICAgICAgICBpbnB1dEVycm9yTWVzc2FnZTogJ+ivt+i+k+WFpeacieaViOmHkemine+8iOacgOWkmuS4pOS9jeWwj+aVsO+8iScKICAgICAgICAgICAgfSkudGhlbigoeyB2YWx1ZSB9KSA9PiB7CiAgICAgICAgICAgICAgICBjb25zdCBhbW91bnQgPSBwYXJzZUZsb2F0KHZhbHVlKTsKCiAgICAgICAgICAgICAgICAvLyDpqozor4Hph5Hpop3ojIPlm7QKICAgICAgICAgICAgICAgIGlmIChhbW91bnQgPCAwLjAxIHx8IGFtb3VudCA+IDEwMDAwKSB7CiAgICAgICAgICAgICAgICAgICAgdGhpcy4kbXNnKCLlhYXlgLzph5Hpop3lv4XpobvlnKgwLjAxLTEwMDAw5YWD5LmL6Ze0IiwgImVycm9yIik7CiAgICAgICAgICAgICAgICAgICAgcmV0dXJuOwogICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgIC8vIOWIm+W7uuaUr+S7mOWuneaUr+S7mOiuouWNlQogICAgICAgICAgICAgICAgY29uc3QgcGF5RGF0YSA9IHsKICAgICAgICAgICAgICAgICAgICBzdWJqZWN0OiAn55So5oi35YWF5YC8JywKICAgICAgICAgICAgICAgICAgICB0b3RhbEFtb3VudDogYW1vdW50LAogICAgICAgICAgICAgICAgICAgIHVzZXJJZDogdGhpcy51c2VyLmlkCiAgICAgICAgICAgICAgICB9OwoKICAgICAgICAgICAgICAgIC8vIOiwg+eUqOWQjuerr+WIm+W7uuaUr+S7mOiuouWNleaOpeWPowogICAgICAgICAgICAgICAgdGhpcy4kcG9zdCgnYXBpL2FsaXBheS9jcmVhdGUnLCBwYXlEYXRhKQogICAgICAgICAgICAgICAgICAgIC50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChyZXMuZGF0YS5zdGF0dXMpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIOWcqOaWsOeql+WPo+S4reaJk+W8gOaUr+S7mOmhtemdogogICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgcGF5V2luZG93ID0gd2luZG93Lm9wZW4oJycsICdfYmxhbmsnLCAnd2lkdGg9ODAwLGhlaWdodD02MDAnKTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBheVdpbmRvdy5kb2N1bWVudC53cml0ZShyZXMuZGF0YS5kYXRhLnBheUZvcm0pOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgcGF5V2luZG93LmRvY3VtZW50LmNsb3NlKCk7CgogICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8g5a6a5LmJ6L2u6K+i5qyh5pWw5ZKM6Ze06ZqUCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBsZXQgcG9sbENvdW50ID0gMDsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IG1heFBvbGxzID0gNjA7IC8vIOacgOWkmui9ruivojYw5qyhCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBwb2xsSW50ZXJ2YWwgPSAzMDAwOyAvLyDmr48z56eS6L2u6K+i5LiA5qyhCgogICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8g6I635Y+W6K6i5Y2V5Y+3CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBvcmRlck5vID0gcmVzLmRhdGEuZGF0YS5vcmRlck5vOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyDmoIforrDmmK/lkKblt7Lnu4/mmL7npLrov4fmiJDlip/mtojmga8KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxldCBoYXNTaG93blN1Y2Nlc3MgPSBmYWxzZTsKCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyDlvIDlp4vova7or6LmlK/ku5jnirbmgIEKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGNoZWNrUGF5bWVudCA9IHNldEludGVydmFsKCgpID0+IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyDlpoLmnpzmlK/ku5jnqpflj6PlhbPpl63miJbovr7liLDmnIDlpKfova7or6LmrKHmlbDvvIzlgZzmraLova7or6IKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAocG9sbENvdW50ID49IG1heFBvbGxzIHx8IHBheVdpbmRvdy5jbG9zZWQpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xlYXJJbnRlcnZhbChjaGVja1BheW1lbnQpOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8g5pyA5ZCO5p+l6K+i5LiA5qyh5pSv5LuY54q25oCBCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJGdldChgYXBpL2FsaXBheS9xdWVyeS8ke29yZGVyTm99YCkKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC50aGVuKHF1ZXJ5UmVzID0+IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAocXVlcnlSZXMuZGF0YS5zdGF0dXMgJiYgcXVlcnlSZXMuZGF0YS5kYXRhLnN0YXR1cyA9PT0gMSkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyDmlK/ku5jmiJDlip/vvIzkvYblj6rlnKjmnKrmmL7npLrov4fmiJDlip/mtojmga/ml7bmmL7npLoKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKCFoYXNTaG93blN1Y2Nlc3MpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMubmV3TGlzdCh0aGlzLnVzZXIuaWQpOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbXNnKCLlhYXlgLzmiJDlip8iLCAic3VjY2VzcyIpOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFzU2hvd25TdWNjZXNzID0gdHJ1ZTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAoIWhhc1Nob3duU3VjY2VzcykgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyDmlK/ku5jmnKrlrozmiJDvvIzkuJTmnKrmmL7npLrov4fmiJDlip/mtojmga8KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbXNnKCLmlK/ku5jmnKrlrozmiJDvvIzlpoLlt7LmlK/ku5jor7fnqI3lkI7liLfmlrDmn6XnnIsiLCAid2FybmluZyIpOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuY2F0Y2goKCkgPT4gewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmICghaGFzU2hvd25TdWNjZXNzKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJG1zZygi5p+l6K+i5pSv5LuY54q25oCB5aSx6LSl77yM5aaC5bey5pSv5LuY6K+356iN5ZCO5Yi35paw5p+l55yLIiwgIndhcm5pbmciKTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8g5a6a5pyf5p+l6K+i5pSv5LuY54q25oCBCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kZ2V0KGBhcGkvYWxpcGF5L3F1ZXJ5LyR7b3JkZXJOb31gKQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAudGhlbihxdWVyeVJlcyA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAocXVlcnlSZXMuZGF0YS5zdGF0dXMgJiYgcXVlcnlSZXMuZGF0YS5kYXRhLnN0YXR1cyA9PT0gMSAmJiAhaGFzU2hvd25TdWNjZXNzKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8g5pSv5LuY5oiQ5Yqf5LiU5pyq5pi+56S66L+H5oiQ5Yqf5raI5oGvCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xlYXJJbnRlcnZhbChjaGVja1BheW1lbnQpOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMubmV3TGlzdCh0aGlzLnVzZXIuaWQpOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJG1zZygi5YWF5YC85oiQ5YqfIiwgInN1Y2Nlc3MiKTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYXNTaG93blN1Y2Nlc3MgPSB0cnVlOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmICghcGF5V2luZG93LmNsb3NlZCkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwYXlXaW5kb3cuY2xvc2UoKTsgLy8g6Ieq5Yqo5YWz6Zet5pSv5LuY56qX5Y+jCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuY2F0Y2goZXJyID0+IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+afpeivouaUr+S7mOeKtuaAgeWksei0pTonLCBlcnIpOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KTsKCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcG9sbENvdW50Kys7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LCBwb2xsSW50ZXJ2YWwpOwoKICAgICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJG1zZyhyZXMuZGF0YS5tc2cgfHwgIuWIm+W7uuaUr+S7mOiuouWNleWksei0pSIsICJlcnJvciIpOwogICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICAgICAgICAuY2F0Y2goZXJyID0+IHsKICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcign5Yib5bu65pSv5LuY6K6i5Y2V5aSx6LSlOicsIGVycik7CiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJG1zZygi5Yib5bu65pSv5LuY6K6i5Y2V5aSx6LSl77yM6K+356iN5ZCO6YeN6K+VIiwgImVycm9yIik7CiAgICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICAgICAgICAgIHRoaXMuJG1zZygi5bey5Y+W5raI5YWF5YC8IiwgImluZm8iKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgfSwKICAgICAgICBwZXJzb25hbEluZm9ybWF0aW9uKCkgewogICAgICAgICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlOwogICAgICAgICAgICB0aGlzLnJ1bGVGb3JtLnVzZXJuYW1lID0gdGhpcy51c2VyLnVzZXJuYW1lOwogICAgICAgICAgICB0aGlzLnJ1bGVGb3JtLnBob25lID0gdGhpcy51c2VyLnBob25lOwoKICAgICAgICAgICAgLy8g5aaC5p6c5piv57u05L+u5ZGY77yM5Yqg6L2957G75Yir5ZKM5a2Q57G75Yir5pWw5o2uCiAgICAgICAgICAgIGlmICh0aGlzLnVzZXIucm9sZSAmJiB0aGlzLnVzZXIucm9sZS5pZCA9PT0gMTMpIHsKICAgICAgICAgICAgICAgIC8vIOiuvue9ruW9k+WJjeeahOexu+WIq+WSjOWtkOexu+WIqwogICAgICAgICAgICAgICAgaWYgKHRoaXMudXNlci5kZXB0KSB7CiAgICAgICAgICAgICAgICAgICAgdGhpcy5ydWxlRm9ybS5kZXB0SWQgPSB0aGlzLnVzZXIuZGVwdC5pZDsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIGlmICh0aGlzLnVzZXIudHlwZSkgewogICAgICAgICAgICAgICAgICAgIHRoaXMucnVsZUZvcm0uY2xhc3NJZCA9IHRoaXMudXNlci50eXBlLmlkOwogICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgIC8vIOiuvue9rue6p+iBlOmAieaLqeWZqOeahOWIneWni+WAvAogICAgICAgICAgICAgICAgaWYgKHRoaXMudXNlci5kZXB0ICYmIHRoaXMudXNlci50eXBlKSB7CiAgICAgICAgICAgICAgICAgICAgdGhpcy5jYXRlZ29yeVZhbHVlID0gW3RoaXMudXNlci5kZXB0LmlkLCB0aGlzLnVzZXIudHlwZS5pZF07CiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKHRoaXMudXNlci5kZXB0KSB7CiAgICAgICAgICAgICAgICAgICAgdGhpcy5jYXRlZ29yeVZhbHVlID0gW3RoaXMudXNlci5kZXB0LmlkXTsKICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgICAgdGhpcy5jYXRlZ29yeVZhbHVlID0gW107CiAgICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgICAgLy8g6K6+572u5Zyw5Z2A6YCJ5oup5Zmo55qE5Yid5aeL5YC8CiAgICAgICAgICAgICAgICBpZiAodGhpcy51c2VyLnByb3ZpbmNlICYmIHRoaXMudXNlci5jaXR5ICYmIHRoaXMudXNlci5kaXN0cmljdCkgewogICAgICAgICAgICAgICAgICAgIHRoaXMuYWRkcmVzc1JlZ2lvbiA9IFt0aGlzLnVzZXIucHJvdmluY2UsIHRoaXMudXNlci5jaXR5LCB0aGlzLnVzZXIuZGlzdHJpY3RdOwogICAgICAgICAgICAgICAgICAgIHRoaXMucnVsZUZvcm0uYWRkcmVzcyA9IHRoaXMudXNlci5hZGRyZXNzIHx8ICcnOwogICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgICB0aGlzLmFkZHJlc3NSZWdpb24gPSBbXTsKICAgICAgICAgICAgICAgICAgICB0aGlzLnJ1bGVGb3JtLmFkZHJlc3MgPSAnJzsKICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICAvLyDliqDovb3miYDmnInlj6/nlKjnmoTnsbvliKvlkozlrZDnsbvliKsKICAgICAgICAgICAgICAgIHRoaXMubG9hZENhdGVnb3J5T3B0aW9ucygpOwogICAgICAgICAgICB9CiAgICAgICAgfSwKCiAgICAgICAgLy8g5Yqg6L2957G75Yir5ZKM5a2Q57G75Yir55qE57qn6IGU6YCJ6aG5CiAgICAgICAgbG9hZENhdGVnb3J5T3B0aW9ucygpIHsKICAgICAgICAgICAgLy8g5YWI6I635Y+W5omA5pyJ57G75YirCiAgICAgICAgICAgIHRoaXMuJGdldCgiL2RlcHQvbGlzdCIsIHsgcm9sZUlkOiAxMyB9KQogICAgICAgICAgICAudGhlbihyZXMgPT4gewogICAgICAgICAgICAgICAgaWYgKHJlcy5kYXRhLnN0YXR1cyAmJiByZXMuZGF0YS5kZXB0KSB7CiAgICAgICAgICAgICAgICAgICAgLy8g5L+d5a2Y5Y6f5aeL57G75Yir5YiX6KGo77yI5YW85a655oCn6ICD6JmR77yJCiAgICAgICAgICAgICAgICAgICAgdGhpcy5kZXB0T3B0aW9ucyA9IHJlcy5kYXRhLmRlcHQ7CiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ+WPr+eUqOeahOexu+WIq+WIl+ihqDonLCB0aGlzLmRlcHRPcHRpb25zKTsKCiAgICAgICAgICAgICAgICAgICAgLy8g5Li65q+P5Liq57G75Yir5Yqg6L295a2Q57G75YirCiAgICAgICAgICAgICAgICAgICAgY29uc3QgcHJvbWlzZXMgPSByZXMuZGF0YS5kZXB0Lm1hcChkZXB0ID0+IHsKICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHRoaXMuJGdldCgiL2NsYXNzL2xpc3QiLCB7IGRlcHRJZDogZGVwdC5pZCB9KQogICAgICAgICAgICAgICAgICAgICAgICAudGhlbihjbGFzc1JlcyA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoY2xhc3NSZXMuZGF0YS5zdGF0dXMgJiYgY2xhc3NSZXMuZGF0YS5jbGFzcykgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIOi/lOWbnuW4puacieWtkOexu+WIq+eahOexu+WIq+WvueixoQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLmRlcHQsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNoaWxkcmVuOiBjbGFzc1Jlcy5kYXRhLmNsYXNzCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIOWmguaenOayoeacieWtkOexu+WIq++8jOi/lOWbnuWOn+Wni+exu+WIq+WvueixoQogICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5kZXB0LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNoaWxkcmVuOiBbXQogICAgICAgICAgICAgICAgICAgICAgICAgICAgfTsKICAgICAgICAgICAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICAgICAgICAgICAgLmNhdGNoKCgpID0+IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIOWHuumUmeaXtui/lOWbnuayoeacieWtkOexu+WIq+eahOexu+WIq+WvueixoQogICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5kZXB0LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNoaWxkcmVuOiBbXQogICAgICAgICAgICAgICAgICAgICAgICAgICAgfTsKICAgICAgICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICAgICAgfSk7CgogICAgICAgICAgICAgICAgICAgIC8vIOetieW+heaJgOacieWtkOexu+WIq+WKoOi9veWujOaIkAogICAgICAgICAgICAgICAgICAgIFByb21pc2UuYWxsKHByb21pc2VzKQogICAgICAgICAgICAgICAgICAgIC50aGVuKGNhdGVnb3JpZXMgPT4gewogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmNhdGVnb3J5T3B0aW9ucyA9IGNhdGVnb3JpZXM7CiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfnuqfogZTpgInmi6nlmajpgInpobk6JywgdGhpcy5jYXRlZ29yeU9wdGlvbnMpOwogICAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgICB0aGlzLiRtc2coIuiOt+WPluexu+WIq+WIl+ihqOWksei0pSIsICJlcnJvciIpOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICB9KQogICAgICAgICAgICAuY2F0Y2goZXJyID0+IHsKICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluexu+WIq+WIl+ihqOWksei0pTonLCBlcnIpOwogICAgICAgICAgICAgICAgdGhpcy4kbXNnKCLojrflj5bnsbvliKvliJfooajlpLHotKXvvIzor7fnqI3lkI7ph43or5UiLCAiZXJyb3IiKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgfSwKCiAgICAgICAgLy8g5L+d55WZ5Y6f5pyJ55qE5pa55rOV77yI5YW85a655oCn6ICD6JmR77yJCiAgICAgICAgbG9hZERlcGFydG1lbnRzKCkgewogICAgICAgICAgICB0aGlzLiRnZXQoIi9kZXB0L2xpc3QiLCB7IHJvbGVJZDogMTMgfSkKICAgICAgICAgICAgLnRoZW4ocmVzID0+IHsKICAgICAgICAgICAgICAgIGlmIChyZXMuZGF0YS5zdGF0dXMgJiYgcmVzLmRhdGEuZGVwdCkgewogICAgICAgICAgICAgICAgICAgIHRoaXMuZGVwdE9wdGlvbnMgPSByZXMuZGF0YS5kZXB0OwogICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCflj6/nlKjnmoTnsbvliKvliJfooag6JywgdGhpcy5kZXB0T3B0aW9ucyk7CiAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICAgIHRoaXMuJG1zZygi6I635Y+W57G75Yir5YiX6KGo5aSx6LSlIiwgImVycm9yIik7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0pCiAgICAgICAgICAgIC5jYXRjaChlcnIgPT4gewogICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W57G75Yir5YiX6KGo5aSx6LSlOicsIGVycik7CiAgICAgICAgICAgICAgICB0aGlzLiRtc2coIuiOt+WPluexu+WIq+WIl+ihqOWksei0pe+8jOivt+eojeWQjumHjeivlSIsICJlcnJvciIpOwogICAgICAgICAgICB9KTsKICAgICAgICB9LAoKICAgICAgICAvLyDkv53nlZnljp/mnInnmoTmlrnms5XvvIjlhbzlrrnmgKfogIPomZHvvIkKICAgICAgICBsb2FkU3ViQ2F0ZWdvcmllcyhkZXB0SWQpIHsKICAgICAgICAgICAgaWYgKCFkZXB0SWQpIHJldHVybjsKCiAgICAgICAgICAgIHRoaXMuJGdldCgiL2NsYXNzL2xpc3QiLCB7IGRlcHRJZDogZGVwdElkIH0pCiAgICAgICAgICAgIC50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgICAgICBpZiAocmVzLmRhdGEuc3RhdHVzICYmIHJlcy5kYXRhLmNsYXNzKSB7CiAgICAgICAgICAgICAgICAgICAgdGhpcy5jbGFzc09wdGlvbnMgPSByZXMuZGF0YS5jbGFzczsKICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn5Y+v55So55qE5a2Q57G75Yir5YiX6KGoOicsIHRoaXMuY2xhc3NPcHRpb25zKTsKCiAgICAgICAgICAgICAgICAgICAgLy8g5aaC5p6c5b2T5YmN6YCJ5oup55qE5a2Q57G75Yir5LiN5Zyo5paw55qE5a2Q57G75Yir5YiX6KGo5Lit77yM5riF56m66YCJ5oupCiAgICAgICAgICAgICAgICAgICAgY29uc3QgZXhpc3RzID0gdGhpcy5jbGFzc09wdGlvbnMuc29tZShpdGVtID0+IGl0ZW0uaWQgPT09IHRoaXMucnVsZUZvcm0uY2xhc3NJZCk7CiAgICAgICAgICAgICAgICAgICAgaWYgKCFleGlzdHMpIHsKICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5ydWxlRm9ybS5jbGFzc0lkID0gbnVsbDsKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICAgIHRoaXMuY2xhc3NPcHRpb25zID0gW107CiAgICAgICAgICAgICAgICAgICAgdGhpcy5ydWxlRm9ybS5jbGFzc0lkID0gbnVsbDsKICAgICAgICAgICAgICAgICAgICB0aGlzLiRtc2coIuiOt+WPluWtkOexu+WIq+WIl+ihqOWksei0pSIsICJlcnJvciIpOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICB9KQogICAgICAgICAgICAuY2F0Y2goZXJyID0+IHsKICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluWtkOexu+WIq+WIl+ihqOWksei0pTonLCBlcnIpOwogICAgICAgICAgICAgICAgdGhpcy5jbGFzc09wdGlvbnMgPSBbXTsKICAgICAgICAgICAgICAgIHRoaXMucnVsZUZvcm0uY2xhc3NJZCA9IG51bGw7CiAgICAgICAgICAgICAgICB0aGlzLiRtc2coIuiOt+WPluWtkOexu+WIq+WIl+ihqOWksei0pe+8jOivt+eojeWQjumHjeivlSIsICJlcnJvciIpOwogICAgICAgICAgICB9KTsKICAgICAgICB9LAogICAgICAgIC8vIHN1Ym1pdENoYW5nZXMoKXsKICAgICAgICAvLyAgICAgdGhpcy4kcHV0KCIvdXNlciIse2lkOnRoaXMudXNlci5pZCx1c2VybmFtZTp0aGlzLnJ1bGVGb3JtLnVzZXJuYW1lLHBob25lOnRoaXMucnVsZUZvcm0ucGhvbmV9KQogICAgICAgIC8vICAgICAudGhlbihyZXM9PnsKICAgICAgICAvLyAgICAgICAgIHRoaXMuJG5vdGlmeU1zZygi5oiQ5YqfIixyZXMuZGF0YS5tc2csInN1Y2Nlc3MiLDEwMDApOwogICAgICAgIC8vICAgICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2U7CiAgICAgICAgLy8gICAgICAgICB0aGlzLm5ld0xpc3QodGhpcy51c2VyLmlkKQogICAgICAgIC8vICAgICB9KQogICAgICAgIC8vIH0sCiAgICAgICAgc3VibWl0Q2hhbmdlcygpIHsKICAgICAgICAgICAgLy8g5YeG5aSH6KaB5pu05paw55qE55So5oi35pWw5o2uCiAgICAgICAgICAgIGNvbnN0IHVzZXJEYXRhID0gewogICAgICAgICAgICAgICAgaWQ6IHRoaXMudXNlci5pZCwKICAgICAgICAgICAgICAgIHVzZXJuYW1lOiB0aGlzLnJ1bGVGb3JtLnVzZXJuYW1lLAogICAgICAgICAgICAgICAgcGhvbmU6IHRoaXMucnVsZUZvcm0ucGhvbmUsCiAgICAgICAgICAgICAgICBzdGF0ZTogMQogICAgICAgICAgICB9OwoKICAgICAgICAgICAgLy8g5aaC5p6c5piv57u05L+u5ZGY77yM5re75Yqg57G75Yir5ZKM5a2Q57G75Yir5L+h5oGvCiAgICAgICAgICAgIGlmICh0aGlzLnVzZXIucm9sZSAmJiB0aGlzLnVzZXIucm9sZS5pZCA9PT0gMTMpIHsKICAgICAgICAgICAgICAgIC8vIOS7jue6p+iBlOmAieaLqeWZqOS4reiOt+WPluexu+WIq+WSjOWtkOexu+WIq0lECiAgICAgICAgICAgICAgICBpZiAodGhpcy5jYXRlZ29yeVZhbHVlICYmIHRoaXMuY2F0ZWdvcnlWYWx1ZS5sZW5ndGggPiAwKSB7CiAgICAgICAgICAgICAgICAgICAgLy8g56ys5LiA5Liq5YC85piv57G75YirSUQKICAgICAgICAgICAgICAgICAgICB1c2VyRGF0YS5kZXB0SWQgPSB0aGlzLmNhdGVnb3J5VmFsdWVbMF07CgogICAgICAgICAgICAgICAgICAgIC8vIOWmguaenOmAieaLqeS6huWtkOexu+WIq++8iOaVsOe7hOmVv+W6puWkp+S6jjHvvInvvIznrKzkuozkuKrlgLzmmK/lrZDnsbvliKtJRAogICAgICAgICAgICAgICAgICAgIGlmICh0aGlzLmNhdGVnb3J5VmFsdWUubGVuZ3RoID4gMSkgewogICAgICAgICAgICAgICAgICAgICAgICB1c2VyRGF0YS5jbGFzc0lkID0gdGhpcy5jYXRlZ29yeVZhbHVlWzFdOwogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIC8vIOWFvOWuueaXp+eJiOacrOeahOmAieaLqeaWueW8jwogICAgICAgICAgICAgICAgZWxzZSB7CiAgICAgICAgICAgICAgICAgICAgLy8g5Y+q5pyJ5b2T6YCJ5oup5LqG57G75Yir5pe25omN5pu05paw57G75YirSUQKICAgICAgICAgICAgICAgICAgICBpZiAodGhpcy5ydWxlRm9ybS5kZXB0SWQpIHsKICAgICAgICAgICAgICAgICAgICAgICAgdXNlckRhdGEuZGVwdElkID0gdGhpcy5ydWxlRm9ybS5kZXB0SWQ7CiAgICAgICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgICAgICAvLyDlj6rmnInlvZPpgInmi6nkuoblrZDnsbvliKvml7bmiY3mm7TmlrDlrZDnsbvliKtJRAogICAgICAgICAgICAgICAgICAgIGlmICh0aGlzLnJ1bGVGb3JtLmNsYXNzSWQpIHsKICAgICAgICAgICAgICAgICAgICAgICAgdXNlckRhdGEuY2xhc3NJZCA9IHRoaXMucnVsZUZvcm0uY2xhc3NJZDsKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgICAgLy8g5aSE55CG5Zyw5Z2A5L+h5oGvCiAgICAgICAgICAgICAgICBpZiAodGhpcy5hZGRyZXNzUmVnaW9uICYmIHRoaXMuYWRkcmVzc1JlZ2lvbi5sZW5ndGggPT09IDMpIHsKICAgICAgICAgICAgICAgICAgICB1c2VyRGF0YS5wcm92aW5jZSA9IHRoaXMuYWRkcmVzc1JlZ2lvblswXTsKICAgICAgICAgICAgICAgICAgICB1c2VyRGF0YS5jaXR5ID0gdGhpcy5hZGRyZXNzUmVnaW9uWzFdOwogICAgICAgICAgICAgICAgICAgIHVzZXJEYXRhLmRpc3RyaWN0ID0gdGhpcy5hZGRyZXNzUmVnaW9uWzJdOwogICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgIC8vIOWmguaenOacieivpue7huWcsOWdgO+8jOa3u+WKoOWIsOabtOaWsOaVsOaNruS4rQogICAgICAgICAgICAgICAgaWYgKHRoaXMucnVsZUZvcm0uYWRkcmVzcykgewogICAgICAgICAgICAgICAgICAgIHVzZXJEYXRhLmFkZHJlc3MgPSB0aGlzLnJ1bGVGb3JtLmFkZHJlc3M7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KCiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfmj5DkuqTnmoTnlKjmiLfmlbDmja46JywgdXNlckRhdGEpOwoKICAgICAgICAgICAgLy8g5Y+R6YCB5pu05paw6K+35rGCCiAgICAgICAgICAgIHRoaXMuJHB1dCgiL3VzZXIiLCB1c2VyRGF0YSkKICAgICAgICAgICAgICAgIC50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgICAgICAgICAgaWYgKHJlcy5kYXRhLnN0YXR1cykgewogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRub3RpZnlNc2coIuaIkOWKnyIsIHJlcy5kYXRhLm1zZywgInN1Y2Nlc3MiKTsKICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2U7CiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMubmV3TGlzdCh0aGlzLnVzZXIuaWQpOwogICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJG1zZyhyZXMuZGF0YS5tc2cgfHwgIuabtOaWsOWksei0pSIsICJlcnJvciIpOwogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgICAuY2F0Y2goZXJyID0+IHsKICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCfmm7TmlrDnlKjmiLfkv6Hmga/lpLHotKU6JywgZXJyKTsKICAgICAgICAgICAgICAgICAgICB0aGlzLiRtc2coIuabtOaWsOWksei0pe+8jOivt+eojeWQjumHjeivlSIsICJlcnJvciIpOwogICAgICAgICAgICAgICAgfSk7CiAgICAgICAgfSwKICAgICAgICAvL+agueaNruW9k+WJjeeUqOaIt+afpeivomlkCiAgICAgICAgbmV3TGlzdChpZCkgewogICAgICAgICAgICB0aGlzLiRnZXQoIi91c2VyLyIgKyBpZCkKICAgICAgICAgICAgLnRoZW4oKHJzKSA9PiB7CiAgICAgICAgICAgICAgICAvLyDnoa7kv53nlKjmiLfnirbmgIHmraPnoa7mm7TmlrAKICAgICAgICAgICAgICAgIGlmIChycy5kYXRhLnVzZXIpIHsKICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygnVXNlciBkYXRhIGZyb20gQVBJOicsIEpTT04uc3RyaW5naWZ5KHJzLmRhdGEudXNlciwgbnVsbCwgMikpOwogICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdVc2VyIHJvbGUgSUQ6JywgcnMuZGF0YS51c2VyLnJvbGUgPyBycy5kYXRhLnVzZXIucm9sZS5pZCA6ICdObyByb2xlJyk7CiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ1VzZXIgZGVwdDonLCBycy5kYXRhLnVzZXIuZGVwdCA/IEpTT04uc3RyaW5naWZ5KHJzLmRhdGEudXNlci5kZXB0LCBudWxsLCAyKSA6ICdObyBkZXB0IGRhdGEnKTsKICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygnVXNlciB0eXBlOicsIHJzLmRhdGEudXNlci50eXBlID8gSlNPTi5zdHJpbmdpZnkocnMuZGF0YS51c2VyLnR5cGUsIG51bGwsIDIpIDogJ05vIHR5cGUgZGF0YScpOwoKICAgICAgICAgICAgICAgICAgICAvLyDlpoLmnpzmmK/nu7Tkv67lkZjkvYbmsqHmnIl0eXBl5L+h5oGv77yM5bCd6K+V6YeN5paw6I635Y+W55So5oi35pWw5o2uCiAgICAgICAgICAgICAgICAgICAgaWYgKHJzLmRhdGEudXNlci5yb2xlICYmIHJzLmRhdGEudXNlci5yb2xlLmlkID09PSAxMyAmJiAhcnMuZGF0YS51c2VyLnR5cGUpIHsKICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ+e7tOS/ruWRmOayoeaciXR5cGXkv6Hmga/vvIzlsJ3or5Xkv67lpI0uLi4nKTsKICAgICAgICAgICAgICAgICAgICAgICAgLy8g5bCd6K+V5omL5Yqo6K6+572udHlwZeS/oeaBrwogICAgICAgICAgICAgICAgICAgICAgICBpZiAocnMuZGF0YS51c2VyLmNsYXNzSWQpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfmib7liLBjbGFzc0lkOicsIHJzLmRhdGEudXNlci5jbGFzc0lkKTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIOiOt+WPlnR5cGXkv6Hmga8KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJGdldCgiL2NsYXNzLyIgKyBycy5kYXRhLnVzZXIuY2xhc3NJZCkKICAgICAgICAgICAgICAgICAgICAgICAgICAgIC50aGVuKHR5cGVSZXMgPT4gewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmICh0eXBlUmVzLmRhdGEuY2xhc3MpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ+iOt+WPluWIsHR5cGXkv6Hmga86JywgSlNPTi5zdHJpbmdpZnkodHlwZVJlcy5kYXRhLmNsYXNzLCBudWxsLCAyKSk7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIOaJi+WKqOiuvue9rnR5cGXkv6Hmga8KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcnMuZGF0YS51c2VyLnR5cGUgPSB0eXBlUmVzLmRhdGEuY2xhc3M7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIOabtOaWsOeUqOaIt+S/oeaBrwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXNzaW9uU3RvcmFnZS5zZXRJdGVtKCJ1c2VyIiwgSlNPTi5zdHJpbmdpZnkocnMuZGF0YS51c2VyKSk7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuc2V0VXNlcihycy5kYXRhLnVzZXIpOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgICAgICAvLyDlpoLmnpznlKjmiLflt7LlrozlloTkv6Hmga/vvIznoa7kv53nirbmgIHkuLoxCiAgICAgICAgICAgICAgICAgICAgaWYgKHJzLmRhdGEudXNlci5kZXB0ICYmIHJzLmRhdGEudXNlci5zdGF0ZSA9PT0gMCkgewogICAgICAgICAgICAgICAgICAgICAgICBycy5kYXRhLnVzZXIuc3RhdGUgPSAxOwogICAgICAgICAgICAgICAgICAgICAgICAvLyDmm7TmlrDnlKjmiLfnirbmgIEKICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kcHV0KCIvdXNlciIsIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkOiBycy5kYXRhLnVzZXIuaWQsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdGF0ZTogMQogICAgICAgICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgc2Vzc2lvblN0b3JhZ2Uuc2V0SXRlbSgidXNlciIsIEpTT04uc3RyaW5naWZ5KHJzLmRhdGEudXNlcikpCiAgICAgICAgICAgICAgICAgICAgdGhpcy5zZXRVc2VyKEpTT04ucGFyc2Uoc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgidXNlciIpKSkKICAgICAgICAgICAgICAgICAgICAvLyDkv67mlLnlrozlkI3lrZcsIOa4heepuueVtuWJjWZpcnN0TmFtZTsg6YG/5YWN5Ye654++55aK5YqgCiAgICAgICAgICAgICAgICAgICAgdGhpcy5maXJzdE5hbWUgPSAnJzsKICAgICAgICAgICAgICAgICAgICB0aGlzLnRleHRBdmF0YXIocnMuZGF0YS51c2VyLnVzZXJuYW1lKTsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSkKICAgICAgICB9LAogICAgICAgIGV4aXQoKXsKICAgICAgICAgICAgc2Vzc2lvblN0b3JhZ2UucmVtb3ZlSXRlbSgndXNlcicpOwogICAgICAgICAgICB0aGlzLiRyb3V0ZXIucHVzaCgnLycpCiAgICAgICAgfSwKICAgICAgICAvLyDmloflrZfpoK3lg48KICAgICAgICB0ZXh0QXZhdGFyKHVzZXJuYW1lKSB7CiAgICAgICAgICAgIGxldCBhcnIgPSB1c2VybmFtZS5zcGxpdCgnICcpOwogICAgICAgICAgICBmb3IgKHZhciBpIGluIGFycikgewogICAgICAgICAgICAgICAgdGhpcy5maXJzdE5hbWUgKz0gYXJyW2ldLnN1YnN0cigwLDEpOwogICAgICAgICAgICB9CiAgICAgICAgICAgIHRoaXMuZmlyc3ROYW1lID0gdGhpcy5maXJzdE5hbWUudG9Mb2NhbGVVcHBlckNhc2UoKTsKICAgICAgICAgICAgY29uc29sZS5sb2coJ2ZpcnN0TmFtZS0+JyArIHRoaXMuZmlyc3ROYW1lKTsKICAgICAgICB9LAoKICAgICAgICAvLyDojrflj5bmnKror7vpgJrnn6XmlbDph48KICAgICAgICBnZXRVbnJlYWROb3RpZmljYXRpb25Db3VudCgpIHsKICAgICAgICAgICAgaWYgKHRoaXMudXNlciAmJiB0aGlzLnVzZXIuaWQpIHsKICAgICAgICAgICAgICAgIHRoaXMuJGdldCgnL2ZvcnVtL25vdGlmaWNhdGlvbi91bnJlYWQvY291bnQnLCB7IHVzZXJJZDogdGhpcy51c2VyLmlkIH0pCiAgICAgICAgICAgICAgICAudGhlbihyZXMgPT4gewogICAgICAgICAgICAgICAgICAgIGlmIChyZXMuZGF0YS5zdGF0dXMpIHsKICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy51bnJlYWRDb3VudCA9IHJlcy5kYXRhLmNvdW50OwogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgICAuY2F0Y2goZXJyID0+IHsKICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bmnKror7vpgJrnn6XmlbDph4/lpLHotKU6JywgZXJyKTsKICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9CiAgICAgICAgfQogICAgfSwKICAgIGNvbXB1dGVkOiB7CiAgICAgICAgLi4ubWFwU3RhdGUoJ3VzZXInLCBbJ3VzZXInXSksCiAgICAgICAgdGhlbWUoKXsKICAgICAgICAgICAgcmV0dXJuIHRoaXMuJHN0b3JlLnN0YXRlLnRoZW1lLnRoZW1lCiAgICAgICAgfSwKICAgIH0sCiAgICBkYXRhKCkgewogICAgICAgIHZhciB2YWxpZGF0ZVVzZXJuYW1lID0gKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgPT4gewogICAgICAgICAgICBpZiAodmFsdWUgPT09ICcnKSB7CiAgICAgICAgICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoJ+ivt+i+k+WFpeWnk+WQjScpKTsKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgIGNhbGxiYWNrKCk7CiAgICAgICAgICAgIH0KICAgICAgICB9OwogICAgICAgIHZhciB2YWxpZGF0ZVBob25lID0gKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgPT4gewogICAgICAgICAgICBpZiAodmFsdWUgPT09ICcnKSB7CiAgICAgICAgICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoJ+ivt+i+k+WFpeaJi+acuuWPtycpKTsKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgIGNhbGxiYWNrKCk7CiAgICAgICAgICAgIH0KICAgICAgICB9OwoKICAgICAgICByZXR1cm4gewogICAgICAgICAgICAvLyDmloflrZflpLTlg48KICAgICAgICAgICAgZmlyc3ROYW1lOicnLAogICAgICAgICAgICBydWxlRm9ybTogewogICAgICAgICAgICAgICAgdXNlcm5hbWU6ICcnLAogICAgICAgICAgICAgICAgcGhvbmU6ICcnLAogICAgICAgICAgICAgICAgZGVwdElkOiBudWxsLAogICAgICAgICAgICAgICAgY2xhc3NJZDogbnVsbCwKICAgICAgICAgICAgICAgIGFkZHJlc3M6ICcnCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIC8vIOe6p+iBlOmAieaLqeWZqOeahOWAvOWSjOmAiemhuQogICAgICAgICAgICBjYXRlZ29yeVZhbHVlOiBbXSwKICAgICAgICAgICAgY2F0ZWdvcnlPcHRpb25zOiBbXSwKICAgICAgICAgICAgLy8g5L+d55WZ5Y6f5pyJ55qE6YCJ6aG577yI5YW85a655oCn6ICD6JmR77yJCiAgICAgICAgICAgIGRlcHRPcHRpb25zOiBbXSwKICAgICAgICAgICAgY2xhc3NPcHRpb25zOiBbXSwKCiAgICAgICAgICAgIC8vIOWcsOWdgOebuOWFswogICAgICAgICAgICBhZGRyZXNzUmVnaW9uOiBbXSwKICAgICAgICAgICAgcmVnaW9uT3B0aW9uczogW10sCiAgICAgICAgICAgIHJ1bGVzOiB7CiAgICAgICAgICAgICAgICB1c2VybmFtZTogWwogICAgICAgICAgICAgICAgICAgIHt2YWxpZGF0b3I6IHZhbGlkYXRlVXNlcm5hbWUsIHRyaWdnZXI6ICdibHVyJ30KICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICBwaG9uZTogWwogICAgICAgICAgICAgICAgICAgIHt2YWxpZGF0b3I6IHZhbGlkYXRlUGhvbmUsIHRyaWdnZXI6ICdibHVyJ30KICAgICAgICAgICAgICAgIF0KICAgICAgICAgICAgfSwKICAgICAgICAgICAgLy/popzoibIKICAgICAgICAgICAgdGhlbWVDb2xvciA6IHsnYmcnOicjZmZmJywnY29sb3InOicjMDAwJ30sCiAgICAgICAgICAgIC8v5oCn5YirCiAgICAgICAgICAgIHNleDonMCcsCiAgICAgICAgICAgIGRyYXdlcjogZmFsc2UsCiAgICAgICAgICAgIC8v5b2T5YmN6Lev55SxCiAgICAgICAgICAgIGJyZWFkTGlzdDogW10sCiAgICAgICAgICAgIC8v5b2T5YmN5bGP5bmV5a695bqmCiAgICAgICAgICAgIHdpbmRvd1dpZHRoOiBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuY2xpZW50V2lkdGgsCiAgICAgICAgICAgIGFjdGl2ZUluZGV4OiAnMScsCiAgICAgICAgICAgIC8v5o6n5Yi26I+c5Y2V5piv5ZCm5bGV5byACiAgICAgICAgICAgIGlzQ29sbGFwc2U6IGZhbHNlLAogICAgICAgICAgICBhZG1pbjogIiIsCiAgICAgICAgICAgIC8vIHJvbGU6IFtdLAogICAgICAgICAgICByb2xlOltdLAogICAgICAgICAgICAvL+e6p+iBlOmAieaLqeWZqOeahOWAvAogICAgICAgICAgICB2YWx1ZTogIiIsCiAgICAgICAgICAgIGRpYWxvZ1Zpc2libGU6ZmFsc2UsCiAgICAgICAgICAgIC8vIOacquivu+mAmuefpeaVsOmHjwogICAgICAgICAgICB1bnJlYWRDb3VudDogMAogICAgICAgIH0KICAgIH0sCiAgICB3YXRjaDogewogICAgICAgICckcm91dGUnKHRvLCBmb3JtKSB7CiAgICAgICAgICAgIHRoaXMuZ2V0QnJlYWRjcnVtYigpCiAgICAgICAgfQogICAgfSwKICAgIGNyZWF0ZWQoKSB7CiAgICAgICAgLy8g5Yid5aeL5YyW5Zyw5Z2A5pWw5o2uCiAgICAgICAgdGhpcy5yZWdpb25PcHRpb25zID0gcmVnaW9uRGF0YTsKCiAgICAgICAgbGV0IHRoZW1lID0gSlNPTi5wYXJzZShzZXNzaW9uU3RvcmFnZS5nZXRJdGVtKCJ0aGVtZUNvbG9yIikpOwogICAgICAgIGlmICh0aGVtZSl7CiAgICAgICAgICAgIHRoaXMudGhlbWVDb2xvciA9IHsnYmcnOnRoZW1lLnZhbHVlLCdjb2xvcic6dGhlbWUuY29sb3J9CiAgICAgICAgfQoKICAgICAgICBpZiAoc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgndXNlcicpKXsKICAgICAgICAgICAgdGhpcy5nZXRCcmVhZGNydW1iKCk7CiAgICAgICAgICAgIGNvbnN0IHVzZXJEYXRhID0gSlNPTi5wYXJzZShzZXNzaW9uU3RvcmFnZS5nZXRJdGVtKCJ1c2VyIikpOwogICAgICAgICAgICBjb25zb2xlLmxvZygnVXNlciBkYXRhIGZyb20gc2Vzc2lvbjonLCB1c2VyRGF0YSk7CiAgICAgICAgICAgIHRoaXMuc2V0VXNlcih1c2VyRGF0YSk7CgogICAgICAgICAgICAvLyDlpoLmnpzmmK/nu7Tkv67lkZjmiJbnrqHnkIblkZjvvIzojrflj5bmnKror7vpgJrnn6XmlbDph48KICAgICAgICAgICAgaWYgKHRoaXMudXNlci5yb2xlICYmICh0aGlzLnVzZXIucm9sZS5pZCA9PT0gMTMgfHwgdGhpcy51c2VyLnJvbGUuaWQgPT09IDE0KSkgewogICAgICAgICAgICAgICAgdGhpcy5nZXRVbnJlYWROb3RpZmljYXRpb25Db3VudCgpOwoKICAgICAgICAgICAgICAgIC8vIOavj+WIhumSn+iOt+WPluS4gOasoeacquivu+mAmuefpeaVsOmHjwogICAgICAgICAgICAgICAgc2V0SW50ZXJ2YWwoKCkgPT4gewogICAgICAgICAgICAgICAgICAgIHRoaXMuZ2V0VW5yZWFkTm90aWZpY2F0aW9uQ291bnQoKTsKICAgICAgICAgICAgICAgIH0sIDYwMDAwKTsKICAgICAgICAgICAgfQogICAgICAgICAgICAvLwogICAgICAgICAgICAvLyDmo4Dmn6XnlKjmiLfmmK/lkKbmmK/pppbmrKHnmbvlvZXvvIjpgJrov4fmo4Dmn6VzdGF0ZeWtl+aute+8iQogICAgICAgICAgICAvLyBzdGF0ZeS4ujDooajnpLrmnKrlrozlloTkv6Hmga/vvIxzdGF0ZeS4ujHooajnpLrlt7LlrozlloTkv6Hmga8KICAgICAgICAgICAgaWYgKHRoaXMudXNlci5zdGF0ZSA9PT0gMCkgewogICAgICAgICAgICAgICAgdGhpcy4kZ2V0KCIvcm9sZS8iICsgdGhpcy51c2VyLnJvbGUuaWQpCiAgICAgICAgICAgICAgICAudGhlbigocmVzKSA9PiB7CiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2cocmVzLmRhdGEpCiAgICAgICAgICAgICAgICAgICAgdGhpcy5yb2xlID0gcmVzLmRhdGEucm9sZTsKICAgICAgICAgICAgICAgICAgICB0aGlzLmRyYXdlciA9IHRydWUKICAgICAgICAgICAgICAgICAgICB0aGlzLiRtc2coIummluasoeeZu+W9le+8jOivt+WujOWWhOS/oeaBryIsICJ3YXJuaW5nIikKICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgIH0KICAgICAgICB9ZWxzZSB7CiAgICAgICAgICAgIHRoaXMuJG1zZygi5oKo5ZCR5pyq55m76ZmGLOayoeacieadg+mZkCIsImVycm9yIikKICAgICAgICAgICAgdGhpcy4kcm91dGVyLnB1c2goIi8iKQogICAgICAgIH0KCiAgICB9LAogICAgbW91bnRlZCgpIHsKICAgICAgICAvLyDojrflj5blvZPliY3lsY/luZXlrr3luqYKICAgICAgICB3aW5kb3cub25yZXNpemUgPSAoKSA9PiB7CiAgICAgICAgICAgIHRoaXMud2luZG93V2lkdGggPSBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuY2xpZW50V2lkdGgKICAgICAgICB9CiAgICAgICAgLy8g5paH5a2X6aCt5YOPCiAgICAgICAgdGhpcy50ZXh0QXZhdGFyKHRoaXMudXNlci51c2VybmFtZSk7CiAgICB9Cn0K"}, {"version": 3, "sources": ["Home.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6QA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Home.vue", "sourceRoot": "src/views/user", "sourcesContent": ["<template>\n    <div class=\"main\">\n        <div class=\"left\" :style=\"{width:isCollapse?'64px':'200px',background:themeColor.bg,color:themeColor.color}\" style=\"transition: .3s;\">\n            <div class=\"logo\">\n<!--                <img src=\"@s/assets/logo.jpg\" style=\"width: 26%\">-->\n            </div>\n            <el-menu\n                    :collapse-transition=\"false\"\n                    :collapse=\"isCollapse\"\n                    :router=\"true\"\n                    :default-active=\"$route.path\"\n                    :background-color=\"themeColor.bg\"\n                    :text-color=\"themeColor.color\"\n                    :unique-opened=\"true\">\n                <el-menu-item index=\"/home/\">\n                    <i class=\"el-icon-s-home\"></i>\n                    <span>首页</span>\n                </el-menu-item>\n\n                <el-submenu index=\"1\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-office-building\"></i>\n                        <span>任务管理</span>\n                    </template>\n                    <el-menu-item v-if=\"user.role.id === 14\" index=\"/home/<USER>\">\n                        <i class=\"el-icon-s-order\"></i>\n                        <span>发布任务</span>\n                    </el-menu-item>\n                    <el-menu-item v-if=\"user.role.id === 13\" index=\"/home/<USER>\">\n                        <i class=\"el-icon-s-order\"></i>\n                        <span>接受任务</span>\n                    </el-menu-item>\n                </el-submenu>\n\n\n                <el-submenu index=\"2\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-paperclip\"></i>\n                        <span>订单管理</span>\n                    </template>\n                    <el-menu-item v-if=\"user.role.id === 14\" index=\"/home/<USER>\">\n                        <i class=\"el-icon-s-order\"></i>\n                        <span>已发布任务</span>\n                    </el-menu-item>\n\n                    <el-menu-item v-if=\"user.role.id === 13\" index=\"/home/<USER>\">\n                        <i class=\"el-icon-s-order\"></i>\n                        <span>已接受任务</span>\n                    </el-menu-item>\n                </el-submenu>\n\n\n                <el-submenu index=\"3\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-s-custom\"></i>\n                        <span>公告管理</span>\n                    </template>\n                    <el-menu-item index=\"/home/<USER>\">\n                    <i class=\"el-icon-s-order\"></i>\n                        <span>查看公告</span>\n                    </el-menu-item>\n                </el-submenu>\n\n                <el-submenu index=\"9\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-s-custom\"></i>\n                        <span>评价管理</span>\n                    </template>\n                     <el-menu-item v-if=\"user.role.id === 14\" index=\"/home/<USER>\">\n                        <i class=\"el-icon-s-order\"></i>\n                        <span>我发布的评价</span>\n                    </el-menu-item>\n                    <el-menu-item v-if=\"user.role.id === 13\" index=\"/home/<USER>\">\n                    <i class=\"el-icon-s-order\"></i>\n                    <span>查看评价</span>\n                    </el-menu-item>\n                </el-submenu>\n\n                <!-- 论坛入口，只对维修员和管理员可见，用户角色ID是14 -->\n                <el-submenu index=\"10\" v-if=\"user.role.id !== 14\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-chat-dot-square\"></i>\n                        <span>维修员论坛</span>\n                    </template>\n                    <el-menu-item index=\"/home/<USER>/posts\">\n                        <i class=\"el-icon-document\"></i>\n                        <span>帖子列表</span>\n                    </el-menu-item>\n                    <el-menu-item index=\"/home/<USER>/my-posts\">\n                        <i class=\"el-icon-s-order\"></i>\n                        <span>我的帖子</span>\n                    </el-menu-item>\n                    <!-- 管理员角色不是14（用户角色）也不是13（维修员角色） -->\n                    <el-menu-item v-if=\"user.role.id !== 14 && user.role.id !== 13\" index=\"/home/<USER>/audit\">\n                        <i class=\"el-icon-s-check\"></i>\n                        <span>帖子审核</span>\n                    </el-menu-item>\n                    <el-menu-item index=\"/home/<USER>/notifications\">\n                        <i class=\"el-icon-bell\"></i>\n                        <span>消息通知</span>\n                        <el-badge v-if=\"unreadCount > 0\" :value=\"unreadCount\" class=\"notification-badge\" />\n                    </el-menu-item>\n                </el-submenu>\n\n                <el-submenu index=\"11\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-s-custom\"></i>\n                        <span>个人中心</span>\n                    </template>\n                    <el-menu-item index=\"/home/<USER>\">个人信息\n                    </el-menu-item>\n                </el-submenu>\n\n                <el-menu-item v-if=\"user.role.id === 14\">\n                    <span>当前余额: {{ user.balance }}元</span>\n                    <!-- <el-button type=\"text\" @click=\"recharge(user.studentId)\">充值余额</el-button> -->\n                </el-menu-item>\n            </el-menu>\n        </div>\n\n        <div class=\"right\"\n             :style=\"{width:isCollapse?windowWidth-64+'px':windowWidth-200+'px',left:isCollapse?'64px':'200px'}\">\n            <div class=\"top\"\n                 :style=\"{width:isCollapse?windowWidth-64+'px':windowWidth-200+'px',left:isCollapse?'64px':'200px',background:themeColor.bg}\">\n                <div class=\"icon\" @click=\"isCollapse = !isCollapse\">\n                    <i :class=\"isCollapse?'el-icon-s-unfold':'el-icon-s-fold'\" :style=\"{color:themeColor.color}\"></i>\n                </div>\n                <el-menu\n\n                        :unique-opened=\"true\"\n                        :default-active=\"activeIndex\"\n                        class=\"el-menu-demo\"\n                        mode=\"horizontal\"\n                        :background-color=\"themeColor.bg\"\n                        :text-color=\"themeColor.color\"\n                        :active-text-color=\"themeColor.color\"\n                        menu-trigger=\"click\">\n\n                    <el-menu-item v-if=\"user.role.id === 14\" @click=\"recharge(user.studentId)\">充值余额</el-menu-item>\n<!--                    <el-menu-item @click=\"recharge(user.studentId)\">充值余额</el-menu-item>-->\n\n                    <el-submenu index=\"1\">\n                        <template slot=\"title\">更换主题</template>\n                        <el-menu-item v-for=\"item in theme\" @click=\"changeColor(item)\">\n                            {{item.name}}\n                        </el-menu-item>\n                    </el-submenu>\n                    <el-submenu index=\"2\">\n<!--                        <template slot=\"title\">{{user.username}}</template>-->\n                        <el-avatar slot=\"title\" style=\"background: #65c4a6; user-select: none;\">{{firstName}}</el-avatar>\n                        <el-menu-item index=\"2-1\" @click=\"exit\">退出</el-menu-item>\n                        <el-menu-item index=\"2-2\" @click=\"updPassword(user.id)\">修改密码</el-menu-item>\n                        <el-menu-item index=\"2-3\" @click=\"personalInformation()\">修改个人信息</el-menu-item>\n                    </el-submenu>\n                </el-menu>\n\n            </div>\n            <div class=\"bottom\">\n                <div class=\"bottom_top\">\n                    <el-breadcrumb separator-class=\"el-icon-arrow-right\">\n                        <el-breadcrumb-item v-for=\"item in breadList\" :to=\"item.path\" v-if=\"item.meta.title\">\n                            {{item.meta.title}}\n                        </el-breadcrumb-item>\n                    </el-breadcrumb>\n                </div>\n                <transition name=\"el-fade-in\" mode=\"out-in\">\n                    <router-view @personalInformation=\"personalInformation\"></router-view>\n                </transition>\n            </div>\n        </div>\n\n        <el-drawer\n                title=\"完善信息\"\n                :visible.sync=\"drawer\"\n                direction=\"rtl\"\n                closeDrawer=\"false\"\n                :show-close=\"false\"\n                :before-close=\"handleClose\">\n            <el-form :model=\"ruleForm\" status-icon :rules=\"rules\" ref=\"ruleForm\" label-width=\"100px\"\n                     class=\"demo-ruleForm ruleform\">\n\n                <!-- 根据角色 ID 动态显示班级信息 -->\n                <el-form-item label=\"类别\" v-if=\"user.role.id !== 14\">\n                    <el-cascader\n                            v-model=\"value\"\n                            :options=\"role.depts\"\n                            :props=\"{\n                children:'classes',\n                label:'name',\n                value:'id'\n            }\"\n                    ></el-cascader>\n                </el-form-item>\n\n                <el-form-item label=\"姓名\" prop=\"username\">\n                    <el-input v-model=\"ruleForm.username\"></el-input>\n                </el-form-item>\n                <el-form-item label=\"工号\" prop=\"phone\">\n                    <el-input v-model.number=\"ruleForm.phone\"></el-input>\n                </el-form-item>\n\n                <el-form-item label=\"性别\">\n                    <el-radio-group v-model=\"sex\">\n                        <el-radio label=\"0\">男</el-radio>\n                        <el-radio label=\"1\">女</el-radio>\n                    </el-radio-group>\n                </el-form-item>\n\n                <el-form-item>\n                    <el-button type=\"primary\" @click=\"submitForm('ruleForm')\">提交</el-button>\n                </el-form-item>\n            </el-form>\n        </el-drawer>\n\n        <el-dialog title=\"修改信息\" :visible.sync=\"dialogVisible\" :close-on-click-modal=\"false\">\n            <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\n                <el-form-item label=\"姓名\" prop=\"username\">\n                    <el-input v-model.number=\"ruleForm.username\"></el-input>\n                </el-form-item>\n                <el-form-item label=\"手机号\" prop=\"phone\">\n                    <el-input v-model.number=\"ruleForm.phone\" oninput=\"if(value.length>11)value=value.slice(0,11)\"></el-input>\n                </el-form-item>\n\n                <!-- 维修员可以修改类别和子类别 -->\n                <template v-if=\"user.role && user.role.id === 13\">\n                    <el-form-item label=\"类别-子类别\" prop=\"categoryValue\">\n                        <el-cascader\n                            v-model=\"categoryValue\"\n                            :options=\"categoryOptions\"\n                            :props=\"{\n                                children: 'children',\n                                label: 'name',\n                                value: 'id',\n                                checkStrictly: false\n                            }\"\n                            placeholder=\"请选择类别和子类别\"\n                            clearable\n                        ></el-cascader>\n                    </el-form-item>\n\n                    <!-- 维修员地址信息 -->\n                    <el-form-item label=\"省市区\" prop=\"addressRegion\">\n                        <el-cascader\n                            v-model=\"addressRegion\"\n                            :options=\"regionOptions\"\n                            placeholder=\"请选择省/市/区\"\n                            style=\"width: 100%\"\n                        ></el-cascader>\n                    </el-form-item>\n                    <el-form-item label=\"详细地址\" prop=\"address\">\n                        <el-input\n                            v-model=\"ruleForm.address\"\n                            type=\"textarea\"\n                            placeholder=\"请输入详细地址信息，如街道、门牌号等\"\n                            :rows=\"3\"\n                        ></el-input>\n                    </el-form-item>\n                </template>\n\n            </el-form>\n            <div slot=\"footer\" class=\"dialog-footer\">\n                <el-button @click=\"dialogVisible = false\">取 消</el-button>\n                <el-button type=\"primary\" @click=\"submitChanges\">确 定</el-button>\n            </div>\n        </el-dialog>\n    </div>\n</template>\n\n<script>\n    import {mapState, mapMutations} from \"vuex\"\n    import user from \"@s/store/module/user\";\n    import regionData from '@/assets/data/region.js';\n\n    export default {\n        name: \"Home\",\n        methods: {\n            ...mapMutations('user', ['setUser']),\n            changeColor(val){\n                sessionStorage.setItem(\"themeColor\",JSON.stringify(val))\n                this.themeColor = {'bg':val.value,'color':val.color};\n            },\n            //面包屑\n            getBreadcrumb() {\n                let matched = this.$route.matched;\n                if (matched[0].name != 'home') {\n                    matched = [{path: \"/home/\", meta: {title: '首页'}}].concat(matched)\n                }\n                this.breadList = matched;\n            },\n            //关闭抽屉触发的事件\n            handleClose(done) {\n                // 如果是首次登录且未完善信息，不允许关闭抽屉\n                if (this.user.state === 0) {\n                    this.$msg(\"首次登录必须完善信息\", \"error\")\n                } else {\n                    // 如果不是首次登录或已完善信息，允许关闭抽屉\n                    done()\n                }\n            },\n            submitForm(formName) {\n                this.$refs[formName].validate((valid) => {\n                    if (valid) {\n                        // 根据角色ID决定是否需要选择类别\n                        if ((this.user.role.id !== 14 && this.value) || this.user.role.id === 14) {\n                            const userData = {\n                                id: this.user.id,\n                                deptId: this.user.role.id !== 14 ? this.value[0] : null,\n                                // classId 字段在数据库中不存在，移除该字段\n                                username: this.ruleForm.username,\n                                phone: this.ruleForm.phone,\n                                sex: this.sex,\n                                // 设置状态为已完善信息\n                                state: 1\n                            }\n\n                            this.$put(\"/user\", userData)\n                                .then(res => {\n                                    if (res.data.status) {\n                                        this.drawer = false;\n                                        this.$notifyMsg(\"成功\", res.data.msg, \"success\")\n                                        // 更新本地用户信息\n                                        if (res.data.user) {\n                                            sessionStorage.setItem('user', JSON.stringify(res.data.user))\n                                            this.setUser(res.data.user)\n                                        }\n                                        this.newList(this.user.id)\n                                    } else {\n                                        this.$msg(res.data.msg, \"error\")\n                                    }\n                                })\n                                .catch(err => {\n                                    console.error('Update failed:', err)\n                                    this.$msg(err.response?.data?.msg || \"更新失败，请稍后重试\", \"error\")\n                                })\n                        } else {\n                            this.$notifyMsg(\"错误\", \"请选择类别\", \"error\")\n                        }\n                    } else {\n                        return false;\n                    }\n                });\n            },\n            //修改密码\n            updPassword(id) {\n                this.$prompt('请输入密码', '提示', {\n                    confirmButtonText: '确定',\n                    cancelButtonText: '取消',\n                    inputType: 'password',\n                    closeOnClickModal:false,\n                    inputPattern: /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,16}$/,\n                    inputErrorMessage: '格式不对，密码只能输入6-16位英文和数字'\n                }).then((res) => {\n                    // console.log(res);\n                    this.$put('/user', {id: id, password: res.value})\n                    .then((res) => {\n                        this.$notifyMsg('成功', res.data.msg, 'success')\n                    })\n                }).catch(() => {\n                })\n            },\n            recharge(studentId) {\n                this.$prompt('请输入充值金额', '充值', {\n                    confirmButtonText: '确定',\n                    cancelButtonText: '取消',\n                    inputPattern: /^(0\\.\\d{1,2}|[1-9]\\d*(\\.\\d{1,2})?)$/,\n                    inputErrorMessage: '请输入有效金额（最多两位小数）'\n                }).then(({ value }) => {\n                    const amount = parseFloat(value);\n\n                    // 验证金额范围\n                    if (amount < 0.01 || amount > 10000) {\n                        this.$msg(\"充值金额必须在0.01-10000元之间\", \"error\");\n                        return;\n                    }\n\n                    // 创建支付宝支付订单\n                    const payData = {\n                        subject: '用户充值',\n                        totalAmount: amount,\n                        userId: this.user.id\n                    };\n\n                    // 调用后端创建支付订单接口\n                    this.$post('api/alipay/create', payData)\n                        .then(res => {\n                            if (res.data.status) {\n                                // 在新窗口中打开支付页面\n                                const payWindow = window.open('', '_blank', 'width=800,height=600');\n                                payWindow.document.write(res.data.data.payForm);\n                                payWindow.document.close();\n\n                                // 定义轮询次数和间隔\n                                let pollCount = 0;\n                                const maxPolls = 60; // 最多轮询60次\n                                const pollInterval = 3000; // 每3秒轮询一次\n\n                                // 获取订单号\n                                const orderNo = res.data.data.orderNo;\n                                \n                                // 标记是否已经显示过成功消息\n                                let hasShownSuccess = false;\n\n                                // 开始轮询支付状态\n                                const checkPayment = setInterval(() => {\n                                    // 如果支付窗口关闭或达到最大轮询次数，停止轮询\n                                    if (pollCount >= maxPolls || payWindow.closed) {\n                                        clearInterval(checkPayment);\n                                        \n                                        // 最后查询一次支付状态\n                                        this.$get(`api/alipay/query/${orderNo}`)\n                                            .then(queryRes => {\n                                                if (queryRes.data.status && queryRes.data.data.status === 1) {\n                                                    // 支付成功，但只在未显示过成功消息时显示\n                                                    if (!hasShownSuccess) {\n                                                        this.newList(this.user.id);\n                                                        this.$msg(\"充值成功\", \"success\");\n                                                        hasShownSuccess = true;\n                                                    }\n                                                } else if (!hasShownSuccess) {\n                                                    // 支付未完成，且未显示过成功消息\n                                                    this.$msg(\"支付未完成，如已支付请稍后刷新查看\", \"warning\");\n                                                }\n                                            })\n                                            .catch(() => {\n                                                if (!hasShownSuccess) {\n                                                    this.$msg(\"查询支付状态失败，如已支付请稍后刷新查看\", \"warning\");\n                                                }\n                                            });\n                                        return;\n                                    }\n\n                                    // 定期查询支付状态\n                                    this.$get(`api/alipay/query/${orderNo}`)\n                                        .then(queryRes => {\n                                            if (queryRes.data.status && queryRes.data.data.status === 1 && !hasShownSuccess) {\n                                                // 支付成功且未显示过成功消息\n                                                clearInterval(checkPayment);\n                                                this.newList(this.user.id);\n                                                this.$msg(\"充值成功\", \"success\");\n                                                hasShownSuccess = true;\n                                                if (!payWindow.closed) {\n                                                    payWindow.close(); // 自动关闭支付窗口\n                                                }\n                                            }\n                                        })\n                                        .catch(err => {\n                                            console.error('查询支付状态失败:', err);\n                                        });\n\n                                    pollCount++;\n                                }, pollInterval);\n\n                            } else {\n                                this.$msg(res.data.msg || \"创建支付订单失败\", \"error\");\n                            }\n                        })\n                        .catch(err => {\n                            console.error('创建支付订单失败:', err);\n                            this.$msg(\"创建支付订单失败，请稍后重试\", \"error\");\n                        });\n                }).catch(() => {\n                    this.$msg(\"已取消充值\", \"info\");\n                });\n            },\n            personalInformation() {\n                this.dialogVisible = true;\n                this.ruleForm.username = this.user.username;\n                this.ruleForm.phone = this.user.phone;\n\n                // 如果是维修员，加载类别和子类别数据\n                if (this.user.role && this.user.role.id === 13) {\n                    // 设置当前的类别和子类别\n                    if (this.user.dept) {\n                        this.ruleForm.deptId = this.user.dept.id;\n                    }\n                    if (this.user.type) {\n                        this.ruleForm.classId = this.user.type.id;\n                    }\n\n                    // 设置级联选择器的初始值\n                    if (this.user.dept && this.user.type) {\n                        this.categoryValue = [this.user.dept.id, this.user.type.id];\n                    } else if (this.user.dept) {\n                        this.categoryValue = [this.user.dept.id];\n                    } else {\n                        this.categoryValue = [];\n                    }\n\n                    // 设置地址选择器的初始值\n                    if (this.user.province && this.user.city && this.user.district) {\n                        this.addressRegion = [this.user.province, this.user.city, this.user.district];\n                        this.ruleForm.address = this.user.address || '';\n                    } else {\n                        this.addressRegion = [];\n                        this.ruleForm.address = '';\n                    }\n\n                    // 加载所有可用的类别和子类别\n                    this.loadCategoryOptions();\n                }\n            },\n\n            // 加载类别和子类别的级联选项\n            loadCategoryOptions() {\n                // 先获取所有类别\n                this.$get(\"/dept/list\", { roleId: 13 })\n                .then(res => {\n                    if (res.data.status && res.data.dept) {\n                        // 保存原始类别列表（兼容性考虑）\n                        this.deptOptions = res.data.dept;\n                        console.log('可用的类别列表:', this.deptOptions);\n\n                        // 为每个类别加载子类别\n                        const promises = res.data.dept.map(dept => {\n                            return this.$get(\"/class/list\", { deptId: dept.id })\n                            .then(classRes => {\n                                if (classRes.data.status && classRes.data.class) {\n                                    // 返回带有子类别的类别对象\n                                    return {\n                                        ...dept,\n                                        children: classRes.data.class\n                                    };\n                                }\n                                // 如果没有子类别，返回原始类别对象\n                                return {\n                                    ...dept,\n                                    children: []\n                                };\n                            })\n                            .catch(() => {\n                                // 出错时返回没有子类别的类别对象\n                                return {\n                                    ...dept,\n                                    children: []\n                                };\n                            });\n                        });\n\n                        // 等待所有子类别加载完成\n                        Promise.all(promises)\n                        .then(categories => {\n                            this.categoryOptions = categories;\n                            console.log('级联选择器选项:', this.categoryOptions);\n                        });\n                    } else {\n                        this.$msg(\"获取类别列表失败\", \"error\");\n                    }\n                })\n                .catch(err => {\n                    console.error('获取类别列表失败:', err);\n                    this.$msg(\"获取类别列表失败，请稍后重试\", \"error\");\n                });\n            },\n\n            // 保留原有的方法（兼容性考虑）\n            loadDepartments() {\n                this.$get(\"/dept/list\", { roleId: 13 })\n                .then(res => {\n                    if (res.data.status && res.data.dept) {\n                        this.deptOptions = res.data.dept;\n                        console.log('可用的类别列表:', this.deptOptions);\n                    } else {\n                        this.$msg(\"获取类别列表失败\", \"error\");\n                    }\n                })\n                .catch(err => {\n                    console.error('获取类别列表失败:', err);\n                    this.$msg(\"获取类别列表失败，请稍后重试\", \"error\");\n                });\n            },\n\n            // 保留原有的方法（兼容性考虑）\n            loadSubCategories(deptId) {\n                if (!deptId) return;\n\n                this.$get(\"/class/list\", { deptId: deptId })\n                .then(res => {\n                    if (res.data.status && res.data.class) {\n                        this.classOptions = res.data.class;\n                        console.log('可用的子类别列表:', this.classOptions);\n\n                        // 如果当前选择的子类别不在新的子类别列表中，清空选择\n                        const exists = this.classOptions.some(item => item.id === this.ruleForm.classId);\n                        if (!exists) {\n                            this.ruleForm.classId = null;\n                        }\n                    } else {\n                        this.classOptions = [];\n                        this.ruleForm.classId = null;\n                        this.$msg(\"获取子类别列表失败\", \"error\");\n                    }\n                })\n                .catch(err => {\n                    console.error('获取子类别列表失败:', err);\n                    this.classOptions = [];\n                    this.ruleForm.classId = null;\n                    this.$msg(\"获取子类别列表失败，请稍后重试\", \"error\");\n                });\n            },\n            // submitChanges(){\n            //     this.$put(\"/user\",{id:this.user.id,username:this.ruleForm.username,phone:this.ruleForm.phone})\n            //     .then(res=>{\n            //         this.$notifyMsg(\"成功\",res.data.msg,\"success\",1000);\n            //         this.dialogVisible = false;\n            //         this.newList(this.user.id)\n            //     })\n            // },\n            submitChanges() {\n                // 准备要更新的用户数据\n                const userData = {\n                    id: this.user.id,\n                    username: this.ruleForm.username,\n                    phone: this.ruleForm.phone,\n                    state: 1\n                };\n\n                // 如果是维修员，添加类别和子类别信息\n                if (this.user.role && this.user.role.id === 13) {\n                    // 从级联选择器中获取类别和子类别ID\n                    if (this.categoryValue && this.categoryValue.length > 0) {\n                        // 第一个值是类别ID\n                        userData.deptId = this.categoryValue[0];\n\n                        // 如果选择了子类别（数组长度大于1），第二个值是子类别ID\n                        if (this.categoryValue.length > 1) {\n                            userData.classId = this.categoryValue[1];\n                        }\n                    }\n                    // 兼容旧版本的选择方式\n                    else {\n                        // 只有当选择了类别时才更新类别ID\n                        if (this.ruleForm.deptId) {\n                            userData.deptId = this.ruleForm.deptId;\n                        }\n\n                        // 只有当选择了子类别时才更新子类别ID\n                        if (this.ruleForm.classId) {\n                            userData.classId = this.ruleForm.classId;\n                        }\n                    }\n\n                    // 处理地址信息\n                    if (this.addressRegion && this.addressRegion.length === 3) {\n                        userData.province = this.addressRegion[0];\n                        userData.city = this.addressRegion[1];\n                        userData.district = this.addressRegion[2];\n                    }\n\n                    // 如果有详细地址，添加到更新数据中\n                    if (this.ruleForm.address) {\n                        userData.address = this.ruleForm.address;\n                    }\n                }\n\n                console.log('提交的用户数据:', userData);\n\n                // 发送更新请求\n                this.$put(\"/user\", userData)\n                    .then(res => {\n                        if (res.data.status) {\n                            this.$notifyMsg(\"成功\", res.data.msg, \"success\");\n                            this.dialogVisible = false;\n                            this.newList(this.user.id);\n                        } else {\n                            this.$msg(res.data.msg || \"更新失败\", \"error\");\n                        }\n                    })\n                    .catch(err => {\n                        console.error('更新用户信息失败:', err);\n                        this.$msg(\"更新失败，请稍后重试\", \"error\");\n                    });\n            },\n            //根据当前用户查询id\n            newList(id) {\n                this.$get(\"/user/\" + id)\n                .then((rs) => {\n                    // 确保用户状态正确更新\n                    if (rs.data.user) {\n                        console.log('User data from API:', JSON.stringify(rs.data.user, null, 2));\n                        console.log('User role ID:', rs.data.user.role ? rs.data.user.role.id : 'No role');\n                        console.log('User dept:', rs.data.user.dept ? JSON.stringify(rs.data.user.dept, null, 2) : 'No dept data');\n                        console.log('User type:', rs.data.user.type ? JSON.stringify(rs.data.user.type, null, 2) : 'No type data');\n\n                        // 如果是维修员但没有type信息，尝试重新获取用户数据\n                        if (rs.data.user.role && rs.data.user.role.id === 13 && !rs.data.user.type) {\n                            console.log('维修员没有type信息，尝试修复...');\n                            // 尝试手动设置type信息\n                            if (rs.data.user.classId) {\n                                console.log('找到classId:', rs.data.user.classId);\n                                // 获取type信息\n                                this.$get(\"/class/\" + rs.data.user.classId)\n                                .then(typeRes => {\n                                    if (typeRes.data.class) {\n                                        console.log('获取到type信息:', JSON.stringify(typeRes.data.class, null, 2));\n                                        // 手动设置type信息\n                                        rs.data.user.type = typeRes.data.class;\n                                        // 更新用户信息\n                                        sessionStorage.setItem(\"user\", JSON.stringify(rs.data.user));\n                                        this.setUser(rs.data.user);\n                                    }\n                                });\n                            }\n                        }\n\n                        // 如果用户已完善信息，确保状态为1\n                        if (rs.data.user.dept && rs.data.user.state === 0) {\n                            rs.data.user.state = 1;\n                            // 更新用户状态\n                            this.$put(\"/user\", {\n                                id: rs.data.user.id,\n                                state: 1\n                            });\n                        }\n                        sessionStorage.setItem(\"user\", JSON.stringify(rs.data.user))\n                        this.setUser(JSON.parse(sessionStorage.getItem(\"user\")))\n                        // 修改完名字, 清空當前firstName; 避免出現疊加\n                        this.firstName = '';\n                        this.textAvatar(rs.data.user.username);\n                    }\n                })\n            },\n            exit(){\n                sessionStorage.removeItem('user');\n                this.$router.push('/')\n            },\n            // 文字頭像\n            textAvatar(username) {\n                let arr = username.split(' ');\n                for (var i in arr) {\n                    this.firstName += arr[i].substr(0,1);\n                }\n                this.firstName = this.firstName.toLocaleUpperCase();\n                console.log('firstName->' + this.firstName);\n            },\n\n            // 获取未读通知数量\n            getUnreadNotificationCount() {\n                if (this.user && this.user.id) {\n                    this.$get('/forum/notification/unread/count', { userId: this.user.id })\n                    .then(res => {\n                        if (res.data.status) {\n                            this.unreadCount = res.data.count;\n                        }\n                    })\n                    .catch(err => {\n                        console.error('获取未读通知数量失败:', err);\n                    });\n                }\n            }\n        },\n        computed: {\n            ...mapState('user', ['user']),\n            theme(){\n                return this.$store.state.theme.theme\n            },\n        },\n        data() {\n            var validateUsername = (rule, value, callback) => {\n                if (value === '') {\n                    callback(new Error('请输入姓名'));\n                } else {\n                    callback();\n                }\n            };\n            var validatePhone = (rule, value, callback) => {\n                if (value === '') {\n                    callback(new Error('请输入手机号'));\n                } else {\n                    callback();\n                }\n            };\n\n            return {\n                // 文字头像\n                firstName:'',\n                ruleForm: {\n                    username: '',\n                    phone: '',\n                    deptId: null,\n                    classId: null,\n                    address: ''\n                },\n                // 级联选择器的值和选项\n                categoryValue: [],\n                categoryOptions: [],\n                // 保留原有的选项（兼容性考虑）\n                deptOptions: [],\n                classOptions: [],\n\n                // 地址相关\n                addressRegion: [],\n                regionOptions: [],\n                rules: {\n                    username: [\n                        {validator: validateUsername, trigger: 'blur'}\n                    ],\n                    phone: [\n                        {validator: validatePhone, trigger: 'blur'}\n                    ]\n                },\n                //颜色\n                themeColor : {'bg':'#fff','color':'#000'},\n                //性别\n                sex:'0',\n                drawer: false,\n                //当前路由\n                breadList: [],\n                //当前屏幕宽度\n                windowWidth: document.documentElement.clientWidth,\n                activeIndex: '1',\n                //控制菜单是否展开\n                isCollapse: false,\n                admin: \"\",\n                // role: [],\n                role:[],\n                //级联选择器的值\n                value: \"\",\n                dialogVisible:false,\n                // 未读通知数量\n                unreadCount: 0\n            }\n        },\n        watch: {\n            '$route'(to, form) {\n                this.getBreadcrumb()\n            }\n        },\n        created() {\n            // 初始化地址数据\n            this.regionOptions = regionData;\n\n            let theme = JSON.parse(sessionStorage.getItem(\"themeColor\"));\n            if (theme){\n                this.themeColor = {'bg':theme.value,'color':theme.color}\n            }\n\n            if (sessionStorage.getItem('user')){\n                this.getBreadcrumb();\n                const userData = JSON.parse(sessionStorage.getItem(\"user\"));\n                console.log('User data from session:', userData);\n                this.setUser(userData);\n\n                // 如果是维修员或管理员，获取未读通知数量\n                if (this.user.role && (this.user.role.id === 13 || this.user.role.id === 14)) {\n                    this.getUnreadNotificationCount();\n\n                    // 每分钟获取一次未读通知数量\n                    setInterval(() => {\n                        this.getUnreadNotificationCount();\n                    }, 60000);\n                }\n                //\n                // 检查用户是否是首次登录（通过检查state字段）\n                // state为0表示未完善信息，state为1表示已完善信息\n                if (this.user.state === 0) {\n                    this.$get(\"/role/\" + this.user.role.id)\n                    .then((res) => {\n                        console.log(res.data)\n                        this.role = res.data.role;\n                        this.drawer = true\n                        this.$msg(\"首次登录，请完善信息\", \"warning\")\n                    })\n                }\n            }else {\n                this.$msg(\"您向未登陆,没有权限\",\"error\")\n                this.$router.push(\"/\")\n            }\n\n        },\n        mounted() {\n            // 获取当前屏幕宽度\n            window.onresize = () => {\n                this.windowWidth = document.documentElement.clientWidth\n            }\n            // 文字頭像\n            this.textAvatar(this.user.username);\n        }\n    }\n</script>\n\n<style scoped lang=\"less\">\n    .main {\n        display: flex;\n        height: 90%;\n\n        .left {\n            position: fixed;\n            height: 100%;\n\n            .logo {\n                width: 90%;\n                /*color: white;*/\n                font-size: 16px;\n                text-align: center;\n                padding: 8px 0;\n                /*border: 1px solid white;*/\n                margin: 9.1px auto 0 auto;\n            }\n        }\n\n        .right {\n            transition: all 0.3s ease 0s;\n            position: relative;\n\n            .top {\n                transition: all 0.3s ease 0s;\n                position: fixed;\n                /*color: #fff;*/\n                display: flex;\n                align-items: center;\n                justify-content: space-between;\n                z-index: 9;\n\n                .icon {\n                    font-size: 20px;\n                    cursor: pointer;\n                    margin-left: 10px;\n                }\n            }\n\n            .bottom {\n                width: 100%;\n                height: 100%;\n                /*background: #fff;*/\n                margin-top: 65px;\n                .bottom_top {\n                    padding: 20px;\n                }\n            }\n        }\n\n        .ruleform /deep/ .el-input {\n            width: 80% !important;\n        }\n\n        /deep/ .el-cascader {\n            width: 100% !important;\n        }\n    }\n</style>\n"]}]}