package com.yqn.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yqn.pojo.Class;
import com.yqn.service.ClassService;
import com.yqn.common.tools.MessageTools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/class")
public class ClassController {
    @Autowired
    private ClassService classService;
    @Autowired
    private MessageTools message;

    // 获取全部class
    @GetMapping
    public Map<String, Object> myClass() {
        List<Class> c = classService.list();
        return message.message(true, "请求成功", "class", c);
    }

    // 根据部门ID获取class列表
    @GetMapping("/list")
    public Map<String, Object> classList(@RequestParam(required = false) Long deptId) {
        QueryWrapper<Class> wrapper = new QueryWrapper<>();
        if (deptId != null) {
            wrapper.eq("dept_id", deptId);
        }
        List<Class> classes = classService.list(wrapper);
        return message.message(true, "请求成功", "class", classes);
    }

    // 根据id获取myClass
    @GetMapping("/{id}")
    public Map<String, Object> myClass(@PathVariable Long id) {
        Class c = classService.getById(id);
        return message.message(true, "请求成功", "class", c);
    }

    // 添加myClass
    @PostMapping
    public Map<String, Object> saveClass(Class c) {
        QueryWrapper<Class> wrapper = new QueryWrapper<>();
        wrapper.eq("role_id", c.getRoleId())
                .eq("dept_id", c.getDeptId())
                .eq("name", c.getName());

        Class one = classService.getOne(wrapper);
        if (one == null) {
            classService.save(c);
            return message.message(true, "添加维修子类别成功", "", null);
        }

        return message.message(false, "该维修类别下已存在同名子类别", "", null);
    }

    // 修改class
    @PutMapping
    public Map<String, Object> updateClass(@RequestBody Class c) {
        try {
            // 检查是否存在同名的子类别（排除自己）
            QueryWrapper<Class> wrapper = new QueryWrapper<>();
            wrapper.eq("role_id", c.getRoleId())
                    .eq("dept_id", c.getDeptId())
                    .eq("name", c.getName())
                    .ne("id", c.getId());

            Class existingClass = classService.getOne(wrapper);
            if (existingClass != null) {
                return message.message(false, "该维修类别下已存在同名子类别", "", null);
            }

            boolean update = classService.updateById(c);
            if (update) {
                return message.message(true, "修改维修子类别成功", "", null);
            }
            return message.message(false, "修改维修子类别失败", "", null);
        } catch (Exception e) {
            return message.message(false, "修改失败：" + e.getMessage(), "", null);
        }
    }

    // 删除
    @DeleteMapping("/{id}")
    public Map<String, Object> delClass(@PathVariable Long id) {
        boolean remove = classService.removeById(id);
        if (remove) {
            return message.message(true, "删除维修子类别成功", "", null);
        }
        return message.message(false, "删除维修子类别失败", "", null);
    }
}
