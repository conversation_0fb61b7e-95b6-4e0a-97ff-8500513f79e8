package com.yqn.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yqn.pojo.Dept;
import com.yqn.service.DeptService;
import com.yqn.common.tools.MessageTools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/dept")
public class DeptController {
    @Autowired
    private DeptService deptService;
    @Autowired
    private MessageTools message;

    // 获取全部dept
    @GetMapping
    public Map<String, Object> depts() {
        List<Dept> depts = deptService.list();
        return message.message(true, "请求成功", "dept", depts);
    }

    // 根据角色ID获取dept列表
    @GetMapping("/list")
    public Map<String, Object> deptList(@RequestParam(required = false) Long roleId) {
        QueryWrapper<Dept> wrapper = new QueryWrapper<>();
        if (roleId != null) {
            wrapper.eq("role_id", roleId);
        }
        List<Dept> depts = deptService.list(wrapper);
        return message.message(true, "请求成功", "dept", depts);
    }

    // 根据id获取dept
    @GetMapping("/{id}")
    public Map<String, Object> dept(@PathVariable Long id) {
        Dept dept = deptService.getById(id);
        return message.message(true, "请求成功", "dept", dept);
    }

    // 添加dept
    @PostMapping
    public Map<String, Object> saveDept(Dept dept) {
        QueryWrapper<Dept> wrapper = new QueryWrapper<>();
        wrapper.eq("role_id", dept.getRoleId())
                .eq("name", dept.getName());

        Dept one = deptService.getOne(wrapper);
        if (one == null) {
            deptService.save(dept);
            return message.message(true, "添加维修类别成功", "", null);
        }

        return message.message(false, "该角色下已存在同名维修类别", "", null);
    }

    // 修改dept
    @PutMapping
    public Map<String, Object> updateDept(@RequestBody Dept dept) {
        try {
            // 检查是否存在同名的类别（排除自己）
            QueryWrapper<Dept> wrapper = new QueryWrapper<>();
            wrapper.eq("role_id", dept.getRoleId())
                    .eq("name", dept.getName())
                    .ne("id", dept.getId());

            Dept existingDept = deptService.getOne(wrapper);
            if (existingDept != null) {
                return message.message(false, "该角色下已存在同名维修类别", "", null);
            }

            boolean update = deptService.updateById(dept);
            if (update) {
                return message.message(true, "修改维修类别成功", "", null);
            }
            return message.message(false, "修改维修类别失败", "", null);
        } catch (Exception e) {
            return message.message(false, "修改失败：" + e.getMessage(), "", null);
        }
    }

    // 删除
    @DeleteMapping("/{id}")
    public Map<String, Object> delDept(@PathVariable Long id) {
        boolean remove = deptService.removeById(id);
        if (remove) {
            return message.message(true, "删除维修类别成功", "", null);
        }
        return message.message(false, "删除维修类别失败", "", null);
    }
}
