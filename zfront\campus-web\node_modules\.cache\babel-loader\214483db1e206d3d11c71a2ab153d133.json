{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\user\\Home.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\user\\Home.vue", "mtime": 1748677982322}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Home.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6QA,SAAA,QAAA,EAAA,YAAA,QAAA,MAAA;AACA,OAAA,IAAA,MAAA,sBAAA;AACA,OAAA,UAAA,MAAA,yBAAA;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,MADA;AAEA,EAAA,OAAA,kCACA,YAAA,CAAA,MAAA,EAAA,CAAA,SAAA,CAAA,CADA;AAEA,IAAA,WAFA,uBAEA,GAFA,EAEA;AACA,MAAA,cAAA,CAAA,OAAA,CAAA,YAAA,EAAA,IAAA,CAAA,SAAA,CAAA,GAAA,CAAA;AACA,WAAA,UAAA,GAAA;AAAA,cAAA,GAAA,CAAA,KAAA;AAAA,iBAAA,GAAA,CAAA;AAAA,OAAA;AACA,KALA;AAMA;AACA,IAAA,aAPA,2BAOA;AACA,UAAA,OAAA,GAAA,KAAA,MAAA,CAAA,OAAA;;AACA,UAAA,OAAA,CAAA,CAAA,CAAA,CAAA,IAAA,IAAA,MAAA,EAAA;AACA,QAAA,OAAA,GAAA,CAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,IAAA,EAAA;AAAA,YAAA,KAAA,EAAA;AAAA;AAAA,SAAA,EAAA,MAAA,CAAA,OAAA,CAAA;AACA;;AACA,WAAA,SAAA,GAAA,OAAA;AACA,KAbA;AAcA;AACA,IAAA,WAfA,uBAeA,IAfA,EAeA;AACA;AACA,UAAA,KAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA;AACA,aAAA,IAAA,CAAA,YAAA,EAAA,OAAA;AACA,OAFA,MAEA;AACA;AACA,QAAA,IAAA;AACA;AACA,KAvBA;AAwBA,IAAA,UAxBA,sBAwBA,QAxBA,EAwBA;AAAA;;AACA,WAAA,KAAA,CAAA,QAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA;AACA,cAAA,KAAA,CAAA,IAAA,CAAA,IAAA,CAAA,EAAA,KAAA,EAAA,IAAA,KAAA,CAAA,KAAA,IAAA,KAAA,CAAA,IAAA,CAAA,IAAA,CAAA,EAAA,KAAA,EAAA,EAAA;AACA,gBAAA,QAAA,GAAA;AACA,cAAA,EAAA,EAAA,KAAA,CAAA,IAAA,CAAA,EADA;AAEA,cAAA,MAAA,EAAA,KAAA,CAAA,IAAA,CAAA,IAAA,CAAA,EAAA,KAAA,EAAA,GAAA,KAAA,CAAA,KAAA,CAAA,CAAA,CAAA,GAAA,IAFA;AAGA;AACA,cAAA,QAAA,EAAA,KAAA,CAAA,QAAA,CAAA,QAJA;AAKA,cAAA,KAAA,EAAA,KAAA,CAAA,QAAA,CAAA,KALA;AAMA,cAAA,GAAA,EAAA,KAAA,CAAA,GANA;AAOA;AACA,cAAA,KAAA,EAAA;AARA,aAAA;;AAWA,YAAA,KAAA,CAAA,IAAA,CAAA,OAAA,EAAA,QAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,kBAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AACA,gBAAA,KAAA,CAAA,MAAA,GAAA,KAAA;;AACA,gBAAA,KAAA,CAAA,UAAA,CAAA,IAAA,EAAA,GAAA,CAAA,IAAA,CAAA,GAAA,EAAA,SAAA,EAFA,CAGA;;;AACA,oBAAA,GAAA,CAAA,IAAA,CAAA,IAAA,EAAA;AACA,kBAAA,cAAA,CAAA,OAAA,CAAA,MAAA,EAAA,IAAA,CAAA,SAAA,CAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA;;AACA,kBAAA,KAAA,CAAA,OAAA,CAAA,GAAA,CAAA,IAAA,CAAA,IAAA;AACA;;AACA,gBAAA,KAAA,CAAA,OAAA,CAAA,KAAA,CAAA,IAAA,CAAA,EAAA;AACA,eATA,MASA;AACA,gBAAA,KAAA,CAAA,IAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,EAAA,OAAA;AACA;AACA,aAdA,EAeA,KAfA,CAeA,UAAA,GAAA,EAAA;AAAA;;AACA,cAAA,OAAA,CAAA,KAAA,CAAA,gBAAA,EAAA,GAAA;;AACA,cAAA,KAAA,CAAA,IAAA,CAAA,kBAAA,GAAA,CAAA,QAAA,sFAAA,IAAA,0EAAA,GAAA,KAAA,YAAA,EAAA,OAAA;AACA,aAlBA;AAmBA,WA/BA,MA+BA;AACA,YAAA,KAAA,CAAA,UAAA,CAAA,IAAA,EAAA,OAAA,EAAA,OAAA;AACA;AACA,SApCA,MAoCA;AACA,iBAAA,KAAA;AACA;AACA,OAxCA;AAyCA,KAlEA;AAmEA;AACA,IAAA,WApEA,uBAoEA,EApEA,EAoEA;AAAA;;AACA,WAAA,OAAA,CAAA,OAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,SAAA,EAAA,UAHA;AAIA,QAAA,iBAAA,EAAA,KAJA;AAKA,QAAA,YAAA,EAAA,8CALA;AAMA,QAAA,iBAAA,EAAA;AANA,OAAA,EAOA,IAPA,CAOA,UAAA,GAAA,EAAA;AACA;AACA,QAAA,MAAA,CAAA,IAAA,CAAA,OAAA,EAAA;AAAA,UAAA,EAAA,EAAA,EAAA;AAAA,UAAA,QAAA,EAAA,GAAA,CAAA;AAAA,SAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,IAAA,EAAA,GAAA,CAAA,IAAA,CAAA,GAAA,EAAA,SAAA;AACA,SAHA;AAIA,OAbA,EAaA,KAbA,CAaA,YAAA,CACA,CAdA;AAeA,KApFA;AAqFA,IAAA,QArFA,oBAqFA,SArFA,EAqFA;AAAA;;AACA,WAAA,OAAA,CAAA,SAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,YAAA,EAAA,qCAHA;AAIA,QAAA,iBAAA,EAAA;AAJA,OAAA,EAKA,IALA,CAKA,gBAAA;AAAA,YAAA,KAAA,QAAA,KAAA;AACA,YAAA,MAAA,GAAA,UAAA,CAAA,KAAA,CAAA,CADA,CAGA;;AACA,YAAA,MAAA,GAAA,IAAA,IAAA,MAAA,GAAA,KAAA,EAAA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,sBAAA,EAAA,OAAA;;AACA;AACA,SAPA,CASA;;;AACA,YAAA,OAAA,GAAA;AACA,UAAA,OAAA,EAAA,MADA;AAEA,UAAA,WAAA,EAAA,MAFA;AAGA,UAAA,MAAA,EAAA,MAAA,CAAA,IAAA,CAAA;AAHA,SAAA,CAVA,CAgBA;;AACA,QAAA,MAAA,CAAA,KAAA,CAAA,mBAAA,EAAA,OAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AACA;AACA,gBAAA,SAAA,GAAA,MAAA,CAAA,IAAA,CAAA,EAAA,EAAA,QAAA,EAAA,sBAAA,CAAA;AACA,YAAA,SAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,OAAA;AACA,YAAA,SAAA,CAAA,QAAA,CAAA,KAAA,GAJA,CAMA;;AACA,gBAAA,SAAA,GAAA,CAAA;AACA,gBAAA,QAAA,GAAA,EAAA,CARA,CAQA;;AACA,gBAAA,YAAA,GAAA,IAAA,CATA,CASA;AAEA;;AACA,gBAAA,OAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,OAAA,CAZA,CAcA;;AACA,gBAAA,eAAA,GAAA,KAAA,CAfA,CAiBA;;AACA,gBAAA,YAAA,GAAA,WAAA,CAAA,YAAA;AACA;AACA,kBAAA,SAAA,IAAA,QAAA,IAAA,SAAA,CAAA,MAAA,EAAA;AACA,gBAAA,aAAA,CAAA,YAAA,CAAA,CADA,CAGA;;AACA,gBAAA,MAAA,CAAA,IAAA,4BAAA,OAAA,GACA,IADA,CACA,UAAA,QAAA,EAAA;AACA,sBAAA,QAAA,CAAA,IAAA,CAAA,MAAA,IAAA,QAAA,CAAA,IAAA,CAAA,IAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA;AACA,wBAAA,CAAA,eAAA,EAAA;AACA,sBAAA,MAAA,CAAA,OAAA,CAAA,MAAA,CAAA,IAAA,CAAA,EAAA;;AACA,sBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,EAAA,SAAA;;AACA,sBAAA,eAAA,GAAA,IAAA;AACA;AACA,mBAPA,MAOA,IAAA,CAAA,eAAA,EAAA;AACA;AACA,oBAAA,MAAA,CAAA,IAAA,CAAA,mBAAA,EAAA,SAAA;AACA;AACA,iBAbA,EAcA,KAdA,CAcA,YAAA;AACA,sBAAA,CAAA,eAAA,EAAA;AACA,oBAAA,MAAA,CAAA,IAAA,CAAA,sBAAA,EAAA,SAAA;AACA;AACA,iBAlBA;;AAmBA;AACA,eA1BA,CA4BA;;;AACA,cAAA,MAAA,CAAA,IAAA,4BAAA,OAAA,GACA,IADA,CACA,UAAA,QAAA,EAAA;AACA,oBAAA,QAAA,CAAA,IAAA,CAAA,MAAA,IAAA,QAAA,CAAA,IAAA,CAAA,IAAA,CAAA,MAAA,KAAA,CAAA,IAAA,CAAA,eAAA,EAAA;AACA;AACA,kBAAA,aAAA,CAAA,YAAA,CAAA;;AACA,kBAAA,MAAA,CAAA,OAAA,CAAA,MAAA,CAAA,IAAA,CAAA,EAAA;;AACA,kBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,EAAA,SAAA;;AACA,kBAAA,eAAA,GAAA,IAAA;;AACA,sBAAA,CAAA,SAAA,CAAA,MAAA,EAAA;AACA,oBAAA,SAAA,CAAA,KAAA,GADA,CACA;AACA;AACA;AACA,eAZA,EAaA,KAbA,CAaA,UAAA,GAAA,EAAA;AACA,gBAAA,OAAA,CAAA,KAAA,CAAA,WAAA,EAAA,GAAA;AACA,eAfA;;AAiBA,cAAA,SAAA;AACA,aA/CA,EA+CA,YA/CA,CAAA;AAiDA,WAnEA,MAmEA;AACA,YAAA,MAAA,CAAA,IAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,IAAA,UAAA,EAAA,OAAA;AACA;AACA,SAxEA,EAyEA,KAzEA,CAyEA,UAAA,GAAA,EAAA;AACA,UAAA,OAAA,CAAA,KAAA,CAAA,WAAA,EAAA,GAAA;;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,gBAAA,EAAA,OAAA;AACA,SA5EA;AA6EA,OAnGA,EAmGA,KAnGA,CAmGA,YAAA;AACA,QAAA,MAAA,CAAA,IAAA,CAAA,OAAA,EAAA,MAAA;AACA,OArGA;AAsGA,KA5LA;AA6LA,IAAA,mBA7LA,iCA6LA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,WAAA,QAAA,CAAA,QAAA,GAAA,KAAA,IAAA,CAAA,QAAA;AACA,WAAA,QAAA,CAAA,KAAA,GAAA,KAAA,IAAA,CAAA,KAAA,CAHA,CAKA;;AACA,UAAA,KAAA,IAAA,CAAA,IAAA,IAAA,KAAA,IAAA,CAAA,IAAA,CAAA,EAAA,KAAA,EAAA,EAAA;AACA;AACA,YAAA,KAAA,IAAA,CAAA,IAAA,EAAA;AACA,eAAA,QAAA,CAAA,MAAA,GAAA,KAAA,IAAA,CAAA,IAAA,CAAA,EAAA;AACA;;AACA,YAAA,KAAA,IAAA,CAAA,IAAA,EAAA;AACA,eAAA,QAAA,CAAA,OAAA,GAAA,KAAA,IAAA,CAAA,IAAA,CAAA,EAAA;AACA,SAPA,CASA;;;AACA,YAAA,KAAA,IAAA,CAAA,IAAA,IAAA,KAAA,IAAA,CAAA,IAAA,EAAA;AACA,eAAA,aAAA,GAAA,CAAA,KAAA,IAAA,CAAA,IAAA,CAAA,EAAA,EAAA,KAAA,IAAA,CAAA,IAAA,CAAA,EAAA,CAAA;AACA,SAFA,MAEA,IAAA,KAAA,IAAA,CAAA,IAAA,EAAA;AACA,eAAA,aAAA,GAAA,CAAA,KAAA,IAAA,CAAA,IAAA,CAAA,EAAA,CAAA;AACA,SAFA,MAEA;AACA,eAAA,aAAA,GAAA,EAAA;AACA,SAhBA,CAkBA;;;AACA,YAAA,KAAA,IAAA,CAAA,QAAA,IAAA,KAAA,IAAA,CAAA,IAAA,IAAA,KAAA,IAAA,CAAA,QAAA,EAAA;AACA,eAAA,aAAA,GAAA,CAAA,KAAA,IAAA,CAAA,QAAA,EAAA,KAAA,IAAA,CAAA,IAAA,EAAA,KAAA,IAAA,CAAA,QAAA,CAAA;AACA,eAAA,QAAA,CAAA,OAAA,GAAA,KAAA,IAAA,CAAA,OAAA,IAAA,EAAA;AACA,SAHA,MAGA;AACA,eAAA,aAAA,GAAA,EAAA;AACA,eAAA,QAAA,CAAA,OAAA,GAAA,EAAA;AACA,SAzBA,CA2BA;;;AACA,aAAA,mBAAA;AACA;AACA,KAjOA;AAmOA;AACA,IAAA,mBApOA,iCAoOA;AAAA;;AACA;AACA,WAAA,IAAA,CAAA,YAAA,EAAA;AAAA,QAAA,MAAA,EAAA;AAAA,OAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,MAAA,IAAA,GAAA,CAAA,IAAA,CAAA,IAAA,EAAA;AACA;AACA,UAAA,MAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,UAAA,EAAA,MAAA,CAAA,WAAA,EAHA,CAKA;;AACA,cAAA,QAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,mBAAA,MAAA,CAAA,IAAA,CAAA,aAAA,EAAA;AAAA,cAAA,MAAA,EAAA,IAAA,CAAA;AAAA,aAAA,EACA,IADA,CACA,UAAA,QAAA,EAAA;AACA,kBAAA,QAAA,CAAA,IAAA,CAAA,MAAA,IAAA,QAAA,CAAA,IAAA,CAAA,KAAA,EAAA;AACA;AACA,uDACA,IADA;AAEA,kBAAA,QAAA,EAAA,QAAA,CAAA,IAAA,CAAA;AAFA;AAIA,eAPA,CAQA;;;AACA,qDACA,IADA;AAEA,gBAAA,QAAA,EAAA;AAFA;AAIA,aAdA,EAeA,KAfA,CAeA,YAAA;AACA;AACA,qDACA,IADA;AAEA,gBAAA,QAAA,EAAA;AAFA;AAIA,aArBA,CAAA;AAsBA,WAvBA,CAAA,CANA,CA+BA;;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,QAAA,EACA,IADA,CACA,UAAA,UAAA,EAAA;AACA,YAAA,MAAA,CAAA,eAAA,GAAA,UAAA;AACA,YAAA,OAAA,CAAA,GAAA,CAAA,UAAA,EAAA,MAAA,CAAA,eAAA;AACA,WAJA;AAKA,SArCA,MAqCA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,UAAA,EAAA,OAAA;AACA;AACA,OA1CA,EA2CA,KA3CA,CA2CA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,WAAA,EAAA,GAAA;;AACA,QAAA,MAAA,CAAA,IAAA,CAAA,gBAAA,EAAA,OAAA;AACA,OA9CA;AA+CA,KArRA;AAuRA;AACA,IAAA,eAxRA,6BAwRA;AAAA;;AACA,WAAA,IAAA,CAAA,YAAA,EAAA;AAAA,QAAA,MAAA,EAAA;AAAA,OAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,MAAA,IAAA,GAAA,CAAA,IAAA,CAAA,IAAA,EAAA;AACA,UAAA,MAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,UAAA,EAAA,MAAA,CAAA,WAAA;AACA,SAHA,MAGA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,UAAA,EAAA,OAAA;AACA;AACA,OARA,EASA,KATA,CASA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,WAAA,EAAA,GAAA;;AACA,QAAA,MAAA,CAAA,IAAA,CAAA,gBAAA,EAAA,OAAA;AACA,OAZA;AAaA,KAtSA;AAwSA;AACA,IAAA,iBAzSA,6BAySA,MAzSA,EAySA;AAAA;;AACA,UAAA,CAAA,MAAA,EAAA;AAEA,WAAA,IAAA,CAAA,aAAA,EAAA;AAAA,QAAA,MAAA,EAAA;AAAA,OAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,MAAA,IAAA,GAAA,CAAA,IAAA,CAAA,KAAA,EAAA;AACA,UAAA,MAAA,CAAA,YAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,WAAA,EAAA,MAAA,CAAA,YAAA,EAFA,CAIA;;AACA,cAAA,MAAA,GAAA,MAAA,CAAA,YAAA,CAAA,IAAA,CAAA,UAAA,IAAA;AAAA,mBAAA,IAAA,CAAA,EAAA,KAAA,MAAA,CAAA,QAAA,CAAA,OAAA;AAAA,WAAA,CAAA;;AACA,cAAA,CAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,OAAA,GAAA,IAAA;AACA;AACA,SATA,MASA;AACA,UAAA,MAAA,CAAA,YAAA,GAAA,EAAA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,OAAA,GAAA,IAAA;;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,WAAA,EAAA,OAAA;AACA;AACA,OAhBA,EAiBA,KAjBA,CAiBA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,YAAA,EAAA,GAAA;AACA,QAAA,MAAA,CAAA,YAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,QAAA,CAAA,OAAA,GAAA,IAAA;;AACA,QAAA,MAAA,CAAA,IAAA,CAAA,iBAAA,EAAA,OAAA;AACA,OAtBA;AAuBA,KAnUA;AAoUA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAA,aA5UA,2BA4UA;AAAA;;AACA;AACA,UAAA,QAAA,GAAA;AACA,QAAA,EAAA,EAAA,KAAA,IAAA,CAAA,EADA;AAEA,QAAA,QAAA,EAAA,KAAA,QAAA,CAAA,QAFA;AAGA,QAAA,KAAA,EAAA,KAAA,QAAA,CAAA,KAHA;AAIA,QAAA,KAAA,EAAA;AAJA,OAAA,CAFA,CASA;;AACA,UAAA,KAAA,IAAA,CAAA,IAAA,IAAA,KAAA,IAAA,CAAA,IAAA,CAAA,EAAA,KAAA,EAAA,EAAA;AACA;AACA,YAAA,KAAA,aAAA,IAAA,KAAA,aAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA;AACA,UAAA,QAAA,CAAA,MAAA,GAAA,KAAA,aAAA,CAAA,CAAA,CAAA,CAFA,CAIA;;AACA,cAAA,KAAA,aAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,YAAA,QAAA,CAAA,OAAA,GAAA,KAAA,aAAA,CAAA,CAAA,CAAA;AACA;AACA,SARA,CASA;AATA,aAUA;AACA;AACA,gBAAA,KAAA,QAAA,CAAA,MAAA,EAAA;AACA,cAAA,QAAA,CAAA,MAAA,GAAA,KAAA,QAAA,CAAA,MAAA;AACA,aAJA,CAMA;;;AACA,gBAAA,KAAA,QAAA,CAAA,OAAA,EAAA;AACA,cAAA,QAAA,CAAA,OAAA,GAAA,KAAA,QAAA,CAAA,OAAA;AACA;AACA,WAtBA,CAwBA;;;AACA,YAAA,KAAA,aAAA,IAAA,KAAA,aAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,UAAA,QAAA,CAAA,QAAA,GAAA,KAAA,aAAA,CAAA,CAAA,CAAA;AACA,UAAA,QAAA,CAAA,IAAA,GAAA,KAAA,aAAA,CAAA,CAAA,CAAA;AACA,UAAA,QAAA,CAAA,QAAA,GAAA,KAAA,aAAA,CAAA,CAAA,CAAA;AACA,SA7BA,CA+BA;;;AACA,YAAA,KAAA,QAAA,CAAA,OAAA,EAAA;AACA,UAAA,QAAA,CAAA,OAAA,GAAA,KAAA,QAAA,CAAA,OAAA;AACA;AACA;;AAEA,MAAA,OAAA,CAAA,GAAA,CAAA,UAAA,EAAA,QAAA,EA/CA,CAiDA;;AACA,WAAA,IAAA,CAAA,OAAA,EAAA,QAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,IAAA,EAAA,GAAA,CAAA,IAAA,CAAA,GAAA,EAAA,SAAA;;AACA,UAAA,MAAA,CAAA,aAAA,GAAA,KAAA;;AACA,UAAA,MAAA,CAAA,OAAA,CAAA,MAAA,CAAA,IAAA,CAAA,EAAA;AACA,SAJA,MAIA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,IAAA,MAAA,EAAA,OAAA;AACA;AACA,OATA,EAUA,KAVA,CAUA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,WAAA,EAAA,GAAA;;AACA,QAAA,MAAA,CAAA,IAAA,CAAA,YAAA,EAAA,OAAA;AACA,OAbA;AAcA,KA5YA;AA6YA;AACA,IAAA,OA9YA,mBA8YA,EA9YA,EA8YA;AAAA;;AACA,WAAA,IAAA,CAAA,WAAA,EAAA,EACA,IADA,CACA,UAAA,EAAA,EAAA;AACA;AACA,YAAA,EAAA,CAAA,IAAA,CAAA,IAAA,EAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,qBAAA,EAAA,IAAA,CAAA,SAAA,CAAA,EAAA,CAAA,IAAA,CAAA,IAAA,EAAA,IAAA,EAAA,CAAA,CAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,eAAA,EAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,CAAA,EAAA,GAAA,SAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,GAAA,IAAA,CAAA,SAAA,CAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,EAAA,IAAA,EAAA,CAAA,CAAA,GAAA,cAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,GAAA,IAAA,CAAA,SAAA,CAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,EAAA,IAAA,EAAA,CAAA,CAAA,GAAA,cAAA,EAJA,CAMA;;AACA,cAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,IAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,CAAA,EAAA,KAAA,EAAA,IAAA,CAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,EAAA;AACA,YAAA,OAAA,CAAA,GAAA,CAAA,qBAAA,EADA,CAEA;;AACA,gBAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,OAAA,EAAA;AACA,cAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,OAAA,EADA,CAEA;;AACA,cAAA,MAAA,CAAA,IAAA,CAAA,YAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,OAAA,EACA,IADA,CACA,UAAA,OAAA,EAAA;AACA,oBAAA,OAAA,CAAA,IAAA,CAAA,KAAA,EAAA;AACA,kBAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA,IAAA,CAAA,SAAA,CAAA,OAAA,CAAA,IAAA,CAAA,KAAA,EAAA,IAAA,EAAA,CAAA,CAAA,EADA,CAEA;;AACA,kBAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,GAAA,OAAA,CAAA,IAAA,CAAA,KAAA,CAHA,CAIA;;AACA,kBAAA,cAAA,CAAA,OAAA,CAAA,MAAA,EAAA,IAAA,CAAA,SAAA,CAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA;;AACA,kBAAA,MAAA,CAAA,OAAA,CAAA,EAAA,CAAA,IAAA,CAAA,IAAA;AACA;AACA,eAVA;AAWA;AACA,WAzBA,CA2BA;;;AACA,cAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,IAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA;AACA,YAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,KAAA,GAAA,CAAA,CADA,CAEA;;AACA,YAAA,MAAA,CAAA,IAAA,CAAA,OAAA,EAAA;AACA,cAAA,EAAA,EAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,EADA;AAEA,cAAA,KAAA,EAAA;AAFA,aAAA;AAIA;;AACA,UAAA,cAAA,CAAA,OAAA,CAAA,MAAA,EAAA,IAAA,CAAA,SAAA,CAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA;;AACA,UAAA,MAAA,CAAA,OAAA,CAAA,IAAA,CAAA,KAAA,CAAA,cAAA,CAAA,OAAA,CAAA,MAAA,CAAA,CAAA,EArCA,CAsCA;;;AACA,UAAA,MAAA,CAAA,SAAA,GAAA,EAAA;;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,QAAA;AACA;AACA,OA7CA;AA8CA,KA7bA;AA8bA,IAAA,IA9bA,kBA8bA;AACA,MAAA,cAAA,CAAA,UAAA,CAAA,MAAA;AACA,WAAA,OAAA,CAAA,IAAA,CAAA,GAAA;AACA,KAjcA;AAkcA;AACA,IAAA,UAncA,sBAmcA,QAncA,EAmcA;AACA,UAAA,GAAA,GAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA;;AACA,WAAA,IAAA,CAAA,IAAA,GAAA,EAAA;AACA,aAAA,SAAA,IAAA,GAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAAA,CAAA;AACA;;AACA,WAAA,SAAA,GAAA,KAAA,SAAA,CAAA,iBAAA,EAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,gBAAA,KAAA,SAAA;AACA,KA1cA;AA4cA;AACA,IAAA,0BA7cA,wCA6cA;AAAA;;AACA,UAAA,KAAA,IAAA,IAAA,KAAA,IAAA,CAAA,EAAA,EAAA;AACA,aAAA,IAAA,CAAA,kCAAA,EAAA;AAAA,UAAA,MAAA,EAAA,KAAA,IAAA,CAAA;AAAA,SAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA;AACA,SALA,EAMA,KANA,CAMA,UAAA,GAAA,EAAA;AACA,UAAA,OAAA,CAAA,KAAA,CAAA,aAAA,EAAA,GAAA;AACA,SARA;AASA;AACA;AAzdA,IAFA;AA6dA,EAAA,QAAA,kCACA,QAAA,CAAA,MAAA,EAAA,CAAA,MAAA,CAAA,CADA;AAEA,IAAA,KAFA,mBAEA;AACA,aAAA,KAAA,MAAA,CAAA,KAAA,CAAA,KAAA,CAAA,KAAA;AACA;AAJA,IA7dA;AAmeA,EAAA,IAneA,kBAmeA;AACA,QAAA,gBAAA,GAAA,SAAA,gBAAA,CAAA,IAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AACA,UAAA,KAAA,KAAA,EAAA,EAAA;AACA,QAAA,QAAA,CAAA,IAAA,KAAA,CAAA,OAAA,CAAA,CAAA;AACA,OAFA,MAEA;AACA,QAAA,QAAA;AACA;AACA,KANA;;AAOA,QAAA,aAAA,GAAA,SAAA,aAAA,CAAA,IAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AACA,UAAA,KAAA,KAAA,EAAA,EAAA;AACA,QAAA,QAAA,CAAA,IAAA,KAAA,CAAA,QAAA,CAAA,CAAA;AACA,OAFA,MAEA;AACA,QAAA,QAAA;AACA;AACA,KANA;;AAQA,WAAA;AACA;AACA,MAAA,SAAA,EAAA,EAFA;AAGA,MAAA,QAAA,EAAA;AACA,QAAA,QAAA,EAAA,EADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,MAAA,EAAA,IAHA;AAIA,QAAA,OAAA,EAAA,IAJA;AAKA,QAAA,OAAA,EAAA;AALA,OAHA;AAUA;AACA,MAAA,aAAA,EAAA,EAXA;AAYA,MAAA,eAAA,EAAA,EAZA;AAaA;AACA,MAAA,WAAA,EAAA,EAdA;AAeA,MAAA,YAAA,EAAA,EAfA;AAiBA;AACA,MAAA,aAAA,EAAA,EAlBA;AAmBA,MAAA,aAAA,EAAA,EAnBA;AAoBA,MAAA,KAAA,EAAA;AACA,QAAA,QAAA,EAAA,CACA;AAAA,UAAA,SAAA,EAAA,gBAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,KAAA,EAAA,CACA;AAAA,UAAA,SAAA,EAAA,aAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AAJA,OApBA;AA4BA;AACA,MAAA,UAAA,EAAA;AAAA,cAAA,MAAA;AAAA,iBAAA;AAAA,OA7BA;AA8BA;AACA,MAAA,GAAA,EAAA,GA/BA;AAgCA,MAAA,MAAA,EAAA,KAhCA;AAiCA;AACA,MAAA,SAAA,EAAA,EAlCA;AAmCA;AACA,MAAA,WAAA,EAAA,QAAA,CAAA,eAAA,CAAA,WApCA;AAqCA,MAAA,WAAA,EAAA,GArCA;AAsCA;AACA,MAAA,UAAA,EAAA,KAvCA;AAwCA,MAAA,KAAA,EAAA,EAxCA;AAyCA;AACA,MAAA,IAAA,EAAA,EA1CA;AA2CA;AACA,MAAA,KAAA,EAAA,EA5CA;AA6CA,MAAA,aAAA,EAAA,KA7CA;AA8CA;AACA,MAAA,WAAA,EAAA;AA/CA,KAAA;AAiDA,GApiBA;AAqiBA,EAAA,KAAA,EAAA;AACA,YADA,kBACA,EADA,EACA,IADA,EACA;AACA,WAAA,aAAA;AACA;AAHA,GAriBA;AA0iBA,EAAA,OA1iBA,qBA0iBA;AAAA;;AACA;AACA,SAAA,aAAA,GAAA,UAAA;AAEA,QAAA,KAAA,GAAA,IAAA,CAAA,KAAA,CAAA,cAAA,CAAA,OAAA,CAAA,YAAA,CAAA,CAAA;;AACA,QAAA,KAAA,EAAA;AACA,WAAA,UAAA,GAAA;AAAA,cAAA,KAAA,CAAA,KAAA;AAAA,iBAAA,KAAA,CAAA;AAAA,OAAA;AACA;;AAEA,QAAA,cAAA,CAAA,OAAA,CAAA,MAAA,CAAA,EAAA;AACA,WAAA,aAAA;AACA,UAAA,QAAA,GAAA,IAAA,CAAA,KAAA,CAAA,cAAA,CAAA,OAAA,CAAA,MAAA,CAAA,CAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,yBAAA,EAAA,QAAA;AACA,WAAA,OAAA,CAAA,QAAA,EAJA,CAMA;;AACA,UAAA,KAAA,IAAA,CAAA,IAAA,KAAA,KAAA,IAAA,CAAA,IAAA,CAAA,EAAA,KAAA,EAAA,IAAA,KAAA,IAAA,CAAA,IAAA,CAAA,EAAA,KAAA,EAAA,CAAA,EAAA;AACA,aAAA,0BAAA,GADA,CAGA;;AACA,QAAA,WAAA,CAAA,YAAA;AACA,UAAA,OAAA,CAAA,0BAAA;AACA,SAFA,EAEA,KAFA,CAAA;AAGA,OAdA,CAeA;AACA;AACA;;;AACA,UAAA,KAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA;AACA,aAAA,IAAA,CAAA,WAAA,KAAA,IAAA,CAAA,IAAA,CAAA,EAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,GAAA,CAAA,IAAA;AACA,UAAA,OAAA,CAAA,IAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA;AACA,UAAA,OAAA,CAAA,MAAA,GAAA,IAAA;;AACA,UAAA,OAAA,CAAA,IAAA,CAAA,YAAA,EAAA,SAAA;AACA,SANA;AAOA;AACA,KA3BA,MA2BA;AACA,WAAA,IAAA,CAAA,YAAA,EAAA,OAAA;AACA,WAAA,OAAA,CAAA,IAAA,CAAA,GAAA;AACA;AAEA,GAnlBA;AAolBA,EAAA,OAplBA,qBAolBA;AAAA;;AACA;AACA,IAAA,MAAA,CAAA,QAAA,GAAA,YAAA;AACA,MAAA,OAAA,CAAA,WAAA,GAAA,QAAA,CAAA,eAAA,CAAA,WAAA;AACA,KAFA,CAFA,CAKA;;;AACA,SAAA,UAAA,CAAA,KAAA,IAAA,CAAA,QAAA;AACA;AA3lBA,CAAA", "sourcesContent": ["<template>\n    <div class=\"main\">\n        <div class=\"left\" :style=\"{width:isCollapse?'64px':'200px',background:themeColor.bg,color:themeColor.color}\" style=\"transition: .3s;\">\n            <div class=\"logo\">\n<!--                <img src=\"@s/assets/logo.jpg\" style=\"width: 26%\">-->\n            </div>\n            <el-menu\n                    :collapse-transition=\"false\"\n                    :collapse=\"isCollapse\"\n                    :router=\"true\"\n                    :default-active=\"$route.path\"\n                    :background-color=\"themeColor.bg\"\n                    :text-color=\"themeColor.color\"\n                    :unique-opened=\"true\">\n                <el-menu-item index=\"/home/\">\n                    <i class=\"el-icon-s-home\"></i>\n                    <span>首页</span>\n                </el-menu-item>\n\n                <el-submenu index=\"1\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-office-building\"></i>\n                        <span>任务管理</span>\n                    </template>\n                    <el-menu-item v-if=\"user.role.id === 14\" index=\"/home/<USER>\">\n                        <i class=\"el-icon-s-order\"></i>\n                        <span>发布任务</span>\n                    </el-menu-item>\n                    <el-menu-item v-if=\"user.role.id === 13\" index=\"/home/<USER>\">\n                        <i class=\"el-icon-s-order\"></i>\n                        <span>接受任务</span>\n                    </el-menu-item>\n                </el-submenu>\n\n\n                <el-submenu index=\"2\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-paperclip\"></i>\n                        <span>订单管理</span>\n                    </template>\n                    <el-menu-item v-if=\"user.role.id === 14\" index=\"/home/<USER>\">\n                        <i class=\"el-icon-s-order\"></i>\n                        <span>已发布任务</span>\n                    </el-menu-item>\n\n                    <el-menu-item v-if=\"user.role.id === 13\" index=\"/home/<USER>\">\n                        <i class=\"el-icon-s-order\"></i>\n                        <span>已接受任务</span>\n                    </el-menu-item>\n                </el-submenu>\n\n\n                <el-submenu index=\"3\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-s-custom\"></i>\n                        <span>公告管理</span>\n                    </template>\n                    <el-menu-item index=\"/home/<USER>\">\n                    <i class=\"el-icon-s-order\"></i>\n                        <span>查看公告</span>\n                    </el-menu-item>\n                </el-submenu>\n\n                <el-submenu index=\"9\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-s-custom\"></i>\n                        <span>评价管理</span>\n                    </template>\n                     <el-menu-item v-if=\"user.role.id === 14\" index=\"/home/<USER>\">\n                        <i class=\"el-icon-s-order\"></i>\n                        <span>我发布的评价</span>\n                    </el-menu-item>\n                    <el-menu-item v-if=\"user.role.id === 13\" index=\"/home/<USER>\">\n                    <i class=\"el-icon-s-order\"></i>\n                    <span>查看评价</span>\n                    </el-menu-item>\n                </el-submenu>\n\n                <!-- 论坛入口，只对维修员和管理员可见，用户角色ID是14 -->\n                <el-submenu index=\"10\" v-if=\"user.role.id !== 14\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-chat-dot-square\"></i>\n                        <span>维修员论坛</span>\n                    </template>\n                    <el-menu-item index=\"/home/<USER>/posts\">\n                        <i class=\"el-icon-document\"></i>\n                        <span>帖子列表</span>\n                    </el-menu-item>\n                    <el-menu-item index=\"/home/<USER>/my-posts\">\n                        <i class=\"el-icon-s-order\"></i>\n                        <span>我的帖子</span>\n                    </el-menu-item>\n                    <!-- 管理员角色不是14（用户角色）也不是13（维修员角色） -->\n                    <el-menu-item v-if=\"user.role.id !== 14 && user.role.id !== 13\" index=\"/home/<USER>/audit\">\n                        <i class=\"el-icon-s-check\"></i>\n                        <span>帖子审核</span>\n                    </el-menu-item>\n                    <el-menu-item index=\"/home/<USER>/notifications\">\n                        <i class=\"el-icon-bell\"></i>\n                        <span>消息通知</span>\n                        <el-badge v-if=\"unreadCount > 0\" :value=\"unreadCount\" class=\"notification-badge\" />\n                    </el-menu-item>\n                </el-submenu>\n\n                <el-submenu index=\"11\">\n                    <template slot=\"title\">\n                        <i class=\"el-icon-s-custom\"></i>\n                        <span>个人中心</span>\n                    </template>\n                    <el-menu-item index=\"/home/<USER>\">个人信息\n                    </el-menu-item>\n                </el-submenu>\n\n                <el-menu-item v-if=\"user.role.id === 14\">\n                    <span>当前余额: {{ user.balance }}元</span>\n                    <!-- <el-button type=\"text\" @click=\"recharge(user.studentId)\">充值余额</el-button> -->\n                </el-menu-item>\n            </el-menu>\n        </div>\n\n        <div class=\"right\"\n             :style=\"{width:isCollapse?windowWidth-64+'px':windowWidth-200+'px',left:isCollapse?'64px':'200px'}\">\n            <div class=\"top\"\n                 :style=\"{width:isCollapse?windowWidth-64+'px':windowWidth-200+'px',left:isCollapse?'64px':'200px',background:themeColor.bg}\">\n                <div class=\"icon\" @click=\"isCollapse = !isCollapse\">\n                    <i :class=\"isCollapse?'el-icon-s-unfold':'el-icon-s-fold'\" :style=\"{color:themeColor.color}\"></i>\n                </div>\n                <el-menu\n\n                        :unique-opened=\"true\"\n                        :default-active=\"activeIndex\"\n                        class=\"el-menu-demo\"\n                        mode=\"horizontal\"\n                        :background-color=\"themeColor.bg\"\n                        :text-color=\"themeColor.color\"\n                        :active-text-color=\"themeColor.color\"\n                        menu-trigger=\"click\">\n\n                    <el-menu-item v-if=\"user.role.id === 14\" @click=\"recharge(user.studentId)\">充值余额</el-menu-item>\n<!--                    <el-menu-item @click=\"recharge(user.studentId)\">充值余额</el-menu-item>-->\n\n                    <el-submenu index=\"1\">\n                        <template slot=\"title\">更换主题</template>\n                        <el-menu-item v-for=\"item in theme\" @click=\"changeColor(item)\">\n                            {{item.name}}\n                        </el-menu-item>\n                    </el-submenu>\n                    <el-submenu index=\"2\">\n<!--                        <template slot=\"title\">{{user.username}}</template>-->\n                        <el-avatar slot=\"title\" style=\"background: #65c4a6; user-select: none;\">{{firstName}}</el-avatar>\n                        <el-menu-item index=\"2-1\" @click=\"exit\">退出</el-menu-item>\n                        <el-menu-item index=\"2-2\" @click=\"updPassword(user.id)\">修改密码</el-menu-item>\n                        <el-menu-item index=\"2-3\" @click=\"personalInformation()\">修改个人信息</el-menu-item>\n                    </el-submenu>\n                </el-menu>\n\n            </div>\n            <div class=\"bottom\">\n                <div class=\"bottom_top\">\n                    <el-breadcrumb separator-class=\"el-icon-arrow-right\">\n                        <el-breadcrumb-item v-for=\"item in breadList\" :to=\"item.path\" v-if=\"item.meta.title\">\n                            {{item.meta.title}}\n                        </el-breadcrumb-item>\n                    </el-breadcrumb>\n                </div>\n                <transition name=\"el-fade-in\" mode=\"out-in\">\n                    <router-view @personalInformation=\"personalInformation\"></router-view>\n                </transition>\n            </div>\n        </div>\n\n        <el-drawer\n                title=\"完善信息\"\n                :visible.sync=\"drawer\"\n                direction=\"rtl\"\n                closeDrawer=\"false\"\n                :show-close=\"false\"\n                :before-close=\"handleClose\">\n            <el-form :model=\"ruleForm\" status-icon :rules=\"rules\" ref=\"ruleForm\" label-width=\"100px\"\n                     class=\"demo-ruleForm ruleform\">\n\n                <!-- 根据角色 ID 动态显示班级信息 -->\n                <el-form-item label=\"类别\" v-if=\"user.role.id !== 14\">\n                    <el-cascader\n                            v-model=\"value\"\n                            :options=\"role.depts\"\n                            :props=\"{\n                children:'classes',\n                label:'name',\n                value:'id'\n            }\"\n                    ></el-cascader>\n                </el-form-item>\n\n                <el-form-item label=\"姓名\" prop=\"username\">\n                    <el-input v-model=\"ruleForm.username\"></el-input>\n                </el-form-item>\n                <el-form-item label=\"工号\" prop=\"phone\">\n                    <el-input v-model.number=\"ruleForm.phone\"></el-input>\n                </el-form-item>\n\n                <el-form-item label=\"性别\">\n                    <el-radio-group v-model=\"sex\">\n                        <el-radio label=\"0\">男</el-radio>\n                        <el-radio label=\"1\">女</el-radio>\n                    </el-radio-group>\n                </el-form-item>\n\n                <el-form-item>\n                    <el-button type=\"primary\" @click=\"submitForm('ruleForm')\">提交</el-button>\n                </el-form-item>\n            </el-form>\n        </el-drawer>\n\n        <el-dialog title=\"修改信息\" :visible.sync=\"dialogVisible\" :close-on-click-modal=\"false\">\n            <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\n                <el-form-item label=\"姓名\" prop=\"username\">\n                    <el-input v-model.number=\"ruleForm.username\"></el-input>\n                </el-form-item>\n                <el-form-item label=\"手机号\" prop=\"phone\">\n                    <el-input v-model.number=\"ruleForm.phone\" oninput=\"if(value.length>11)value=value.slice(0,11)\"></el-input>\n                </el-form-item>\n\n                <!-- 维修员可以修改类别和子类别 -->\n                <template v-if=\"user.role && user.role.id === 13\">\n                    <el-form-item label=\"类别-子类别\" prop=\"categoryValue\">\n                        <el-cascader\n                            v-model=\"categoryValue\"\n                            :options=\"categoryOptions\"\n                            :props=\"{\n                                children: 'children',\n                                label: 'name',\n                                value: 'id',\n                                checkStrictly: false\n                            }\"\n                            placeholder=\"请选择类别和子类别\"\n                            clearable\n                        ></el-cascader>\n                    </el-form-item>\n\n                    <!-- 维修员地址信息 -->\n                    <el-form-item label=\"省市区\" prop=\"addressRegion\">\n                        <el-cascader\n                            v-model=\"addressRegion\"\n                            :options=\"regionOptions\"\n                            placeholder=\"请选择省/市/区\"\n                            style=\"width: 100%\"\n                        ></el-cascader>\n                    </el-form-item>\n                    <el-form-item label=\"详细地址\" prop=\"address\">\n                        <el-input\n                            v-model=\"ruleForm.address\"\n                            type=\"textarea\"\n                            placeholder=\"请输入详细地址信息，如街道、门牌号等\"\n                            :rows=\"3\"\n                        ></el-input>\n                    </el-form-item>\n                </template>\n\n            </el-form>\n            <div slot=\"footer\" class=\"dialog-footer\">\n                <el-button @click=\"dialogVisible = false\">取 消</el-button>\n                <el-button type=\"primary\" @click=\"submitChanges\">确 定</el-button>\n            </div>\n        </el-dialog>\n    </div>\n</template>\n\n<script>\n    import {mapState, mapMutations} from \"vuex\"\n    import user from \"@s/store/module/user\";\n    import regionData from '@/assets/data/region.js';\n\n    export default {\n        name: \"Home\",\n        methods: {\n            ...mapMutations('user', ['setUser']),\n            changeColor(val){\n                sessionStorage.setItem(\"themeColor\",JSON.stringify(val))\n                this.themeColor = {'bg':val.value,'color':val.color};\n            },\n            //面包屑\n            getBreadcrumb() {\n                let matched = this.$route.matched;\n                if (matched[0].name != 'home') {\n                    matched = [{path: \"/home/\", meta: {title: '首页'}}].concat(matched)\n                }\n                this.breadList = matched;\n            },\n            //关闭抽屉触发的事件\n            handleClose(done) {\n                // 如果是首次登录且未完善信息，不允许关闭抽屉\n                if (this.user.state === 0) {\n                    this.$msg(\"首次登录必须完善信息\", \"error\")\n                } else {\n                    // 如果不是首次登录或已完善信息，允许关闭抽屉\n                    done()\n                }\n            },\n            submitForm(formName) {\n                this.$refs[formName].validate((valid) => {\n                    if (valid) {\n                        // 根据角色ID决定是否需要选择类别\n                        if ((this.user.role.id !== 14 && this.value) || this.user.role.id === 14) {\n                            const userData = {\n                                id: this.user.id,\n                                deptId: this.user.role.id !== 14 ? this.value[0] : null,\n                                // classId 字段在数据库中不存在，移除该字段\n                                username: this.ruleForm.username,\n                                phone: this.ruleForm.phone,\n                                sex: this.sex,\n                                // 设置状态为已完善信息\n                                state: 1\n                            }\n\n                            this.$put(\"/user\", userData)\n                                .then(res => {\n                                    if (res.data.status) {\n                                        this.drawer = false;\n                                        this.$notifyMsg(\"成功\", res.data.msg, \"success\")\n                                        // 更新本地用户信息\n                                        if (res.data.user) {\n                                            sessionStorage.setItem('user', JSON.stringify(res.data.user))\n                                            this.setUser(res.data.user)\n                                        }\n                                        this.newList(this.user.id)\n                                    } else {\n                                        this.$msg(res.data.msg, \"error\")\n                                    }\n                                })\n                                .catch(err => {\n                                    console.error('Update failed:', err)\n                                    this.$msg(err.response?.data?.msg || \"更新失败，请稍后重试\", \"error\")\n                                })\n                        } else {\n                            this.$notifyMsg(\"错误\", \"请选择类别\", \"error\")\n                        }\n                    } else {\n                        return false;\n                    }\n                });\n            },\n            //修改密码\n            updPassword(id) {\n                this.$prompt('请输入密码', '提示', {\n                    confirmButtonText: '确定',\n                    cancelButtonText: '取消',\n                    inputType: 'password',\n                    closeOnClickModal:false,\n                    inputPattern: /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,16}$/,\n                    inputErrorMessage: '格式不对，密码只能输入6-16位英文和数字'\n                }).then((res) => {\n                    // console.log(res);\n                    this.$put('/user', {id: id, password: res.value})\n                    .then((res) => {\n                        this.$notifyMsg('成功', res.data.msg, 'success')\n                    })\n                }).catch(() => {\n                })\n            },\n            recharge(studentId) {\n                this.$prompt('请输入充值金额', '充值', {\n                    confirmButtonText: '确定',\n                    cancelButtonText: '取消',\n                    inputPattern: /^(0\\.\\d{1,2}|[1-9]\\d*(\\.\\d{1,2})?)$/,\n                    inputErrorMessage: '请输入有效金额（最多两位小数）'\n                }).then(({ value }) => {\n                    const amount = parseFloat(value);\n\n                    // 验证金额范围\n                    if (amount < 0.01 || amount > 10000) {\n                        this.$msg(\"充值金额必须在0.01-10000元之间\", \"error\");\n                        return;\n                    }\n\n                    // 创建支付宝支付订单\n                    const payData = {\n                        subject: '用户充值',\n                        totalAmount: amount,\n                        userId: this.user.id\n                    };\n\n                    // 调用后端创建支付订单接口\n                    this.$post('api/alipay/create', payData)\n                        .then(res => {\n                            if (res.data.status) {\n                                // 在新窗口中打开支付页面\n                                const payWindow = window.open('', '_blank', 'width=800,height=600');\n                                payWindow.document.write(res.data.data.payForm);\n                                payWindow.document.close();\n\n                                // 定义轮询次数和间隔\n                                let pollCount = 0;\n                                const maxPolls = 60; // 最多轮询60次\n                                const pollInterval = 3000; // 每3秒轮询一次\n\n                                // 获取订单号\n                                const orderNo = res.data.data.orderNo;\n                                \n                                // 标记是否已经显示过成功消息\n                                let hasShownSuccess = false;\n\n                                // 开始轮询支付状态\n                                const checkPayment = setInterval(() => {\n                                    // 如果支付窗口关闭或达到最大轮询次数，停止轮询\n                                    if (pollCount >= maxPolls || payWindow.closed) {\n                                        clearInterval(checkPayment);\n                                        \n                                        // 最后查询一次支付状态\n                                        this.$get(`api/alipay/query/${orderNo}`)\n                                            .then(queryRes => {\n                                                if (queryRes.data.status && queryRes.data.data.status === 1) {\n                                                    // 支付成功，但只在未显示过成功消息时显示\n                                                    if (!hasShownSuccess) {\n                                                        this.newList(this.user.id);\n                                                        this.$msg(\"充值成功\", \"success\");\n                                                        hasShownSuccess = true;\n                                                    }\n                                                } else if (!hasShownSuccess) {\n                                                    // 支付未完成，且未显示过成功消息\n                                                    this.$msg(\"支付未完成，如已支付请稍后刷新查看\", \"warning\");\n                                                }\n                                            })\n                                            .catch(() => {\n                                                if (!hasShownSuccess) {\n                                                    this.$msg(\"查询支付状态失败，如已支付请稍后刷新查看\", \"warning\");\n                                                }\n                                            });\n                                        return;\n                                    }\n\n                                    // 定期查询支付状态\n                                    this.$get(`api/alipay/query/${orderNo}`)\n                                        .then(queryRes => {\n                                            if (queryRes.data.status && queryRes.data.data.status === 1 && !hasShownSuccess) {\n                                                // 支付成功且未显示过成功消息\n                                                clearInterval(checkPayment);\n                                                this.newList(this.user.id);\n                                                this.$msg(\"充值成功\", \"success\");\n                                                hasShownSuccess = true;\n                                                if (!payWindow.closed) {\n                                                    payWindow.close(); // 自动关闭支付窗口\n                                                }\n                                            }\n                                        })\n                                        .catch(err => {\n                                            console.error('查询支付状态失败:', err);\n                                        });\n\n                                    pollCount++;\n                                }, pollInterval);\n\n                            } else {\n                                this.$msg(res.data.msg || \"创建支付订单失败\", \"error\");\n                            }\n                        })\n                        .catch(err => {\n                            console.error('创建支付订单失败:', err);\n                            this.$msg(\"创建支付订单失败，请稍后重试\", \"error\");\n                        });\n                }).catch(() => {\n                    this.$msg(\"已取消充值\", \"info\");\n                });\n            },\n            personalInformation() {\n                this.dialogVisible = true;\n                this.ruleForm.username = this.user.username;\n                this.ruleForm.phone = this.user.phone;\n\n                // 如果是维修员，加载类别和子类别数据\n                if (this.user.role && this.user.role.id === 13) {\n                    // 设置当前的类别和子类别\n                    if (this.user.dept) {\n                        this.ruleForm.deptId = this.user.dept.id;\n                    }\n                    if (this.user.type) {\n                        this.ruleForm.classId = this.user.type.id;\n                    }\n\n                    // 设置级联选择器的初始值\n                    if (this.user.dept && this.user.type) {\n                        this.categoryValue = [this.user.dept.id, this.user.type.id];\n                    } else if (this.user.dept) {\n                        this.categoryValue = [this.user.dept.id];\n                    } else {\n                        this.categoryValue = [];\n                    }\n\n                    // 设置地址选择器的初始值\n                    if (this.user.province && this.user.city && this.user.district) {\n                        this.addressRegion = [this.user.province, this.user.city, this.user.district];\n                        this.ruleForm.address = this.user.address || '';\n                    } else {\n                        this.addressRegion = [];\n                        this.ruleForm.address = '';\n                    }\n\n                    // 加载所有可用的类别和子类别\n                    this.loadCategoryOptions();\n                }\n            },\n\n            // 加载类别和子类别的级联选项\n            loadCategoryOptions() {\n                // 先获取所有类别\n                this.$get(\"/dept/list\", { roleId: 13 })\n                .then(res => {\n                    if (res.data.status && res.data.dept) {\n                        // 保存原始类别列表（兼容性考虑）\n                        this.deptOptions = res.data.dept;\n                        console.log('可用的类别列表:', this.deptOptions);\n\n                        // 为每个类别加载子类别\n                        const promises = res.data.dept.map(dept => {\n                            return this.$get(\"/class/list\", { deptId: dept.id })\n                            .then(classRes => {\n                                if (classRes.data.status && classRes.data.class) {\n                                    // 返回带有子类别的类别对象\n                                    return {\n                                        ...dept,\n                                        children: classRes.data.class\n                                    };\n                                }\n                                // 如果没有子类别，返回原始类别对象\n                                return {\n                                    ...dept,\n                                    children: []\n                                };\n                            })\n                            .catch(() => {\n                                // 出错时返回没有子类别的类别对象\n                                return {\n                                    ...dept,\n                                    children: []\n                                };\n                            });\n                        });\n\n                        // 等待所有子类别加载完成\n                        Promise.all(promises)\n                        .then(categories => {\n                            this.categoryOptions = categories;\n                            console.log('级联选择器选项:', this.categoryOptions);\n                        });\n                    } else {\n                        this.$msg(\"获取类别列表失败\", \"error\");\n                    }\n                })\n                .catch(err => {\n                    console.error('获取类别列表失败:', err);\n                    this.$msg(\"获取类别列表失败，请稍后重试\", \"error\");\n                });\n            },\n\n            // 保留原有的方法（兼容性考虑）\n            loadDepartments() {\n                this.$get(\"/dept/list\", { roleId: 13 })\n                .then(res => {\n                    if (res.data.status && res.data.dept) {\n                        this.deptOptions = res.data.dept;\n                        console.log('可用的类别列表:', this.deptOptions);\n                    } else {\n                        this.$msg(\"获取类别列表失败\", \"error\");\n                    }\n                })\n                .catch(err => {\n                    console.error('获取类别列表失败:', err);\n                    this.$msg(\"获取类别列表失败，请稍后重试\", \"error\");\n                });\n            },\n\n            // 保留原有的方法（兼容性考虑）\n            loadSubCategories(deptId) {\n                if (!deptId) return;\n\n                this.$get(\"/class/list\", { deptId: deptId })\n                .then(res => {\n                    if (res.data.status && res.data.class) {\n                        this.classOptions = res.data.class;\n                        console.log('可用的子类别列表:', this.classOptions);\n\n                        // 如果当前选择的子类别不在新的子类别列表中，清空选择\n                        const exists = this.classOptions.some(item => item.id === this.ruleForm.classId);\n                        if (!exists) {\n                            this.ruleForm.classId = null;\n                        }\n                    } else {\n                        this.classOptions = [];\n                        this.ruleForm.classId = null;\n                        this.$msg(\"获取子类别列表失败\", \"error\");\n                    }\n                })\n                .catch(err => {\n                    console.error('获取子类别列表失败:', err);\n                    this.classOptions = [];\n                    this.ruleForm.classId = null;\n                    this.$msg(\"获取子类别列表失败，请稍后重试\", \"error\");\n                });\n            },\n            // submitChanges(){\n            //     this.$put(\"/user\",{id:this.user.id,username:this.ruleForm.username,phone:this.ruleForm.phone})\n            //     .then(res=>{\n            //         this.$notifyMsg(\"成功\",res.data.msg,\"success\",1000);\n            //         this.dialogVisible = false;\n            //         this.newList(this.user.id)\n            //     })\n            // },\n            submitChanges() {\n                // 准备要更新的用户数据\n                const userData = {\n                    id: this.user.id,\n                    username: this.ruleForm.username,\n                    phone: this.ruleForm.phone,\n                    state: 1\n                };\n\n                // 如果是维修员，添加类别和子类别信息\n                if (this.user.role && this.user.role.id === 13) {\n                    // 从级联选择器中获取类别和子类别ID\n                    if (this.categoryValue && this.categoryValue.length > 0) {\n                        // 第一个值是类别ID\n                        userData.deptId = this.categoryValue[0];\n\n                        // 如果选择了子类别（数组长度大于1），第二个值是子类别ID\n                        if (this.categoryValue.length > 1) {\n                            userData.classId = this.categoryValue[1];\n                        }\n                    }\n                    // 兼容旧版本的选择方式\n                    else {\n                        // 只有当选择了类别时才更新类别ID\n                        if (this.ruleForm.deptId) {\n                            userData.deptId = this.ruleForm.deptId;\n                        }\n\n                        // 只有当选择了子类别时才更新子类别ID\n                        if (this.ruleForm.classId) {\n                            userData.classId = this.ruleForm.classId;\n                        }\n                    }\n\n                    // 处理地址信息\n                    if (this.addressRegion && this.addressRegion.length === 3) {\n                        userData.province = this.addressRegion[0];\n                        userData.city = this.addressRegion[1];\n                        userData.district = this.addressRegion[2];\n                    }\n\n                    // 如果有详细地址，添加到更新数据中\n                    if (this.ruleForm.address) {\n                        userData.address = this.ruleForm.address;\n                    }\n                }\n\n                console.log('提交的用户数据:', userData);\n\n                // 发送更新请求\n                this.$put(\"/user\", userData)\n                    .then(res => {\n                        if (res.data.status) {\n                            this.$notifyMsg(\"成功\", res.data.msg, \"success\");\n                            this.dialogVisible = false;\n                            this.newList(this.user.id);\n                        } else {\n                            this.$msg(res.data.msg || \"更新失败\", \"error\");\n                        }\n                    })\n                    .catch(err => {\n                        console.error('更新用户信息失败:', err);\n                        this.$msg(\"更新失败，请稍后重试\", \"error\");\n                    });\n            },\n            //根据当前用户查询id\n            newList(id) {\n                this.$get(\"/user/\" + id)\n                .then((rs) => {\n                    // 确保用户状态正确更新\n                    if (rs.data.user) {\n                        console.log('User data from API:', JSON.stringify(rs.data.user, null, 2));\n                        console.log('User role ID:', rs.data.user.role ? rs.data.user.role.id : 'No role');\n                        console.log('User dept:', rs.data.user.dept ? JSON.stringify(rs.data.user.dept, null, 2) : 'No dept data');\n                        console.log('User type:', rs.data.user.type ? JSON.stringify(rs.data.user.type, null, 2) : 'No type data');\n\n                        // 如果是维修员但没有type信息，尝试重新获取用户数据\n                        if (rs.data.user.role && rs.data.user.role.id === 13 && !rs.data.user.type) {\n                            console.log('维修员没有type信息，尝试修复...');\n                            // 尝试手动设置type信息\n                            if (rs.data.user.classId) {\n                                console.log('找到classId:', rs.data.user.classId);\n                                // 获取type信息\n                                this.$get(\"/class/\" + rs.data.user.classId)\n                                .then(typeRes => {\n                                    if (typeRes.data.class) {\n                                        console.log('获取到type信息:', JSON.stringify(typeRes.data.class, null, 2));\n                                        // 手动设置type信息\n                                        rs.data.user.type = typeRes.data.class;\n                                        // 更新用户信息\n                                        sessionStorage.setItem(\"user\", JSON.stringify(rs.data.user));\n                                        this.setUser(rs.data.user);\n                                    }\n                                });\n                            }\n                        }\n\n                        // 如果用户已完善信息，确保状态为1\n                        if (rs.data.user.dept && rs.data.user.state === 0) {\n                            rs.data.user.state = 1;\n                            // 更新用户状态\n                            this.$put(\"/user\", {\n                                id: rs.data.user.id,\n                                state: 1\n                            });\n                        }\n                        sessionStorage.setItem(\"user\", JSON.stringify(rs.data.user))\n                        this.setUser(JSON.parse(sessionStorage.getItem(\"user\")))\n                        // 修改完名字, 清空當前firstName; 避免出現疊加\n                        this.firstName = '';\n                        this.textAvatar(rs.data.user.username);\n                    }\n                })\n            },\n            exit(){\n                sessionStorage.removeItem('user');\n                this.$router.push('/')\n            },\n            // 文字頭像\n            textAvatar(username) {\n                let arr = username.split(' ');\n                for (var i in arr) {\n                    this.firstName += arr[i].substr(0,1);\n                }\n                this.firstName = this.firstName.toLocaleUpperCase();\n                console.log('firstName->' + this.firstName);\n            },\n\n            // 获取未读通知数量\n            getUnreadNotificationCount() {\n                if (this.user && this.user.id) {\n                    this.$get('/forum/notification/unread/count', { userId: this.user.id })\n                    .then(res => {\n                        if (res.data.status) {\n                            this.unreadCount = res.data.count;\n                        }\n                    })\n                    .catch(err => {\n                        console.error('获取未读通知数量失败:', err);\n                    });\n                }\n            }\n        },\n        computed: {\n            ...mapState('user', ['user']),\n            theme(){\n                return this.$store.state.theme.theme\n            },\n        },\n        data() {\n            var validateUsername = (rule, value, callback) => {\n                if (value === '') {\n                    callback(new Error('请输入姓名'));\n                } else {\n                    callback();\n                }\n            };\n            var validatePhone = (rule, value, callback) => {\n                if (value === '') {\n                    callback(new Error('请输入手机号'));\n                } else {\n                    callback();\n                }\n            };\n\n            return {\n                // 文字头像\n                firstName:'',\n                ruleForm: {\n                    username: '',\n                    phone: '',\n                    deptId: null,\n                    classId: null,\n                    address: ''\n                },\n                // 级联选择器的值和选项\n                categoryValue: [],\n                categoryOptions: [],\n                // 保留原有的选项（兼容性考虑）\n                deptOptions: [],\n                classOptions: [],\n\n                // 地址相关\n                addressRegion: [],\n                regionOptions: [],\n                rules: {\n                    username: [\n                        {validator: validateUsername, trigger: 'blur'}\n                    ],\n                    phone: [\n                        {validator: validatePhone, trigger: 'blur'}\n                    ]\n                },\n                //颜色\n                themeColor : {'bg':'#fff','color':'#000'},\n                //性别\n                sex:'0',\n                drawer: false,\n                //当前路由\n                breadList: [],\n                //当前屏幕宽度\n                windowWidth: document.documentElement.clientWidth,\n                activeIndex: '1',\n                //控制菜单是否展开\n                isCollapse: false,\n                admin: \"\",\n                // role: [],\n                role:[],\n                //级联选择器的值\n                value: \"\",\n                dialogVisible:false,\n                // 未读通知数量\n                unreadCount: 0\n            }\n        },\n        watch: {\n            '$route'(to, form) {\n                this.getBreadcrumb()\n            }\n        },\n        created() {\n            // 初始化地址数据\n            this.regionOptions = regionData;\n\n            let theme = JSON.parse(sessionStorage.getItem(\"themeColor\"));\n            if (theme){\n                this.themeColor = {'bg':theme.value,'color':theme.color}\n            }\n\n            if (sessionStorage.getItem('user')){\n                this.getBreadcrumb();\n                const userData = JSON.parse(sessionStorage.getItem(\"user\"));\n                console.log('User data from session:', userData);\n                this.setUser(userData);\n\n                // 如果是维修员或管理员，获取未读通知数量\n                if (this.user.role && (this.user.role.id === 13 || this.user.role.id === 14)) {\n                    this.getUnreadNotificationCount();\n\n                    // 每分钟获取一次未读通知数量\n                    setInterval(() => {\n                        this.getUnreadNotificationCount();\n                    }, 60000);\n                }\n                //\n                // 检查用户是否是首次登录（通过检查state字段）\n                // state为0表示未完善信息，state为1表示已完善信息\n                if (this.user.state === 0) {\n                    this.$get(\"/role/\" + this.user.role.id)\n                    .then((res) => {\n                        console.log(res.data)\n                        this.role = res.data.role;\n                        this.drawer = true\n                        this.$msg(\"首次登录，请完善信息\", \"warning\")\n                    })\n                }\n            }else {\n                this.$msg(\"您向未登陆,没有权限\",\"error\")\n                this.$router.push(\"/\")\n            }\n\n        },\n        mounted() {\n            // 获取当前屏幕宽度\n            window.onresize = () => {\n                this.windowWidth = document.documentElement.clientWidth\n            }\n            // 文字頭像\n            this.textAvatar(this.user.username);\n        }\n    }\n</script>\n\n<style scoped lang=\"less\">\n    .main {\n        display: flex;\n        height: 90%;\n\n        .left {\n            position: fixed;\n            height: 100%;\n\n            .logo {\n                width: 90%;\n                /*color: white;*/\n                font-size: 16px;\n                text-align: center;\n                padding: 8px 0;\n                /*border: 1px solid white;*/\n                margin: 9.1px auto 0 auto;\n            }\n        }\n\n        .right {\n            transition: all 0.3s ease 0s;\n            position: relative;\n\n            .top {\n                transition: all 0.3s ease 0s;\n                position: fixed;\n                /*color: #fff;*/\n                display: flex;\n                align-items: center;\n                justify-content: space-between;\n                z-index: 9;\n\n                .icon {\n                    font-size: 20px;\n                    cursor: pointer;\n                    margin-left: 10px;\n                }\n            }\n\n            .bottom {\n                width: 100%;\n                height: 100%;\n                /*background: #fff;*/\n                margin-top: 65px;\n                .bottom_top {\n                    padding: 20px;\n                }\n            }\n        }\n\n        .ruleform /deep/ .el-input {\n            width: 80% !important;\n        }\n\n        /deep/ .el-cascader {\n            width: 100% !important;\n        }\n    }\n</style>\n"], "sourceRoot": "src/views/user"}]}