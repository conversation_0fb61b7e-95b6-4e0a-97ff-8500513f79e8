{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\Task.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\Task.vue", "mtime": 1748677040096}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Task.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiIA,SAAA,UAAA,IAAA,WAAA,QAAA,aAAA;AACA,eAAA;AACA,EAAA,IAAA,EAAA,MADA;AAEA,EAAA,OAAA,EAAA;AACA,IAAA,OADA,qBACA;AAAA;;AACA,UAAA,KAAA,MAAA,IAAA,QAAA,EAAA;AACA,aAAA,OAAA;AACA,OAFA,MAEA;AACA,aAAA,KAAA,CAAA,4BAAA,EAAA;AAAA,gBAAA,KAAA;AAAA,SAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,UAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,SAHA;AAIA;AACA,KAVA;AAWA,IAAA,WAXA,yBAWA;AACA,UAAA,KAAA,KAAA,IAAA,KAAA,EAAA,CAEA;AACA,KAfA;AAiBA,IAAA,YAjBA,wBAiBA,KAjBA,EAiBA;AACA,UAAA,KAAA,GAAA;AACA,WAAA,MADA;AACA;AACA,WAAA,SAFA;AAEA;AACA,WAAA,SAHA,CAGA;;AAHA,OAAA;AAKA,aAAA,KAAA,CAAA,KAAA,CAAA,IAAA,MAAA;AACA,KAxBA;AAyBA,IAAA,YAzBA,wBAyBA,KAzBA,EAyBA;AACA,UAAA,KAAA,GAAA;AACA,WAAA,KADA;AAEA,WAAA,KAFA;AAGA,WAAA;AAHA,OAAA;AAKA,aAAA,KAAA,CAAA,KAAA,CAAA,IAAA,IAAA;AACA,KAhCA;AAiCA,IAAA,SAjCA,qBAiCA,IAjCA,EAiCA;AACA,UAAA,IAAA,GAAA,IAAA,IAAA,CAAA,IAAA,CAAA;AACA,aAAA,WAAA,CAAA,IAAA,EAAA,kBAAA,CAAA;AACA,KApCA;AAqCA,IAAA,YArCA,wBAqCA,GArCA,EAqCA;AAAA;;AACA,WAAA,QAAA,CAAA,UAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,YAAA;AACA,QAAA,MAAA,CAAA,IAAA,iBAAA,GAAA,CAAA,EAAA,GAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,YAAA,MAAA,CAAA,OAAA;AACA,WAHA,MAGA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,IAAA,MAAA;AACA;AACA,SAPA;AAQA,OAbA,EAaA,KAbA,CAaA,YAAA,CAAA,CAbA;AAcA,KApDA;AAqDA,IAAA,eArDA,2BAqDA,GArDA,EAqDA;AAAA;;AACA,UAAA,MAAA,GAAA,GAAA,CAAA,KAAA,GAAA,WAAA,GAAA,KAAA;AACA,UAAA,OAAA,GAAA,GAAA,CAAA,KAAA,GAAA,MAAA,GAAA,IAAA;AAEA,WAAA,IAAA,iBAAA,MAAA,cAAA,GAAA,CAAA,EAAA,GAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,OAAA,WAAA,OAAA;;AACA,UAAA,GAAA,CAAA,KAAA,GAAA,CAAA,GAAA,CAAA,KAAA;;AACA,UAAA,MAAA,CAAA,OAAA;AACA,SAJA,MAIA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,cAAA,OAAA,iBAAA;AACA;AACA,OARA,EAQA,KARA,CAQA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,WAAA,OAAA,oBAAA,GAAA;;AACA,QAAA,MAAA,CAAA,QAAA,CAAA,KAAA,WAAA,OAAA;AACA,OAXA;AAYA,KArEA;AAsEA,IAAA,OAtEA,qBAsEA;AAAA;;AACA,WAAA,IAAA,CAAA,OAAA,EACA,IADA,CACA,UAAA,EAAA,EAAA;AACA,YAAA,EAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,KAAA,GAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,CAAA,UAAA,CAAA,EAAA,CAAA,EAAA;AACA,gBAAA,CAAA,CAAA,KAAA,IAAA,CAAA,CAAA,CAAA,KAAA,EAAA,OAAA,CAAA,CAAA;AACA,gBAAA,CAAA,CAAA,CAAA,KAAA,IAAA,CAAA,CAAA,KAAA,EAAA,OAAA,CAAA;AACA,mBAAA,IAAA,IAAA,CAAA,CAAA,CAAA,UAAA,IAAA,IAAA,IAAA,CAAA,CAAA,CAAA,UAAA,CAAA;AACA,WAJA,CAAA;AAKA,SANA,MAMA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,EAAA,CAAA,IAAA,CAAA,GAAA,IAAA,UAAA;AACA;AACA,OAXA,EAYA,KAZA,CAYA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,WAAA,EAAA,GAAA;;AACA,QAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,gBAAA;AACA,OAfA;AAgBA;AAvFA,GAFA;AA2FA,EAAA,IA3FA,kBA2FA;AACA,WAAA;AACA;AACA,MAAA,KAAA,EAAA,EAFA;AAGA,MAAA,KAAA,EAAA,EAHA;AAIA,MAAA,QAAA,EAAA;AACA,QAAA,EAAA,EAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAJA;AAQA,MAAA,OAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,CARA;AAaA,MAAA,KAAA,EAAA,EAbA;AAcA,MAAA,MAAA,EAAA;AAdA,KAAA;AAgBA,GA5GA;AA6GA,EAAA,OA7GA,qBA6GA;AAAA;;AACA,SAAA,OAAA;AACA,SAAA,IAAA,CAAA,MAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,MAAA,MAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA;AACA,KAHA;AAIA,GAnHA;AAqHA,EAAA,OAAA,EAAA;AACA,IAAA,UADA,sBACA,IADA,EACA;AACA,UAAA,IAAA,GAAA,IAAA,IAAA,CAAA,IAAA,CAAA;AACA,aAAA,WAAA,CAAA,IAAA,EAAA,kBAAA,CAAA;AACA;AAJA;AArHA,CAAA", "sourcesContent": ["<template>\n    <div class=\"content\">\n<!--        搜索栏-->\n        <!-- <div class=\"center\">\n            <el-input placeholder=\"请输入内容\" v-model=\"input\" class=\"input-with-select\" @keydown.enter.native=\"clickSearch\">\n                <el-select v-model=\"select\" slot=\"prepend\" placeholder=\"请选择\" value=\"1\" @change=\"inquiry\">\n                    <el-option value=\"entire\" label=\"全部\"></el-option>\n                    <el-option :value=\"item.id\" v-for=\"item in roles\" :label=\"item.name\"></el-option>\n                </el-select>\n                <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"clickSearch\"></el-button>\n            </el-input>\n        </div> -->\n        <div class=\"bottom\">\n            <el-table\n                    :data=\"tasks\"\n                    :resizable=\"false\"\n                    style=\"width: 100%  \">\n                <el-table-column\n                        prop=\"publish.username\"\n                        label=\"发布人\"\n                        min-width=\"140\">\n                </el-table-column>\n                <el-table-column\n                        label=\"维修员\"\n                        min-width=\"140\">\n                    <template slot-scope=\"scope\">\n                        {{scope.row.accept ? scope.row.accept.username : '暂无服务'}}\n                    </template>\n                </el-table-column>\n                <el-table-column\n                        prop=\"reward\"\n                        label=\"任务金额\"\n                        min-width=\"110\">\n                </el-table-column>\n                <el-table-column\n                        label=\"任务所在类别\"\n                        min-width=\"120\">\n                    <template slot-scope=\"scope\">\n                        {{scope.row.dept ? scope.row.dept.name : '未分类'}}\n                    </template>\n                </el-table-column>\n                <el-table-column\n                        label=\"任务子类别\"\n                        min-width=\"120\">\n                    <template slot-scope=\"scope\">\n                        {{scope.row.type ? scope.row.type.name : '未分类'}}\n                    </template>\n                </el-table-column>\n                <el-table-column\n                        label=\"地址\"\n                        min-width=\"180\">\n                    <template slot-scope=\"scope\">\n                        <span v-if=\"scope.row.province\">\n                            {{scope.row.province}} {{scope.row.city}} {{scope.row.district}}\n                        </span>\n                        <span v-else>未设置</span>\n                    </template>\n                </el-table-column>\n                <el-table-column\n                        label=\"详细地址\"\n                        min-width=\"180\">\n                    <template slot-scope=\"scope\">\n                        {{scope.row.address || '未设置'}}\n                    </template>\n                </el-table-column>\n                <el-table-column\n                        prop=\"taskTitle\"\n                        label=\"标题\"\n                        min-width=\"150\">\n                </el-table-column>\n                <el-table-column\n                        label=\"发布时间\"\n                        min-width=\"140\">\n                    <template slot-scope=\"scope\">\n                        {{scope.row.createTime ? transform(scope.row.createTime) : '暂无时间'}}\n                    </template>\n                </el-table-column>\n                <el-table-column\n                        label=\"服务时间\"\n                        min-width=\"140\">\n                    <template slot-scope=\"scope\">\n                        {{scope.row.orderTime ? transform(scope.row.orderTime) : '暂无时间'}}\n                    </template>\n                </el-table-column>\n                <el-table-column\n                        prop=\"balance\"\n                        label=\"完成时间\"\n                        min-width=\"140\">\n                    <template slot-scope=\"scope\">\n                        {{scope.row.endTime ? transform(scope.row.endTime) : '暂无时间'}}\n                    </template>\n                </el-table-column>\n                <el-table-column\n                        prop=\"state\"\n                        label=\"任务状态\"\n                        min-width=\"90\">\n                    <template slot-scope=\"scope\">\n                        <el-tag :type=\"getStateType(scope.row.state)\">\n                            {{ getStateText(scope.row.state) }}\n                        </el-tag>\n                    </template>\n                </el-table-column>\n                <el-table-column\n                        label=\"操作\"\n                        width=\"180\">\n                    <template slot-scope=\"scope\">\n                        <el-button-group>\n                            <!-- <el-button\n                                    size=\"mini\"\n                                    :type=\"scope.row.isTop ? 'warning' : 'success'\"\n                                    @click=\"handleTopToggle(scope.row)\">\n                                {{ scope.row.isTop ? '取消置顶' : '置顶' }}\n                            </el-button> -->\n                            <el-button\n                                    size=\"mini\"\n                                    type=\"danger\"\n                                    @click=\"handleDelete(scope.row)\">\n                                删除\n                            </el-button>\n                        </el-button-group>\n                    </template>\n                </el-table-column>\n            </el-table>\n\n        </div>\n    </div>\n</template>\n\n<script>\n    import {formatDate} from '@/util/date';\n    export default {\n        name: \"Task\",\n        methods: {\n            inquiry(){\n                if (this.select == 'entire'){\n                    this.newList();\n                }else {\n                    this.$post(\"/task/api/findTaskByRoleId\",{\"id\":this.select})\n                    .then(res => {\n                        this.tasks = res.data.tasks\n                    })\n                }\n            },\n            clickSearch() {\n                if(this.input == '已完成'){\n\n                }\n            },\n\n            getStateType(state) {\n                const types = {\n                    0: 'info',    // 待接单\n                    1: 'warning', // 进行中\n                    2: 'success'  // 已完成\n                }\n                return types[state] || 'info'\n            },\n            getStateText(state) {\n                const texts = {\n                    0: '待接单',\n                    1: '进行中',\n                    2: '已完成'\n                }\n                return texts[state] || '未知'\n            },\n            transform(time) {\n                let date = new Date(time);\n                return formatDate(date, 'yyyy-MM-dd hh:mm');\n            },\n            handleDelete(row) {\n                this.$confirm('确认删除该任务?', '提示', {\n                    confirmButtonText: '确定',\n                    cancelButtonText: '取消',\n                    type: 'warning'\n                }).then(() => {\n                    this.$del(`/task/${row.id}`).then(res => {\n                        if (res.data.status) {\n                            this.$message.success('删除成功');\n                            this.newList();\n                        } else {\n                            this.$message.error(res.data.msg || '删除失败');\n                        }\n                    })\n                }).catch(() => {})\n            },\n            handleTopToggle(row) {\n                const action = row.isTop ? 'cancelTop' : 'top';\n                const message = row.isTop ? '取消置顶' : '置顶';\n\n                this.$put(`/task/${action}/${row.id}`).then(res => {\n                    if (res.data.status) {\n                        this.$message.success(`${message}成功`);\n                        row.isTop = !row.isTop;\n                        this.newList();\n                    } else {\n                        this.$message.error(res.data.msg || `${message}失败`);\n                    }\n                }).catch(err => {\n                    console.error(`${message}失败:`, err);\n                    this.$message.error(`${message}失败，请稍后重试`);\n                });\n            },\n            newList() {\n                this.$get(\"/task\")\n                .then((rs) => {\n                    if (rs.data.status) {\n                        this.tasks = rs.data.task.sort((a, b) => {\n                            if (a.isTop && !b.isTop) return -1;\n                            if (!a.isTop && b.isTop) return 1;\n                            return new Date(b.createTime) - new Date(a.createTime);\n                        });\n                    } else {\n                        this.$message.error(rs.data.msg || '获取任务列表失败');\n                    }\n                })\n                .catch(err => {\n                    console.error('获取任务列表失败:', err);\n                    this.$message.error('获取任务列表失败，请稍后重试');\n                });\n            }\n        },\n        data() {\n            return {\n                //学校\n                roles:[],\n                tasks: [],\n                ruleForm: {\n                    id: 0,\n                    state: true\n                },\n                options: [\n                    {label: \"正常\", value: \"0\"},\n                    {label: \"禁用\", value: \"1\"}\n\n                ],\n                input:\"\",\n                select: 'entire'\n            }\n        },\n        created() {\n            this.newList();\n            this.$get(\"role\")\n            .then(res => {\n                this.roles = res.data.role\n            })\n        },\n\n        filters: {\n            formatDate(time) {\n                let date = new Date(time);\n                return formatDate(date, 'yyyy-MM-dd hh:mm');\n            }\n        }\n\n    }\n</script>\n\n<style scoped lang=\"less\">\n    .content {\n        padding: 0 1%;\n\n    }\n\n    .center {\n        width: 80%;\n        margin-bottom: 30px;\n    }\n\n    /deep/ .el-select .el-input {\n        width: 200px;\n    }\n\n    /deep/ .input-with-select .el-input-group__prepend {\n        background-color: #fff;\n    }\n\n\n\n    .form {\n        margin: 0 22px;\n    }\n\n    .el-button-group {\n        .el-button {\n            margin-left: 0;\n            margin-right: 0;\n\n            &:first-child {\n                border-right: 1px solid rgba(255, 255, 255, 0.5);\n            }\n        }\n    }\n</style>"], "sourceRoot": "src/views/admin/children"}]}