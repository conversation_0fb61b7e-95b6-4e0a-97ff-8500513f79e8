{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\remark\\remark.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\remark\\remark.vue", "mtime": 1748677053866}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RSZW1hcmssIGdldFJlbWFyaywgZGVsUmVtYXJrLCBhZGRSZW1hcmssIHVwZGF0ZVJlbWFyayB9IGZyb20gIkAvYXBpL3JlbWFyay9yZW1hcmsiOwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJSZW1hcmsiLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAgIGljb25DbGFzc2VzOiBbJ2ljb24tcmF0ZS1mYWNlLTEnLCAnaWNvbi1yYXRlLWZhY2UtMicsICdpY29uLXJhdGUtZmFjZS0zJ10sCiAgICAgICAgLy8g6YGu572p5bGCCiAgICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgICAvLyDpgInkuK3mlbDnu4QKICAgICAgICBpZHM6IFtdLAogICAgICAgIC8vIOmdnuWNleS4quemgeeUqAogICAgICAgIHNpbmdsZTogdHJ1ZSwKICAgICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgKICAgICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YKICAgICAgICBzaG93U2VhcmNoOiB0cnVlLAogICAgICAgIC8vIOaAu+adoeaVsAogICAgICAgIHRvdGFsOiAwLAogICAgICAgIC8vIHJlbWFya+ihqOagvOaVsOaNrgogICAgICAgIHJlbWFya0xpc3Q6IFtdLAogICAgICAgIC8vIOW8ueWHuuWxguagh+mimAogICAgICAgIHRpdGxlOiAiIiwKICAgICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYIKICAgICAgICBvcGVuOiBmYWxzZSwKICAgICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgICBxdWVyeVBhcmFtczogewogICAgICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgICAgIHN0YXI6IG51bGwsCiAgICAgICAgICAgIHRhc2tJZDogbnVsbCwKICAgICAgICAgICAgdGFza05hbWU6IG51bGwsCiAgICAgICAgICAgIGFjY2VwdElkOiBudWxsLAogICAgICAgICAgICBhY2NlcHROYW1lOiBudWxsLAogICAgICAgICAgICBwdWJsaXNoSWQ6IG51bGwsCiAgICAgICAgICAgIHB1Ymxpc2hOYW1lOiBudWxsCiAgICAgICAgfSwKICAgICAgICAvLyDooajljZXlj4LmlbAKICAgICAgICBmb3JtOiB7fSwKICAgICAgICAvLyDooajljZXmoKHpqowKICAgICAgICBydWxlczogewogICAgICAgIH0sCiAgICAgICAgLy8g5Lu75Yqh6YCJ6aG5CiAgICAgICAgdGFza09wdGlvbnM6IFtdLAogICAgICAgIC8vIOeUqOaIt+mAiemhuQogICAgICAgIHVzZXJPcHRpb25zOiBbXQogICAgfTsKICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKTsKICAgIHRoaXMuZ2V0VGFza09wdGlvbnMoKTsKICAgIHRoaXMuZ2V0VXNlck9wdGlvbnMoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKiDmn6Xor6JyZW1hcmvliJfooaggKi8KICAgIGdldExpc3QoKSB7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIGxpc3RSZW1hcmsodGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXMgPT4gewogICAgICAgIC8vIGNvbnNvbGUubG9nKHJlcy5kYXRhLnJvd3MpOwogICAgICAgIHRoaXMucmVtYXJrTGlzdCA9IHJlcy5kYXRhLnJvd3M7CiAgICAgICAgdGhpcy50b3RhbCA9IHJlcy50b3RhbDsKICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g5Y+W5raI5oyJ6ZKuCiAgICBjYW5jZWwoKSB7CiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICB0aGlzLnJlc2V0KCk7CiAgICB9LAogICAgLy8g6KGo5Y2V6YeN572uCiAgICByZXNldCgpIHsKICAgICAgdGhpcy5mb3JtID0gewogICAgICAgIGlkOiBudWxsLAogICAgICAgIHN0YXI6IG51bGwsCiAgICAgICAgcmVtYXJrOiBudWxsLAogICAgICAgIHRhc2tJZDogbnVsbCwKICAgICAgICBhY2NlcHRJZDogbnVsbCwKICAgICAgICBwdWJsaXNoSWQ6IG51bGwKICAgICAgfTsKICAgIC8vICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsKICAgIH0sCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVRdWVyeSgpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLwogICAgcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOwogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7CiAgICB9LAogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0uaWQpCiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCE9PTEKICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoCiAgICB9LAogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlQWRkKCkgewogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIHRoaXMub3BlbiA9IHRydWU7CiAgICAgIHRoaXMudGl0bGUgPSAi5re75YqgcmVtYXJrIjsKICAgIH0sCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVVcGRhdGUocm93KSB7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgY29uc3QgaWQgPSByb3cuaWQgfHwgdGhpcy5pZHMKICAgICAgZ2V0UmVtYXJrKGlkKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhLmRhdGE7CiAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsKICAgICAgICB0aGlzLnRpdGxlID0gIuS/ruaUuXJlbWFyayI7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmj5DkuqTmjInpkq4gKi8KICAgIHN1Ym1pdEZvcm0oKSB7CiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBpZiAodGhpcy5mb3JtLmlkICE9IG51bGwpIHsKICAgICAgICAgICAgdXBkYXRlUmVtYXJrKHRoaXMuZm9ybSkudGhlbigoKSA9PiB7CiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSgi5L+u5pS55oiQ5YqfIik7CiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgYWRkUmVtYXJrKHRoaXMuZm9ybSkudGhlbigoKSA9PiB7CiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSgi5paw5aKe5oiQ5YqfIik7CiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlRGVsZXRlKHJvdykgewogICAgICBjb25zdCBpZHMgPSByb3cuaWQgfHwgdGhpcy5pZHM7CiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpOatpOWtpueUnycsICfmj5DnpLonLCB7CiAgICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJywKICAgICAgICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgICAgIGRlbFJlbWFyayhpZHMpLnRoZW4oKCkgPT4gewogICAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLAogICAgICAgICAgICAgICAgbWVzc2FnZTogJ+WIoOmZpOaIkOWKnyEnCiAgICAgICAgICAgICAgICB9KTsKCiAgICAgICAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgICB0eXBlOiAnaW5mbycsCiAgICAgICAgICAgICAgICBtZXNzYWdlOiAn5bey5Y+W5raI5Yig6ZmkJwogICAgICAgICAgICB9KTsKICAgICAgICAgICAgfSk7CiAgICB9LAogICAgLy/lrpjnvZHmj5DkvpvnmoTmlrnms5XvvIzlroPpu5jorqTmnInkuKrlj4LmlbB2YWx1Ze+8jOaJgOiOt+WPluWIsOeahOWwseaYr+aUueWPmOeahOWAvO+8jOeUqOWug+i/m+ihjOWunumZhemcgOaxguaTjeS9nAogICAgY2hhbmdlVmFsdWUodmFsdWUpewogICAgICBjb25zb2xlLmxvZyh2YWx1ZSk7CiAgICB9LAoKICAgIC8qKiDojrflj5bku7vliqHpgInpobkgKi8KICAgIGdldFRhc2tPcHRpb25zKCkgewogICAgICB0aGlzLiRnZXQoIi90YXNrIikudGhlbihyZXMgPT4gewogICAgICAgIGlmIChyZXMuZGF0YS5zdGF0dXMpIHsKICAgICAgICAgIHRoaXMudGFza09wdGlvbnMgPSByZXMuZGF0YS50YXNrIHx8IFtdOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAoKICAgIC8qKiDojrflj5bnlKjmiLfpgInpobkgKi8KICAgIGdldFVzZXJPcHRpb25zKCkgewogICAgICB0aGlzLiRnZXQoIi91c2VyIikudGhlbihyZXMgPT4gewogICAgICAgIGlmIChyZXMuZGF0YS5zdGF0dXMpIHsKICAgICAgICAgIHRoaXMudXNlck9wdGlvbnMgPSByZXMuZGF0YS51c2VyIHx8IFtdOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9CiAgfQp9Owo="}, {"version": 3, "sources": ["remark.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgMA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "remark.vue", "sourceRoot": "src/views/remark", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"星级\" prop=\"star\">\n        <el-input\n          v-model=\"queryParams.star\"\n          placeholder=\"请输入星级\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"任务名称\" prop=\"taskName\">\n        <el-input\n          v-model=\"queryParams.taskName\"\n          placeholder=\"请输入任务名称\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"接收人\" prop=\"acceptName\">\n        <el-input\n          v-model=\"queryParams.acceptName\"\n          placeholder=\"请输入接收人姓名\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"评价人\" prop=\"publishName\">\n        <el-input\n          v-model=\"queryParams.publishName\"\n          placeholder=\"请输入评价人姓名\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n<!--      <el-col :span=\"1.5\">-->\n<!--        <el-button-->\n<!--          type=\"primary\"-->\n<!--          plain-->\n<!--          icon=\"el-icon-plus\"-->\n<!--          size=\"mini\"-->\n<!--          @click=\"handleAdd\"-->\n<!--        >新增</el-button>-->\n<!--      </el-col>-->\n<!--      <el-col :span=\"1.5\">-->\n<!--        <el-button-->\n<!--          type=\"success\"-->\n<!--          plain-->\n<!--          icon=\"el-icon-edit\"-->\n<!--          size=\"mini\"-->\n<!--          :disabled=\"single\"-->\n<!--          @click=\"handleUpdate\"-->\n<!--        >修改</el-button>-->\n<!--      </el-col>-->\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n        >删除</el-button>\n      </el-col>\n      <!-- <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar> -->\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"remarkList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"评分\" min-width=\"100\" align=\"center\" prop=\"star\">\n        <template slot-scope=\"scope\">\n          <el-rate\n            v-model=\"scope.row.star\"\n            show-text>\n          </el-rate>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"评价内容\" align=\"center\" prop=\"remark\" />\n      <el-table-column label=\"任务名称\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.task ? scope.row.task.taskTitle : '未知任务' }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"任务类别\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.task && scope.row.task.dept ? scope.row.task.dept.name : '未分类' }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"任务子类别\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.task && scope.row.task.type ? scope.row.task.type.name : '未分类' }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"维修员\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.accept ? scope.row.accept.username : '未知用户' }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"评价人\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.publish ? scope.row.publish.username : '未知用户' }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n<!--          <el-button-->\n<!--            size=\"mini\"-->\n<!--            type=\"text\"-->\n<!--            icon=\"el-icon-edit\"-->\n<!--            @click=\"handleUpdate(scope.row)\"-->\n<!--          >修改</el-button>-->\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    /> -->\n\n    <!-- 添加或修改remark对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n\n        <el-form-item label=\"星级\" prop=\"star\">\n            <el-rate\n                v-model=\"form.star\"\n                show-text\n                @change=\"changeValue\">\n            </el-rate>\n        </el-form-item>\n\n        <el-form-item label=\"评价内容\" prop=\"remark\">\n          <el-input v-model=\"form.remark\" placeholder=\"请输入评价内容\" />\n        </el-form-item>\n        <el-form-item label=\"任务\" prop=\"taskId\">\n          <el-select v-model=\"form.taskId\" placeholder=\"请选择任务\" filterable>\n            <el-option\n              v-for=\"task in taskOptions\"\n              :key=\"task.id\"\n              :label=\"task.taskTitle\"\n              :value=\"task.id\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"接收人\" prop=\"acceptId\">\n          <el-select v-model=\"form.acceptId\" placeholder=\"请选择接收人\" filterable>\n            <el-option\n              v-for=\"user in userOptions\"\n              :key=\"user.id\"\n              :label=\"user.username\"\n              :value=\"user.id\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"评价人\" prop=\"publishId\">\n          <el-select v-model=\"form.publishId\" placeholder=\"请选择评价人\" filterable>\n            <el-option\n              v-for=\"user in userOptions\"\n              :key=\"user.id\"\n              :label=\"user.username\"\n              :value=\"user.id\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listRemark, getRemark, delRemark, addRemark, updateRemark } from \"@/api/remark/remark\";\n\nexport default {\n  name: \"Remark\",\n  data() {\n    return {\n        iconClasses: ['icon-rate-face-1', 'icon-rate-face-2', 'icon-rate-face-3'],\n        // 遮罩层\n        loading: true,\n        // 选中数组\n        ids: [],\n        // 非单个禁用\n        single: true,\n        // 非多个禁用\n        multiple: true,\n        // 显示搜索条件\n        showSearch: true,\n        // 总条数\n        total: 0,\n        // remark表格数据\n        remarkList: [],\n        // 弹出层标题\n        title: \"\",\n        // 是否显示弹出层\n        open: false,\n        // 查询参数\n        queryParams: {\n            pageNum: 1,\n            pageSize: 10,\n            star: null,\n            taskId: null,\n            taskName: null,\n            acceptId: null,\n            acceptName: null,\n            publishId: null,\n            publishName: null\n        },\n        // 表单参数\n        form: {},\n        // 表单校验\n        rules: {\n        },\n        // 任务选项\n        taskOptions: [],\n        // 用户选项\n        userOptions: []\n    };\n  },\n  created() {\n    this.getList();\n    this.getTaskOptions();\n    this.getUserOptions();\n  },\n  methods: {\n    /** 查询remark列表 */\n    getList() {\n      this.loading = true;\n      listRemark(this.queryParams).then(res => {\n        // console.log(res.data.rows);\n        this.remarkList = res.data.rows;\n        this.total = res.total;\n        this.loading = false;\n      });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        id: null,\n        star: null,\n        remark: null,\n        taskId: null,\n        acceptId: null,\n        publishId: null\n      };\n    //   this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加remark\";\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const id = row.id || this.ids\n      getRemark(id).then(response => {\n        this.form = response.data.data;\n        this.open = true;\n        this.title = \"修改remark\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.id != null) {\n            updateRemark(this.form).then(() => {\n              this.$message(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addRemark(this.form).then(() => {\n              this.$message(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const ids = row.id || this.ids;\n      this.$confirm('是否确认删除此学生', '提示', {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning',\n            }).then(() => {\n            delRemark(ids).then(() => {\n                this.open = false;\n                this.getList();\n            });\n            this.$message({\n                type: 'success',\n                message: '删除成功!'\n                });\n\n            }).catch(() => {\n            this.$message({\n                type: 'info',\n                message: '已取消删除'\n            });\n            });\n    },\n    //官网提供的方法，它默认有个参数value，所获取到的就是改变的值，用它进行实际需求操作\n    changeValue(value){\n      console.log(value);\n    },\n\n    /** 获取任务选项 */\n    getTaskOptions() {\n      this.$get(\"/task\").then(res => {\n        if (res.data.status) {\n          this.taskOptions = res.data.task || [];\n        }\n      });\n    },\n\n    /** 获取用户选项 */\n    getUserOptions() {\n      this.$get(\"/user\").then(res => {\n        if (res.data.status) {\n          this.userOptions = res.data.user || [];\n        }\n      });\n    }\n  }\n};\n</script>\n"]}]}