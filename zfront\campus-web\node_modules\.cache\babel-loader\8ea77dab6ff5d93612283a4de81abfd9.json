{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\Role.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\Role.vue", "mtime": 1748715510969}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Role.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+HA,eAAA;AACA,EAAA,IAAA,EAAA,MADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA,MAAA,KAAA,EAAA,EADA;AAEA,MAAA,WAAA,EAAA,EAFA;AAEA;AAEA;AACA,MAAA,qBAAA,EAAA,KALA;AAMA,MAAA,mBAAA,EAAA,EANA;AAOA,MAAA,cAAA,EAAA,KAPA;AAOA;AAEA;AACA,MAAA,YAAA,EAAA;AACA,QAAA,EAAA,EAAA,IADA;AAEA,QAAA,IAAA,EAAA,EAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OAVA;AAgBA;AACA,MAAA,aAAA,EAAA;AACA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,WAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,MAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,OAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AAJA,OAjBA;AA0BA;AACA,MAAA,aAAA,EAAA,KA3BA;AA4BA,MAAA,WAAA,EAAA,EA5BA;AA6BA,MAAA,MAAA,EAAA,KA7BA;AA6BA;AAEA;AACA,MAAA,IAAA,EAAA;AACA,QAAA,EAAA,EAAA,IADA;AAEA,QAAA,IAAA,EAAA,EAFA;AAGA,QAAA,MAAA,EAAA,IAHA;AAIA,QAAA,MAAA,EAAA;AAJA,OAhCA;AAuCA;AACA,MAAA,cAAA,EAAA,EAxCA;AAyCA,MAAA,kBAAA,EAAA,EAzCA;AA0CA,MAAA,gBAAA,EAAA,IA1CA;AA2CA,MAAA,YAAA,EAAA,IA3CA;AA6CA;AACA,MAAA,KAAA,EAAA;AACA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,YAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AADA;AA9CA,KAAA;AAoDA,GAvDA;AAwDA,EAAA,OAAA,EAAA;AACA,IAAA,KADA,mBACA;AACA,eAAA,EAAA,GAAA;AACA,eAAA,CAAA,CAAA,IAAA,IAAA,CAAA,MAAA,EAAA,IAAA,OAAA,GAAA,CAAA,EAAA,QAAA,CAAA,EAAA,EAAA,SAAA,CAAA,CAAA,CAAA;AACA;;AACA,aAAA,EAAA,KAAA,EAAA,EAAA,GAAA,GAAA,GAAA,EAAA,EAAA,GAAA,GAAA,GAAA,EAAA,EAAA,GAAA,GAAA,GAAA,EAAA,EAAA,GAAA,GAAA,GAAA,EAAA,EAAA,GAAA,EAAA,EAAA,GAAA,EAAA,EAAA;AACA,KANA;AAQA;AAEA;AACA,IAAA,iBAXA,+BAWA;AACA,WAAA,iBAAA;AACA,WAAA,cAAA,GAAA,KAAA;AACA,WAAA,mBAAA,GAAA,QAAA;AACA,WAAA,qBAAA,GAAA,IAAA;AACA,KAhBA;AAkBA;AACA,IAAA,oBAnBA,gCAmBA,WAnBA,EAmBA;AACA,WAAA,SAAA;AACA,WAAA,QAAA,GAAA,aAAA;AACA,WAAA,MAAA,GAAA,KAAA;AACA,WAAA,WAAA,GAAA,SAAA,CAJA,CAMA;;AACA,WAAA,gBAAA,GAAA,WAAA,CAAA,OAAA;AACA,WAAA,kBAAA,GAAA,WAAA,CAAA,QAAA;AACA,WAAA,YAAA,GAAA,WAAA,CAAA,MAAA;AACA,WAAA,cAAA,GAAA,WAAA,CAAA,QAAA;AAEA,WAAA,aAAA,GAAA,IAAA;AACA,KAhCA;AAkCA;AACA,IAAA,oBAnCA,gCAmCA,WAnCA,EAmCA;AACA,WAAA,cAAA,GAAA,IAAA;AACA,WAAA,mBAAA,GAAA,QAAA;AAEA,WAAA,YAAA,GAAA;AACA,QAAA,EAAA,EAAA,WAAA,CAAA,OADA;AAEA,QAAA,IAAA,EAAA,WAAA,CAAA,QAFA;AAGA,QAAA,MAAA,EAAA,WAAA,CAAA;AAHA,OAAA;AAMA,WAAA,qBAAA,GAAA,IAAA;AACA,KA9CA;AAgDA;AACA,IAAA,uBAjDA,mCAiDA,cAjDA,EAiDA;AACA,WAAA,MAAA,GAAA,IAAA;AACA,WAAA,WAAA,GAAA,SAAA,CAFA,CAIA;;AACA,UAAA,cAAA,GAAA,KAAA,kBAAA,CAAA,cAAA,CAAA,SAAA,CAAA;;AACA,UAAA,cAAA,EAAA;AACA,aAAA,gBAAA,GAAA,cAAA,CAAA,OAAA;AACA,aAAA,kBAAA,GAAA,cAAA,CAAA,QAAA;AACA,aAAA,YAAA,GAAA,cAAA,CAAA,MAAA;AACA,aAAA,cAAA,GAAA,cAAA,CAAA,QAAA;AACA;;AAEA,WAAA,IAAA,GAAA;AACA,QAAA,EAAA,EAAA,cAAA,CAAA,SADA;AAEA,QAAA,IAAA,EAAA,cAAA,CAAA;AAFA,OAAA;AAKA,WAAA,aAAA,GAAA,IAAA;AACA,KApEA;AAsEA;AACA,IAAA,oBAvEA,gCAuEA,WAvEA,EAuEA;AAAA;;AACA,WAAA,QAAA,CAAA,gCAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,YAAA;AACA,QAAA,KAAA,CAAA,IAAA,CAAA,WAAA,WAAA,CAAA,OAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,UAAA,KAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,UAAA,KAAA,CAAA,OAAA;AACA,SAJA,EAKA,KALA,CAKA,UAAA,KAAA,EAAA;AACA,UAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,UAAA,KAAA,CAAA,OAAA;AACA,SAPA;AAQA,OAbA;AAcA,KAtFA;AAwFA;AACA,IAAA,uBAzFA,mCAyFA,cAzFA,EAyFA;AAAA;;AACA,WAAA,QAAA,CAAA,cAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,YAAA;AACA,QAAA,MAAA,CAAA,IAAA,CAAA,YAAA,cAAA,CAAA,SAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,UAAA,MAAA,CAAA,OAAA;AACA,SAJA,EAKA,KALA,CAKA,UAAA,KAAA,EAAA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,UAAA,KAAA,CAAA,OAAA;AACA,SAPA;AAQA,OAbA;AAcA,KAxGA;AA0GA;AACA,IAAA,kBA3GA,gCA2GA;AAAA;;AACA,WAAA,KAAA,CAAA,cAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,UAAA,MAAA,CAAA,cAAA;AACA;AACA,OAJA;AAKA,KAjHA;AAmHA;AACA,IAAA,UApHA,wBAoHA;AAAA;;AACA,WAAA,KAAA,CAAA,MAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,UAAA,MAAA,CAAA,iBAAA;AACA;AACA,OAJA;AAKA,KA1HA;AA4HA;AACA,IAAA,cA7HA,4BA6HA;AAAA;;AACA,UAAA,KAAA,cAAA,EAAA;AACA;AACA,aAAA,IAAA,CAAA,OAAA,EAAA;AACA,UAAA,EAAA,EAAA,KAAA,YAAA,CAAA,EADA;AAEA,UAAA,IAAA,EAAA,KAAA,YAAA,CAAA,IAFA;AAGA,UAAA,MAAA,EAAA,KAAA,YAAA,CAAA;AAHA,SAAA,EAIA,IAJA,CAIA,YAAA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA;;AACA,UAAA,MAAA,CAAA,qBAAA,GAAA,KAAA;;AACA,UAAA,MAAA,CAAA,OAAA;AACA,SARA,EAQA,KARA,CAQA,UAAA,KAAA,EAAA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,UAAA,KAAA,CAAA,OAAA;AACA,SAVA;AAWA,OAbA,MAaA;AACA;AACA,aAAA,KAAA,CAAA,OAAA,EAAA;AACA,UAAA,IAAA,EAAA,KAAA,YAAA,CAAA,IADA;AAEA,UAAA,MAAA,EAAA,KAAA,YAAA,CAAA;AAFA,SAAA,EAGA,IAHA,CAGA,YAAA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA;;AACA,UAAA,MAAA,CAAA,qBAAA,GAAA,KAAA;;AACA,UAAA,MAAA,CAAA,OAAA;AACA,SAPA,EAOA,KAPA,CAOA,UAAA,KAAA,EAAA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,UAAA,KAAA,CAAA,OAAA;AACA,SATA;AAUA;AACA,KAxJA;AA0JA;AACA,IAAA,iBA3JA,+BA2JA;AAAA;;AACA,UAAA,KAAA,MAAA,EAAA;AACA;AACA,aAAA,IAAA,CAAA,QAAA,EAAA;AACA,UAAA,EAAA,EAAA,KAAA,IAAA,CAAA,EADA;AAEA,UAAA,IAAA,EAAA,KAAA,IAAA,CAAA,IAFA;AAGA,UAAA,MAAA,EAAA,KAAA,YAHA;AAIA,UAAA,MAAA,EAAA,KAAA;AAJA,SAAA,EAKA,IALA,CAKA,YAAA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,WAAA;;AACA,UAAA,MAAA,CAAA,aAAA,GAAA,KAAA;;AACA,UAAA,MAAA,CAAA,OAAA;AACA,SATA,EASA,KATA,CASA,UAAA,KAAA,EAAA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,UAAA,KAAA,CAAA,OAAA;AACA,SAXA;AAYA,OAdA,MAcA;AACA;AACA,aAAA,KAAA,CAAA,QAAA,EAAA;AACA,UAAA,IAAA,EAAA,KAAA,IAAA,CAAA,IADA;AAEA,UAAA,MAAA,EAAA,KAAA,YAFA;AAGA,UAAA,MAAA,EAAA,KAAA;AAHA,SAAA,EAIA,IAJA,CAIA,YAAA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,WAAA;;AACA,UAAA,MAAA,CAAA,aAAA,GAAA,KAAA;;AACA,UAAA,MAAA,CAAA,OAAA;AACA,SARA,EAQA,KARA,CAQA,UAAA,KAAA,EAAA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,UAAA,KAAA,CAAA,OAAA;AACA,SAVA;AAWA;AACA,KAxLA;AA0LA;AACA,IAAA,yBA3LA,uCA2LA;AACA,WAAA,qBAAA,GAAA,KAAA;AACA,WAAA,iBAAA;AACA,KA9LA;AAgMA;AACA,IAAA,iBAjMA,+BAiMA;AACA,WAAA,aAAA,GAAA,KAAA;AACA,WAAA,SAAA;AACA,KApMA;AAsMA;AACA,IAAA,OAvMA,qBAuMA;AAAA;;AACA,WAAA,IAAA,CAAA,OAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,IAAA,GAAA,CAAA,IAAA,CAAA,IAAA,EAAA;AACA,UAAA,MAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CADA,CAEA;;AACA,UAAA,MAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,mBAAA;AACA,cAAA,EAAA,EAAA,IAAA,CAAA,EADA;AAEA,cAAA,IAAA,EAAA,IAAA,CAAA;AAFA,aAAA;AAAA,WAAA,CAAA;AAIA,SAPA,MAOA;AACA,UAAA,MAAA,CAAA,KAAA,GAAA,EAAA;AACA,UAAA,MAAA,CAAA,WAAA,GAAA,EAAA;AACA,UAAA,OAAA,CAAA,IAAA,CAAA,oBAAA;AACA;AACA,OAdA,EAeA,KAfA,CAeA,UAAA,KAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,WAAA,EAAA,KAAA;AACA,QAAA,MAAA,CAAA,KAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,WAAA,GAAA,EAAA;;AACA,QAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,gBAAA;AACA,OApBA;AAqBA,KA7NA;AA+NA;AACA,IAAA,iBAhOA,+BAgOA;AACA,WAAA,YAAA,GAAA;AACA,QAAA,EAAA,EAAA,IADA;AAEA,QAAA,IAAA,EAAA,EAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OAAA;;AAMA,UAAA,KAAA,KAAA,CAAA,YAAA,EAAA;AACA,aAAA,KAAA,CAAA,YAAA,CAAA,WAAA;AACA;AACA,KA1OA;AA4OA;AACA,IAAA,SA7OA,uBA6OA;AACA,WAAA,IAAA,GAAA;AACA,QAAA,EAAA,EAAA,IADA;AAEA,QAAA,IAAA,EAAA,EAFA;AAGA,QAAA,MAAA,EAAA,IAHA;AAIA,QAAA,MAAA,EAAA;AAJA,OAAA;AAMA,WAAA,cAAA,GAAA,EAAA;AACA,WAAA,kBAAA,GAAA,EAAA;AACA,WAAA,gBAAA,GAAA,IAAA;AACA,WAAA,YAAA,GAAA,IAAA;;AAEA,UAAA,KAAA,KAAA,CAAA,IAAA,EAAA;AACA,aAAA,KAAA,CAAA,IAAA,CAAA,WAAA;AACA;AACA,KA5PA;AAgQA;AACA,IAAA,kBAjQA,8BAiQA,OAjQA,EAiQA;AAAA,iDACA,KAAA,QADA;AAAA;;AAAA;AACA,4DAAA;AAAA,cAAA,IAAA;;AACA,cAAA,IAAA,CAAA,QAAA,EAAA;AAAA,wDACA,IAAA,CAAA,QADA;AAAA;;AAAA;AACA,qEAAA;AAAA,oBAAA,KAAA;;AACA,oBAAA,KAAA,CAAA,SAAA,KAAA,OAAA,EAAA;AACA,yBAAA,IAAA;AACA;AACA;AALA;AAAA;AAAA;AAAA;AAAA;AAMA;AACA;AATA;AAAA;AAAA;AAAA;AAAA;;AAUA,aAAA,IAAA;AACA;AA5QA,GAxDA;AAuUA,EAAA,OAvUA,qBAuUA;AACA,SAAA,OAAA;AACA,GAzUA;AA2UA,EAAA,QAAA,EAAA;AACA,IAAA,QADA,sBACA;AAAA;;AACA,UAAA,CAAA,KAAA,KAAA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,KAAA,KAAA,CAAA,EAAA;AACA,eAAA,EAAA;AACA;;AAEA,UAAA,QAAA,GAAA,EAAA,CALA,CAOA;;AACA,WAAA,KAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,CAAA,IAAA,IAAA,CAAA,IAAA,CAAA,KAAA,EAAA;AAEA,QAAA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,CAAA,IAAA,EAAA;AAEA,cAAA,QAAA,GAAA,EAAA;AACA,cAAA,WAAA,GAAA,IAAA,CAAA,SAAA,CAAA,IAAA,EAAA,CAJA,CAMA;;AACA,UAAA,WAAA,CAAA,OAAA,CAAA,UAAA,SAAA,EAAA;AACA,gBAAA,CAAA,SAAA,EAAA;AAEA,YAAA,QAAA,CAAA,IAAA,CAAA;AACA,cAAA,SAAA,EAAA,SAAA,CAAA,EADA;AAEA,cAAA,WAAA,EAAA,SAAA,CAAA,IAAA,IAAA,EAFA;AAGA,cAAA,IAAA,EAAA,MAAA,CAAA,KAAA;AAHA,aAAA;AAKA,WARA,EAPA,CAiBA;;AACA,UAAA,QAAA,CAAA,IAAA,CAAA;AACA,YAAA,OAAA,EAAA,IAAA,CAAA,EADA;AAEA,YAAA,QAAA,EAAA,IAAA,CAAA,IAAA,IAAA,EAFA;AAGA,YAAA,MAAA,EAAA,IAAA,CAAA,EAHA;AAGA;AACA,YAAA,QAAA,EAAA,IAAA,CAAA,IAJA;AAIA;AACA,YAAA,QAAA,EAAA,QALA;AAMA,YAAA,IAAA,EAAA,MAAA,CAAA,KAAA;AANA,WAAA;AAQA,SA1BA;AA2BA,OA9BA;AAgCA,aAAA,QAAA;AACA;AA1CA;AA3UA,CAAA", "sourcesContent": ["<template>\n    <div class=\"content\">\n        <!-- 顶部操作按钮 -->\n        <div class=\"top-actions\" style=\"margin-bottom: 20px;\">\n            <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"handleAddCategory\">添加维修类别</el-button>\n        </div>\n\n        <el-table\n                :data=\"dataList\"\n                style=\"width: 100%;margin-bottom: 20px;\"\n                row-key=\"uuid\"\n                border\n                :default-expand-all = 'false'\n                :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\">\n            <el-table-column\n                    prop=\"deptname\"\n                    label=\"维修类别\"\n                    sortable\n                    width=\"250\">\n            </el-table-column>\n            <el-table-column\n                    prop=\"classesname\"\n                    label=\"维修子类别\">\n            </el-table-column>\n\n            <el-table-column label=\"操作\" width=\"280\">\n                <template slot-scope=\"scope\">\n                    <!-- 维修类别级别：可以增加子类别、修改、删除 -->\n                    <template v-if=\"scope.row.deptsId\">\n                        <el-button\n                                icon=\"el-icon-plus\"\n                                size=\"mini\"\n                                type=\"primary\"\n                                @click=\"handleAddSubcategory(scope.row)\">增加维修子类别\n                        </el-button>\n                        <el-button\n                                icon=\"el-icon-edit\"\n                                size=\"mini\"\n                                @click=\"handleUpdateCategory(scope.row)\">修改\n                        </el-button>\n                        <el-button\n                                icon=\"el-icon-delete\"\n                                size=\"mini\"\n                                type=\"danger\"\n                                @click=\"handleDeleteCategory(scope.row)\">删除\n                        </el-button>\n                    </template>\n\n                    <!-- 维修子类别级别：可以修改、删除 -->\n                    <template v-else-if=\"scope.row.classesId\">\n                        <el-button\n                                icon=\"el-icon-edit\"\n                                size=\"mini\"\n                                @click=\"handleUpdateSubcategory(scope.row)\">修改\n                        </el-button>\n                        <el-button\n                                icon=\"el-icon-delete\"\n                                size=\"mini\"\n                                type=\"danger\"\n                                @click=\"handleDeleteSubcategory(scope.row)\">删除\n                        </el-button>\n                    </template>\n                </template>\n            </el-table-column>\n        </el-table>\n\n        <!-- 添加维修类别弹窗 -->\n        <el-dialog\n            :title=\"categoryDialogTitle\"\n            :visible.sync=\"categoryDialogVisible\"\n            width=\"500px\"\n            :before-close=\"handleCategoryDialogClose\">\n\n            <el-form ref=\"categoryForm\" :model=\"categoryForm\" :rules=\"categoryRules\" label-width=\"120px\">\n                <el-form-item label=\"所属角色\" prop=\"roleId\">\n                    <el-select v-model=\"categoryForm.roleId\" placeholder=\"请选择角色\" style=\"width: 100%;\" :disabled=\"isCategoryEdit\">\n                        <el-option\n                            v-for=\"role in roleOptions\"\n                            :key=\"role.id\"\n                            :label=\"role.name\"\n                            :value=\"role.id\">\n                        </el-option>\n                    </el-select>\n                </el-form-item>\n                <el-form-item label=\"维修类别名称\" prop=\"name\">\n                    <el-input v-model=\"categoryForm.name\" placeholder=\"请输入维修类别名称\" />\n                </el-form-item>\n            </el-form>\n\n            <span slot=\"footer\" class=\"dialog-footer\">\n                <el-button @click=\"handleCategoryDialogClose\">取 消</el-button>\n                <el-button type=\"primary\" @click=\"submitCategoryForm\">确 定</el-button>\n            </span>\n        </el-dialog>\n\n        <!-- 抽屉式表单（仅用于子类别） -->\n        <el-drawer\n            :title=\"drawerTitle\"\n            :visible.sync=\"drawerVisible\"\n            direction=\"rtl\"\n            size=\"400px\"\n            :before-close=\"handleDrawerClose\">\n\n            <div style=\"padding: 20px;\">\n                <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\n                    <!-- 子类别表单 -->\n                    <el-form-item label=\"所属角色\">\n                        <el-input :value=\"parentRoleName\" disabled />\n                    </el-form-item>\n                    <el-form-item label=\"所属维修类别\">\n                        <el-input :value=\"parentCategoryName\" disabled />\n                    </el-form-item>\n                    <el-form-item label=\"维修子类别名称\" prop=\"name\">\n                        <el-input v-model=\"form.name\" placeholder=\"请输入维修子类别名称\" />\n                    </el-form-item>\n                </el-form>\n\n                <div class=\"drawer-footer\">\n                    <el-button @click=\"handleDrawerClose\">取 消</el-button>\n                    <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n                </div>\n            </div>\n        </el-drawer>\n    </div>\n</template>\n\n<script>\n    export default {\n        name: \"Role\",\n        data() {\n            return {\n                roles: [],\n                roleOptions: [], // 角色选项列表\n\n                // 维修类别弹窗相关\n                categoryDialogVisible: false,\n                categoryDialogTitle: '',\n                isCategoryEdit: false, // 是否为编辑模式\n\n                // 维修类别表单参数\n                categoryForm: {\n                    id: null,\n                    name: '',\n                    roleId: null\n                },\n\n                // 维修类别表单校验\n                categoryRules: {\n                    name: [\n                        { required: true, message: '请输入维修类别名称', trigger: 'blur' }\n                    ],\n                    roleId: [\n                        { required: true, message: '请选择角色', trigger: 'change' }\n                    ]\n                },\n\n                // 抽屉相关（仅用于子类别）\n                drawerVisible: false,\n                drawerTitle: '',\n                isEdit: false, // 是否为编辑模式\n\n                // 子类别表单参数\n                form: {\n                    id: null,\n                    name: '',\n                    roleId: null,\n                    deptId: null\n                },\n\n                // 父级信息（用于子类别表单显示）\n                parentRoleName: '',\n                parentCategoryName: '',\n                parentCategoryId: null,\n                parentRoleId: null,\n\n                // 子类别表单校验\n                rules: {\n                    name: [\n                        { required: true, message: '请输入维修子类别名称', trigger: 'blur' }\n                    ]\n                },\n            }\n        },\n        methods: {\n            guid2() {\n                function S4() {\n                    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);\n                }\n                return (S4() + S4() + \"-\" + S4() + \"-\" + S4() + \"-\" + S4() + \"-\" + S4() + S4() + S4());\n            },\n\n            //-------新的方法---------\n\n            // 添加维修类别\n            handleAddCategory() {\n                this.resetCategoryForm();\n                this.isCategoryEdit = false;\n                this.categoryDialogTitle = '添加维修类别';\n                this.categoryDialogVisible = true;\n            },\n\n            // 添加子类别\n            handleAddSubcategory(categoryRow) {\n                this.resetForm();\n                this.formType = 'subcategory';\n                this.isEdit = false;\n                this.drawerTitle = '添加维修子类别';\n\n                // 设置父级信息\n                this.parentCategoryId = categoryRow.deptsId;\n                this.parentCategoryName = categoryRow.deptname;\n                this.parentRoleId = categoryRow.roleId;\n                this.parentRoleName = categoryRow.roleName;\n\n                this.drawerVisible = true;\n            },\n\n            // 修改维修类别\n            handleUpdateCategory(categoryRow) {\n                this.isCategoryEdit = true;\n                this.categoryDialogTitle = '修改维修类别';\n\n                this.categoryForm = {\n                    id: categoryRow.deptsId,\n                    name: categoryRow.deptname,\n                    roleId: categoryRow.roleId\n                };\n\n                this.categoryDialogVisible = true;\n            },\n\n            // 修改维修子类别\n            handleUpdateSubcategory(subcategoryRow) {\n                this.isEdit = true;\n                this.drawerTitle = '修改维修子类别';\n\n                // 从父级数据中获取信息\n                const parentCategory = this.findParentCategory(subcategoryRow.classesId);\n                if (parentCategory) {\n                    this.parentCategoryId = parentCategory.deptsId;\n                    this.parentCategoryName = parentCategory.deptname;\n                    this.parentRoleId = parentCategory.roleId;\n                    this.parentRoleName = parentCategory.roleName;\n                }\n\n                this.form = {\n                    id: subcategoryRow.classesId,\n                    name: subcategoryRow.classesname\n                };\n\n                this.drawerVisible = true;\n            },\n\n            // 删除类别\n            handleDeleteCategory(categoryRow) {\n                this.$confirm('确认删除该维修类别吗？删除后其下的所有维修子类别也将被删除。', '提示', {\n                    confirmButtonText: '确定',\n                    cancelButtonText: '取消',\n                    type: 'warning'\n                }).then(() => {\n                    this.$del(\"/dept/\" + categoryRow.deptsId)\n                        .then((res) => {\n                            this.$message.success(\"删除成功\");\n                            this.newList();\n                        })\n                        .catch(error => {\n                            this.$message.error(\"删除失败：\" + error.message);\n                        });\n                });\n            },\n\n            // 删除子类别\n            handleDeleteSubcategory(subcategoryRow) {\n                this.$confirm('确认删除该维修子类别吗？', '提示', {\n                    confirmButtonText: '确定',\n                    cancelButtonText: '取消',\n                    type: 'warning'\n                }).then(() => {\n                    this.$del(\"/class/\" + subcategoryRow.classesId)\n                        .then((res) => {\n                            this.$message.success(\"删除成功\");\n                            this.newList();\n                        })\n                        .catch(error => {\n                            this.$message.error(\"删除失败：\" + error.message);\n                        });\n                });\n            },\n\n            // 提交维修类别表单\n            submitCategoryForm() {\n                this.$refs[\"categoryForm\"].validate(valid => {\n                    if (valid) {\n                        this.submitCategory();\n                    }\n                });\n            },\n\n            // 提交子类别表单\n            submitForm() {\n                this.$refs[\"form\"].validate(valid => {\n                    if (valid) {\n                        this.submitSubcategory();\n                    }\n                });\n            },\n\n            // 提交维修类别\n            submitCategory() {\n                if (this.isCategoryEdit) {\n                    // 修改维修类别\n                    this.$put(\"/dept\", {\n                        id: this.categoryForm.id,\n                        name: this.categoryForm.name,\n                        roleId: this.categoryForm.roleId\n                    }).then(() => {\n                        this.$message.success(\"修改维修类别成功\");\n                        this.categoryDialogVisible = false;\n                        this.newList();\n                    }).catch(error => {\n                        this.$message.error(\"修改失败：\" + error.message);\n                    });\n                } else {\n                    // 添加维修类别\n                    this.$post(\"/dept\", {\n                        name: this.categoryForm.name,\n                        roleId: this.categoryForm.roleId\n                    }).then(() => {\n                        this.$message.success(\"添加维修类别成功\");\n                        this.categoryDialogVisible = false;\n                        this.newList();\n                    }).catch(error => {\n                        this.$message.error(\"添加失败：\" + error.message);\n                    });\n                }\n            },\n\n            // 提交子类别\n            submitSubcategory() {\n                if (this.isEdit) {\n                    // 修改子类别\n                    this.$put(\"/class\", {\n                        id: this.form.id,\n                        name: this.form.name,\n                        roleId: this.parentRoleId,\n                        deptId: this.parentCategoryId\n                    }).then(() => {\n                        this.$message.success(\"修改维修子类别成功\");\n                        this.drawerVisible = false;\n                        this.newList();\n                    }).catch(error => {\n                        this.$message.error(\"修改失败：\" + error.message);\n                    });\n                } else {\n                    // 添加子类别\n                    this.$post(\"/class\", {\n                        name: this.form.name,\n                        roleId: this.parentRoleId,\n                        deptId: this.parentCategoryId\n                    }).then(() => {\n                        this.$message.success(\"添加维修子类别成功\");\n                        this.drawerVisible = false;\n                        this.newList();\n                    }).catch(error => {\n                        this.$message.error(\"添加失败：\" + error.message);\n                    });\n                }\n            },\n\n            // 关闭维修类别弹窗\n            handleCategoryDialogClose() {\n                this.categoryDialogVisible = false;\n                this.resetCategoryForm();\n            },\n\n            // 关闭子类别抽屉\n            handleDrawerClose() {\n                this.drawerVisible = false;\n                this.resetForm();\n            },\n\n            // 获取数据列表\n            newList(){\n                this.$get('/role')\n                .then(res => {\n                    if (res.data && res.data.role) {\n                        this.roles = res.data.role;\n                        // 提取角色选项\n                        this.roleOptions = res.data.role.map(role => ({\n                            id: role.id,\n                            name: role.name\n                        }));\n                    } else {\n                        this.roles = [];\n                        this.roleOptions = [];\n                        console.warn('获取角色数据失败，返回数据格式不正确');\n                    }\n                })\n                .catch(error => {\n                    console.error('获取角色列表失败:', error);\n                    this.roles = [];\n                    this.roleOptions = [];\n                    this.$message.error('获取角色列表失败，请稍后重试');\n                });\n            },\n\n            // 维修类别表单重置\n            resetCategoryForm() {\n                this.categoryForm = {\n                    id: null,\n                    name: '',\n                    roleId: null\n                };\n\n                if (this.$refs.categoryForm) {\n                    this.$refs.categoryForm.resetFields();\n                }\n            },\n\n            // 子类别表单重置\n            resetForm() {\n                this.form = {\n                    id: null,\n                    name: '',\n                    roleId: null,\n                    deptId: null\n                };\n                this.parentRoleName = '';\n                this.parentCategoryName = '';\n                this.parentCategoryId = null;\n                this.parentRoleId = null;\n\n                if (this.$refs.form) {\n                    this.$refs.form.resetFields();\n                }\n            },\n\n\n\n            // 辅助方法：根据子类别ID查找父级维修类别\n            findParentCategory(classId) {\n                for (let dept of this.dataList) {\n                    if (dept.children) {\n                        for (let child of dept.children) {\n                            if (child.classesId === classId) {\n                                return dept;\n                            }\n                        }\n                    }\n                }\n                return null;\n            },\n        },\n\n        created() {\n            this.newList();\n        },\n\n        computed:{\n            dataList(){\n                if (!this.roles || !Array.isArray(this.roles)) {\n                    return [];\n                }\n\n                let allDepts = [];\n\n                // 遍历所有角色，收集所有的维修类别\n                this.roles.forEach(role => {\n                    if (!role || !role.depts) return;\n\n                    role.depts.forEach(dept => {\n                        if (!dept) return;\n\n                        let children = [];\n                        const deptClasses = dept['classes'] || [];\n\n                        // 添加子类别\n                        deptClasses.forEach(classItem => {\n                            if (!classItem) return;\n\n                            children.push({\n                                classesId: classItem.id,\n                                classesname: classItem.name || '',\n                                uuid: this.guid2()\n                            });\n                        });\n\n                        // 添加维修类别\n                        allDepts.push({\n                            deptsId: dept.id,\n                            deptname: dept.name || '',\n                            roleId: role.id, // 保留角色ID用于操作\n                            roleName: role.name, // 保留角色名称用于表单显示\n                            children: children,\n                            uuid: this.guid2()\n                        });\n                    });\n                });\n\n                return allDepts;\n            }\n        }\n    }\n</script>\n\n<style scoped lang=\"less\">\n    .content {\n        padding: 0 1%;\n    }\n\n    .top-actions {\n        text-align: left;\n        margin-bottom: 20px;\n    }\n\n    .drawer-footer {\n        text-align: right;\n        margin-top: 30px;\n        padding-top: 20px;\n        border-top: 1px solid #e8e8e8;\n    }\n\n    .drawer-footer .el-button {\n        margin-left: 10px;\n    }\n\n    // 操作按钮样式优化\n    .el-table .el-button--mini {\n        margin-right: 5px;\n        margin-bottom: 5px;\n    }\n\n    // 角色不可操作提示样式\n    .el-table .role-no-operation {\n        color: #909399;\n        font-size: 12px;\n        font-style: italic;\n    }\n</style>\n"], "sourceRoot": "src/views/admin/children"}]}