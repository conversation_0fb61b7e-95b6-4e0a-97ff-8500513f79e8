{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\Role.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\Role.vue", "mtime": 1748712855482}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Role.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+EA,eAAA;AACA,EAAA,IAAA,EAAA,MADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA,MAAA,KAAA,EAAA,IADA;AAGA,MAAA,KAAA,EAAA,EAHA;AAIA;AACA,MAAA,KAAA,EAAA,EALA;AAMA;AACA,MAAA,IAAA,EAAA,KAPA;AAQA;AACA,MAAA,IAAA,EAAA;AACA,QAAA,EAAA,EAAA,IADA;AAEA,QAAA,IAAA,EAAA,EAFA;AAGA,QAAA,QAAA,EAAA,EAHA;AAIA,QAAA,WAAA,EAAA;AAJA,OATA;AAeA;AACA,MAAA,KAAA,EAAA;AACA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,QAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAJA;AAOA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AAPA;AAhBA,KAAA;AA4BA,GA/BA;AAgCA,EAAA,OAAA,EAAA;AACA,IAAA,KADA,mBACA;AACA,eAAA,EAAA,GAAA;AACA,eAAA,CAAA,CAAA,IAAA,IAAA,CAAA,MAAA,EAAA,IAAA,OAAA,GAAA,CAAA,EAAA,QAAA,CAAA,EAAA,EAAA,SAAA,CAAA,CAAA,CAAA;AACA;;AACA,aAAA,EAAA,KAAA,EAAA,EAAA,GAAA,GAAA,GAAA,EAAA,EAAA,GAAA,GAAA,GAAA,EAAA,EAAA,GAAA,GAAA,GAAA,EAAA,EAAA,GAAA,GAAA,GAAA,EAAA,EAAA,GAAA,EAAA,EAAA,GAAA,EAAA,EAAA;AACA,KANA;AAQA;;AAEA;AACA,IAAA,SAXA,qBAWA,CAXA,EAWA,CAXA,EAWA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,CAAA,CAAA,QAAA,CAAA,MAAA,EADA,CAEA;;AACA,UAAA,CAAA,CAAA,MAAA,IAAA,IAAA,EAAA;AACA,aAAA,KAAA,GAAA,CAAA;AACA,aAAA,KAAA;AACA,aAAA,KAAA,GAAA,QAAA;AACA,aAAA,IAAA,GAAA,IAAA;AACA;AACA,KApBA;;AAsBA;AACA,IAAA,MAvBA,oBAuBA;AAAA;;AACA,WAAA,KAAA,CAAA,MAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,cAAA,KAAA,CAAA,IAAA,CAAA,EAAA,IAAA,IAAA,EAAA;AACA;AACA,YAAA,KAAA,CAAA,IAAA,CAAA,OAAA,EAAA,KAAA,CAAA,IAAA,EAAA,IAAA,CAAA,UAAA,QAAA,EAAA;AACA,cAAA,KAAA,CAAA,QAAA,CAAA,MAAA;;AACA,cAAA,KAAA,CAAA,IAAA,GAAA,KAAA;;AACA,cAAA,KAAA,CAAA,OAAA;AACA,aAJA,EAIA,KAJA,CAIA,UAAA,KAAA,EAAA;AACA,cAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,UAAA,KAAA,CAAA,OAAA;AACA,aANA;AAOA,WATA,MASA;AACA;AACA,YAAA,KAAA,CAAA,KAAA,CAAA,OAAA,EAAA;AACA,sBAAA,KAAA,CAAA,IAAA,CAAA;AADA,aAAA,EAEA,IAFA,CAEA,UAAA,QAAA,EAAA;AACA,cAAA,KAAA,CAAA,QAAA,CAAA,QAAA;;AACA,cAAA,KAAA,CAAA,OAAA,GAFA,CAIA;;;AACA,cAAA,KAAA,CAAA,KAAA,CAAA,OAAA,EAAA;AACA,0BAAA,KAAA,CAAA,KAAA,CAAA,MADA;AAEA,wBAAA,KAAA,CAAA,IAAA,CAAA;AAFA,eAAA,EAGA,IAHA,CAGA,UAAA,QAAA,EAAA;AACA,oBAAA,IAAA,GAAA,KAAA,CAAA,IAAA,CAAA,QAAA,CADA,CAEA;;AACA,qBAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,KAAA,CAAA,KAAA,CAAA,QAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,sBAAA,KAAA,CAAA,KAAA,CAAA,QAAA,CAAA,CAAA,EAAA,QAAA,KAAA,IAAA,EAAA;AACA,oBAAA,KAAA,CAAA,QAAA,CAAA,QAAA;;AACA,oBAAA,KAAA,CAAA,OAAA,GAFA,CAIA;;;AACA,oBAAA,KAAA,CAAA,KAAA,CAAA,QAAA,EAAA;AACA,gCAAA,KAAA,CAAA,KAAA,CAAA,MADA;AAEA,gCAAA,KAAA,CAAA,KAAA,CAAA,QAAA,CAAA,CAAA,EAAA,OAFA;AAGA,8BAAA,KAAA,CAAA,IAAA,CAAA;AAHA,qBAAA,EAIA,IAJA,CAIA,UAAA,QAAA,EAAA;AACA,sBAAA,KAAA,CAAA,QAAA,CAAA,QAAA;;AACA,sBAAA,KAAA,CAAA,OAAA;;AACA,sBAAA,KAAA,CAAA,IAAA,GAAA,KAAA;AACA,qBARA,EAQA,KARA,CAQA,UAAA,KAAA,EAAA;AACA,sBAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,YAAA,KAAA,CAAA,OAAA;AACA,qBAVA;;AAWA;AACA;AACA;AACA,eA1BA,EA0BA,KA1BA,CA0BA,UAAA,KAAA,EAAA;AACA,gBAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,YAAA,KAAA,CAAA,OAAA;AACA,eA5BA;AA6BA,aApCA,EAoCA,KApCA,CAoCA,UAAA,KAAA,EAAA;AACA,cAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,YAAA,KAAA,CAAA,OAAA;AACA,aAtCA;AAuCA;AACA;AACA,OAtDA;AAuDA,KA/EA;AAmFA;AACA,IAAA,MApFA,oBAoFA;AACA,WAAA,IAAA,GAAA,KAAA;AACA,KAtFA;AAwFA,IAAA,YAxFA,wBAwFA,CAxFA,EAwFA,CAxFA,EAwFA;AAAA;;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,CAAA;;AACA,UAAA,CAAA,CAAA,MAAA,IAAA,IAAA,EAAA;AACA,aAAA,IAAA,CAAA,WAAA,CAAA,CAAA,MAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,IAAA,EAAA,GAAA,CAAA,IAAA,CAAA,GAAA,EAAA,SAAA;;AACA,UAAA,MAAA,CAAA,OAAA;AACA,SAJA;AAKA,OANA,MAMA,IAAA,CAAA,CAAA,OAAA,IAAA,IAAA,EAAA;AACA,aAAA,IAAA,CAAA,WAAA,CAAA,CAAA,OAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,IAAA,EAAA,GAAA,CAAA,IAAA,CAAA,GAAA,EAAA,SAAA;;AACA,UAAA,MAAA,CAAA,OAAA;AACA,SAJA;AAKA,OANA,MAMA;AACA,aAAA,IAAA,CAAA,YAAA,CAAA,CAAA,SAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,IAAA,EAAA,GAAA,CAAA,IAAA,CAAA,GAAA,EAAA,SAAA;;AACA,UAAA,MAAA,CAAA,OAAA;AACA,SAJA;AAKA;AACA,KA7GA;;AAgHA;AACA,IAAA,YAjHA,wBAiHA,CAjHA,EAiHA,CAjHA,EAiHA;AAAA;;AACA;AACA,WAAA,IAAA,CAAA,WAAA,CAAA,CAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,IAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA;AACA,QAAA,MAAA,CAAA,IAAA,GAAA,IAAA;AACA,QAAA,MAAA,CAAA,KAAA,GAAA,QAAA;AACA,OAJA;AAKA,KAxHA;AA0HA,IAAA,OA1HA,qBA0HA;AAAA;;AACA,WAAA,IAAA,CAAA,OAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA;AACA;AACA,YAAA,GAAA,CAAA,IAAA,IAAA,GAAA,CAAA,IAAA,CAAA,IAAA,EAAA;AACA,UAAA,MAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA;AACA,SAFA,MAEA;AACA,UAAA,MAAA,CAAA,KAAA,GAAA,EAAA;AACA,UAAA,OAAA,CAAA,IAAA,CAAA,oBAAA;AACA;AACA,OAVA,EAWA,KAXA,CAWA,UAAA,KAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,WAAA,EAAA,KAAA;AACA,QAAA,MAAA,CAAA,KAAA,GAAA,EAAA;;AACA,QAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,gBAAA;AACA,OAfA;AAgBA,KA3IA;AA6IA;AACA,IAAA,KA9IA,mBA8IA;AACA,WAAA,IAAA,GAAA;AACA,QAAA,EAAA,EAAA,IADA;AAEA,QAAA,IAAA,EAAA,EAFA;AAGA,QAAA,QAAA,EAAA,EAHA;AAIA,QAAA,WAAA,EAAA;AAJA,OAAA;;AAMA,UAAA,KAAA,KAAA,CAAA,IAAA,EAAA;AACA,aAAA,KAAA,CAAA,IAAA,CAAA,WAAA;AACA;AACA,KAxJA,CAyJA;;AAzJA,GAhCA;AA4LA,EAAA,OA5LA,qBA4LA;AACA,SAAA,OAAA;AACA,GA9LA;AAgMA,EAAA,QAAA,EAAA;AACA,IAAA,QADA,sBACA;AAAA;;AACA,UAAA,CAAA,KAAA,KAAA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,KAAA,KAAA,CAAA,EAAA;AACA,eAAA,EAAA;AACA;;AAEA,aAAA,KAAA,KAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,CAAA,IAAA,EAAA,OAAA,IAAA;AAEA,YAAA,KAAA,GAAA,EAAA;AACA,YAAA,SAAA,GAAA,IAAA,CAAA,OAAA,CAAA,IAAA,EAAA;;AAEA,aAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,SAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,cAAA,IAAA,GAAA,SAAA,CAAA,CAAA,CAAA;AACA,cAAA,CAAA,IAAA,EAAA,SAFA,CAIA;;AACA,UAAA,KAAA,CAAA,IAAA,CACA;AACA,YAAA,OAAA,EAAA,IAAA,CAAA,EADA;AAEA,YAAA,QAAA,EAAA,IAAA,CAAA,IAAA,IAAA,EAFA;AAGA,YAAA,IAAA,EAAA,MAAA,CAAA,KAAA;AAHA,WADA,EALA,CAYA;;AACA,cAAA,QAAA,GAAA,EAAA;AACA,cAAA,WAAA,GAAA,IAAA,CAAA,SAAA,CAAA,IAAA,EAAA;;AAEA,eAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,WAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,gBAAA,SAAA,GAAA,WAAA,CAAA,CAAA,CAAA;AACA,gBAAA,CAAA,SAAA,EAAA;AAEA,YAAA,QAAA,CAAA,IAAA,CACA;AACA,cAAA,SAAA,EAAA,SAAA,CAAA,EADA;AAEA,cAAA,WAAA,EAAA,SAAA,CAAA,IAAA,IAAA,EAFA;AAGA,cAAA,IAAA,EAAA,MAAA,CAAA,KAAA;AAHA,aADA;AAOA;;AACA,UAAA,KAAA,CAAA,CAAA,CAAA,CAAA,QAAA,GAAA,QAAA;AACA;;AACA,eAAA;AACA,UAAA,MAAA,EAAA,IAAA,CAAA,EADA;AAEA,UAAA,IAAA,EAAA,IAAA,CAAA,IAAA,IAAA,EAFA;AAGA,UAAA,QAAA,EAAA,KAHA;AAIA,UAAA,IAAA,EAAA,MAAA,CAAA,KAAA;AAJA,SAAA;AAMA,OA1CA,EA0CA,MA1CA,CA0CA,UAAA,IAAA;AAAA,eAAA,IAAA,KAAA,IAAA;AAAA,OA1CA,CAAA;AA2CA;AAjDA;AAhMA,CAAA", "sourcesContent": ["<template>\n    <div class=\"content\">\n        <el-table\n                :data=\"dataList\"\n                style=\"width: 100%;margin-bottom: 20px;\"\n                row-key=\"uuid\"\n                border\n                :default-expand-all = 'false'\n                :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\">\n            <el-table-column\n                    prop=\"name\"\n                    label=\"角色\"\n                    sortable\n                    width=\"230\">\n            </el-table-column>\n            <el-table-column\n                    prop=\"deptname\"\n                    label=\"类别\"\n                    sortable\n                    width=\"180\">\n            </el-table-column>\n            <el-table-column\n                    prop=\"classesname\"\n                    label=\"子类别\">\n            </el-table-column>\n\n            <el-table-column label=\"操作\">\n                <template slot-scope=\"scope\">\n                    <el-button\n                            icon=\"el-icon-plus\"\n                            size=\"mini\"\n                            @click=\"handleAdd(scope.$index, scope.row)\">增加\n                    </el-button>\n\n                    <el-button\n                            icon=\"el-icon-edit\"\n                            size=\"mini\"\n                            @click=\"handleUpdate(scope.$index, scope.row)\">修改\n                    </el-button>\n\n                    <el-button\n                            icon=\"el-icon-delete\"\n                            size=\"mini\"\n                            type=\"danger\"\n                            @click=\"handleDelete(scope.$index, scope.row)\">删除\n                    </el-button>\n                </template>\n            </el-table-column>\n\n            <!-- ------------新加入---------- -->\n\n\n            <!-- 添加或修改角色管理对话框 -->\n            <el-dialog :title=\"title\"  :visible.sync=\"open\" width=\"500px\" append-to-body>\n                <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n                        <el-form-item label=\"角色\" prop=\"name\">\n                        <el-input v-model=\"form.name\" placeholder=\"请输入角色\" />\n                        </el-form-item>\n                        <el-form-item label=\"类别\" prop=\"deptname\">\n                        <el-input v-model=\"form.deptname\" placeholder=\"请输入类别\" />\n                        </el-form-item>\n                        <el-form-item label=\"子类别\"  prop=\"classesname\">\n                        <el-input  placeholder=\"请输入子类别\" v-model=\"form.classesname\"/>\n                        </el-form-item>\n                    </el-form>\n                         <div slot=\"footer\" class=\"dialog-footer\" >\n                            <el-button type=\"primary\" @click=\"submit()\">确 定</el-button>\n                            <el-button @click=\"cancel\">取 消</el-button>\n                        </div>\n            </el-dialog>\n\n        </el-table>\n\n\n        <!-- ------------新加入---------- -->\n    </div>\n</template>\n\n<script>\n    export default {\n        name: \"Role\",\n        data() {\n            return {\n                value:null,\n\n                roles : [],\n                // 弹出层标题\n                title: \"\",\n                // 是否显示弹出层\n                open: false,\n                // 表单参数\n                form: {\n                    id: null,\n                    name: '',\n                    deptname: '',\n                    classesname: ''\n                },\n                // 表单校验\n                rules: {\n                    name: [\n                        { required: true, message: '请输入角色名称', trigger: 'blur' }\n                    ],\n                    deptname: [\n                        { required: true, message: '请输入类别名称', trigger: 'blur' }\n                    ],\n                    classesname: [\n                        { required: true, message: '请输入子类别名称', trigger: 'blur' }\n                    ]\n                },\n            }\n        },\n        methods: {\n            guid2() {\n                function S4() {\n                    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);\n                }\n                return (S4() + S4() + \"-\" + S4() + \"-\" + S4() + \"-\" + S4() + \"-\" + S4() + S4() + S4());\n            },\n\n            //-------新加入---------\n\n            /** 新增按钮操作 */\n            handleAdd(a,b) {\n                console.log(b.children.length)\n                // console.log(b)\n                if(b.roleId != null){\n                    this.value=b;\n                    this.reset();\n                    this.title = \"添加角色信息\";\n                    this.open = true;\n                }\n            },\n\n            /** 提交按钮 */\n            submit() {\n                this.$refs[\"form\"].validate(valid => {\n                    if (valid) {\n                        if (this.form.id != null) {\n                            // 修改角色信息\n                            this.$put(\"/role\", this.form).then(response => {\n                                this.$message(\"修改成功\");\n                                this.open = false;\n                                this.newList();\n                            }).catch(error => {\n                                this.$message.error(\"修改失败：\" + error.message);\n                            });\n                        } else {\n                            // 先添加角色\n                            this.$post(\"/role\", {\n                                \"name\": this.form.name,\n                            }).then(response => {\n                                this.$message(\"新增角色成功\");\n                                this.newList();\n\n                                // 添加系别\n                                this.$post(\"/dept\", {\n                                    \"roleId\": this.value.roleId,\n                                    \"name\": this.form.deptname,\n                                }).then(response => {\n                                    const name = this.form.deptname;\n                                    // 遍历查找匹配的系别\n                                    for (let i = 0; i < this.value.children.length; i++) {\n                                        if (this.value.children[i].deptname === name) {\n                                            this.$message(\"新增专业成功\");\n                                            this.newList();\n\n                                            // 添加班级\n                                            this.$post(\"/class\", {\n                                                \"roleId\": this.value.roleId,\n                                                \"deptId\": this.value.children[i].deptsId,\n                                                \"name\": this.form.classesname,\n                                            }).then(response => {\n                                                this.$message(\"新增班级成功\");\n                                                this.newList();\n                                                this.open = false;\n                                            }).catch(error => {\n                                                this.$message.error(\"添加班级失败：\" + error.message);\n                                            });\n                                            break;\n                                        }\n                                    }\n                                }).catch(error => {\n                                    this.$message.error(\"添加系别失败：\" + error.message);\n                                });\n                            }).catch(error => {\n                                this.$message.error(\"添加角色失败：\" + error.message);\n                            });\n                        }\n                    }\n                });\n            },\n\n\n\n            // 取消按钮\n            cancel() {\n            this.open = false;\n            },\n\n            handleDelete(a,b){\n                console.log(b);\n                if(b.roleId != null){\n                    this.$del(\"/role/\"+b.roleId)\n                    .then((res) => {\n                        this.$notifyMsg(\"成功\", res.data.msg, \"success\")\n                        this.newList()\n                    })\n                }else if(b.deptsId != null){\n                     this.$del(\"/dept/\"+b.deptsId)\n                        .then((res) => {\n                            this.$notifyMsg(\"成功\", res.data.msg, \"success\")\n                            this.newList()\n                        })\n                }else{\n                    this.$del(\"/class/\"+b.classesId)\n                        .then((res) => {\n                            this.$notifyMsg(\"成功\", res.data.msg, \"success\")\n                            this.newList()\n                        })\n                }\n            },\n\n\n            /** 修改按钮操作 */\n            handleUpdate(a,b) {\n                // console.log(b);\n                this.$get(\"/role/\"+b.roleId).then((res) => {\n                    this.form = res.data.role;\n                    this.open = true;\n                    this.title = \"修改角色管理\";\n                });\n            },\n\n            newList(){\n                this.$get('/role')\n                .then(res => {\n                    // console.log(res.data)\n                    // console.log(this.roles)\n                    if (res.data && res.data.role) {\n                        this.roles = res.data.role;\n                    } else {\n                        this.roles = [];\n                        console.warn('获取角色数据失败，返回数据格式不正确');\n                    }\n                })\n                .catch(error => {\n                    console.error('获取角色列表失败:', error);\n                    this.roles = [];\n                    this.$message.error('获取角色列表失败，请稍后重试');\n                });\n            },\n\n            // 表单重置\n            reset() {\n                this.form = {\n                    id: null,\n                    name: '',\n                    deptname: '',\n                    classesname: ''\n                };\n                if (this.$refs.form) {\n                    this.$refs.form.resetFields();\n                }\n            },\n            //-------新加入---------\n        },\n\n        created() {\n            this.newList();\n        },\n\n        computed:{\n            dataList(){\n                if (!this.roles || !Array.isArray(this.roles)) {\n                    return [];\n                }\n\n                return this.roles.map(item => {\n                    if (!item) return null;\n\n                    let depts = [];\n                    const itemDepts = item['depts'] || [];\n\n                    for (let i = 0; i < itemDepts.length; i++){\n                        const dept = itemDepts[i];\n                        if (!dept) continue;\n\n                        // console.log(dept['classes'])\n                        depts.push(\n                            {\n                                deptsId : dept.id,\n                                deptname : dept.name || '',\n                                uuid : this.guid2()\n                            }\n                        )\n                        // console.log(depts)\n                        let children = [];\n                        const deptClasses = dept['classes'] || [];\n\n                        for (let j = 0; j < deptClasses.length; j++){\n                            const classItem = deptClasses[j];\n                            if (!classItem) continue;\n\n                            children.push(\n                                {\n                                    classesId : classItem.id,\n                                    classesname: classItem.name || '',\n                                    uuid : this.guid2()\n                                }\n                            )\n                        }\n                        depts[i].children = children\n                    }\n                    return{\n                            roleId : item.id,\n                            name : item.name || '',\n                            children : depts,\n                            uuid : this.guid2()\n                    }\n                }).filter(item => item !== null);\n            }\n        }\n    }\n</script>\n\n<style scoped lang=\"less\">\n    .content {\n        padding: 0 1%;\n    }\n</style>\n"], "sourceRoot": "src/views/admin/children"}]}