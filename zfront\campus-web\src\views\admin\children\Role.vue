<template>
    <div class="content">
        <el-table
                :data="dataList"
                style="width: 100%;margin-bottom: 20px;"
                row-key="uuid"
                border
                :default-expand-all = 'false'
                :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
            <el-table-column
                    prop="name"
                    label="角色"
                    sortable
                    width="230">
            </el-table-column>
            <el-table-column
                    prop="deptname"
                    label="类别"
                    sortable
                    width="180">
            </el-table-column>
            <el-table-column
                    prop="classesname"
                    label="子类别">
            </el-table-column>

            <el-table-column label="操作">
                <template slot-scope="scope">
                    <el-button
                            icon="el-icon-plus"
                            size="mini"
                            @click="handleAdd(scope.$index, scope.row)">增加
                    </el-button>

                    <el-button
                            icon="el-icon-edit"
                            size="mini"
                            @click="handleUpdate(scope.$index, scope.row)">修改
                    </el-button>

                    <el-button
                            icon="el-icon-delete"
                            size="mini"
                            type="danger"
                            @click="handleDelete(scope.$index, scope.row)">删除
                    </el-button>
                </template>
            </el-table-column>

            <!-- ------------新加入---------- -->


            <!-- 添加或修改角色管理对话框 -->
            <el-dialog :title="title"  :visible.sync="open" width="500px" append-to-body>
                <el-form ref="form" :model="form" :rules="rules" label-width="80px">
                        <el-form-item label="角色" prop="name">
                        <el-input v-model="form.name" placeholder="请输入角色" />
                        </el-form-item>
                        <el-form-item label="类别" prop="deptname">
                        <el-input v-model="form.deptname" placeholder="请输入类别" />
                        </el-form-item>
                        <el-form-item label="子类别"  prop="classesname">
                        <el-input  placeholder="请输入子类别" v-model="form.classesname"/>
                        </el-form-item>
                    </el-form>
                         <div slot="footer" class="dialog-footer" >
                            <el-button type="primary" @click="submit()">确 定</el-button>
                            <el-button @click="cancel">取 消</el-button>
                        </div>
            </el-dialog>

        </el-table>


        <!-- ------------新加入---------- -->
    </div>
</template>

<script>
    export default {
        name: "Role",
        data() {
            return {
                value:null,

                roles : [],
                // 弹出层标题
                title: "",
                // 是否显示弹出层
                open: false,
                // 表单参数
                form: {
                    id: null,
                    name: '',
                    deptname: '',
                    classesname: ''
                },
                // 表单校验
                rules: {
                    name: [
                        { required: true, message: '请输入角色名称', trigger: 'blur' }
                    ],
                    deptname: [
                        { required: true, message: '请输入类别名称', trigger: 'blur' }
                    ],
                    classesname: [
                        { required: true, message: '请输入子类别名称', trigger: 'blur' }
                    ]
                },
            }
        },
        methods: {
            guid2() {
                function S4() {
                    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
                }
                return (S4() + S4() + "-" + S4() + "-" + S4() + "-" + S4() + "-" + S4() + S4() + S4());
            },

            //-------新加入---------

            /** 新增按钮操作 */
            handleAdd(a,b) {
                console.log(b.children.length)
                // console.log(b)
                if(b.roleId != null){
                    this.value=b;
                    this.reset();
                    this.title = "添加角色信息";
                    this.open = true;
                }
            },

            /** 提交按钮 */
            submit() {
                this.$refs["form"].validate(valid => {
                    if (valid) {
                        if (this.form.id != null) {
                            // 修改角色信息
                            this.$put("/role", this.form).then(response => {
                                this.$message("修改成功");
                                this.open = false;
                                this.newList();
                            }).catch(error => {
                                this.$message.error("修改失败：" + error.message);
                            });
                        } else {
                            // 先添加角色
                            this.$post("/role", {
                                "name": this.form.name,
                            }).then(response => {
                                this.$message("新增角色成功");
                                this.newList();

                                // 添加系别
                                this.$post("/dept", {
                                    "roleId": this.value.roleId,
                                    "name": this.form.deptname,
                                }).then(response => {
                                    const name = this.form.deptname;
                                    // 遍历查找匹配的系别
                                    for (let i = 0; i < this.value.children.length; i++) {
                                        if (this.value.children[i].deptname === name) {
                                            this.$message("新增专业成功");
                                            this.newList();

                                            // 添加班级
                                            this.$post("/class", {
                                                "roleId": this.value.roleId,
                                                "deptId": this.value.children[i].deptsId,
                                                "name": this.form.classesname,
                                            }).then(response => {
                                                this.$message("新增班级成功");
                                                this.newList();
                                                this.open = false;
                                            }).catch(error => {
                                                this.$message.error("添加班级失败：" + error.message);
                                            });
                                            break;
                                        }
                                    }
                                }).catch(error => {
                                    this.$message.error("添加系别失败：" + error.message);
                                });
                            }).catch(error => {
                                this.$message.error("添加角色失败：" + error.message);
                            });
                        }
                    }
                });
            },



            // 取消按钮
            cancel() {
            this.open = false;
            },

            handleDelete(a,b){
                console.log(b);
                if(b.roleId != null){
                    this.$del("/role/"+b.roleId)
                    .then((res) => {
                        this.$notifyMsg("成功", res.data.msg, "success")
                        this.newList()
                    })
                }else if(b.deptsId != null){
                     this.$del("/dept/"+b.deptsId)
                        .then((res) => {
                            this.$notifyMsg("成功", res.data.msg, "success")
                            this.newList()
                        })
                }else{
                    this.$del("/class/"+b.classesId)
                        .then((res) => {
                            this.$notifyMsg("成功", res.data.msg, "success")
                            this.newList()
                        })
                }
            },


            /** 修改按钮操作 */
            handleUpdate(a,b) {
                // console.log(b);
                this.$get("/role/"+b.roleId).then((res) => {
                    this.form = res.data.role;
                    this.open = true;
                    this.title = "修改角色管理";
                });
            },

            newList(){
                this.$get('/role')
                .then(res => {
                    // console.log(res.data)
                    // console.log(this.roles)
                    if (res.data && res.data.role) {
                        this.roles = res.data.role;
                    } else {
                        this.roles = [];
                        console.warn('获取角色数据失败，返回数据格式不正确');
                    }
                })
                .catch(error => {
                    console.error('获取角色列表失败:', error);
                    this.roles = [];
                    this.$message.error('获取角色列表失败，请稍后重试');
                });
            },

            // 表单重置
            reset() {
                this.form = {
                    id: null,
                    name: '',
                    deptname: '',
                    classesname: ''
                };
                if (this.$refs.form) {
                    this.$refs.form.resetFields();
                }
            },
            //-------新加入---------
        },

        created() {
            this.newList();
        },

        computed:{
            dataList(){
                if (!this.roles || !Array.isArray(this.roles)) {
                    return [];
                }

                return this.roles.map(item => {
                    if (!item) return null;

                    let depts = [];
                    const itemDepts = item['depts'] || [];

                    for (let i = 0; i < itemDepts.length; i++){
                        const dept = itemDepts[i];
                        if (!dept) continue;

                        // console.log(dept['classes'])
                        depts.push(
                            {
                                deptsId : dept.id,
                                deptname : dept.name || '',
                                uuid : this.guid2()
                            }
                        )
                        // console.log(depts)
                        let children = [];
                        const deptClasses = dept['classes'] || [];

                        for (let j = 0; j < deptClasses.length; j++){
                            const classItem = deptClasses[j];
                            if (!classItem) continue;

                            children.push(
                                {
                                    classesId : classItem.id,
                                    classesname: classItem.name || '',
                                    uuid : this.guid2()
                                }
                            )
                        }
                        depts[i].children = children
                    }
                    return{
                            roleId : item.id,
                            name : item.name || '',
                            children : depts,
                            uuid : this.guid2()
                    }
                }).filter(item => item !== null);
            }
        }
    }
</script>

<style scoped lang="less">
    .content {
        padding: 0 1%;
    }
</style>
