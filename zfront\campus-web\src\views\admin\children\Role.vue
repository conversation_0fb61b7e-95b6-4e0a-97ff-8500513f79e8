<template>
    <div class="content">
        <!-- 顶部操作按钮 -->
        <div class="top-actions" style="margin-bottom: 20px;">
            <el-button type="primary" icon="el-icon-plus" @click="handleAddCategory">添加维修类别</el-button>
        </div>

        <el-table
                :data="dataList"
                style="width: 100%"
                :resizable="false"
                row-key="uuid"
                border
                :default-expand-all = 'false'
                :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
            <el-table-column
                    prop="deptname"
                    label="维修类别"
                    sortable
                    min-width="120">
            </el-table-column>
            <el-table-column
                    prop="classesname"
                    label="维修子类别"
                    min-width="120">
            </el-table-column>

            <el-table-column 
                    label="操作" 
                    width="280"
                    align="center"
                    header-align="center"
                    fixed="right">
                <template slot-scope="scope">
                    <!-- 维修类别级别：可以增加子类别、修改、删除 -->
                    <template v-if="scope.row.deptsId">
                        <el-button 
                            type="text" 
                            size="small"
                            @click="handleAddSubcategory(scope.row)">
                            <i class="el-icon-plus" style="color: #409EFF"></i>
                            添加子类别
                        </el-button>
                        <el-button 
                            type="text" 
                            size="small"
                            @click="handleUpdateCategory(scope.row)">
                            <i class="el-icon-edit" style="color: #409EFF"></i>
                            修改
                        </el-button>
                        <el-popconfirm
                            confirm-button-text='确定'
                            cancel-button-text='取消'
                            icon="el-icon-info"
                            icon-color="red"
                            title="确定删除该维修类别吗？删除后其下的所有维修子类别也将被删除。"
                            @confirm="handleDeleteCategory(scope.row)"
                        >
                            <el-button type="text" size="small" slot="reference">
                                <i class="el-icon-delete" style="color: red"></i>
                                删除
                            </el-button>
                        </el-popconfirm>
                    </template>

                    <!-- 维修子类别级别：可以修改、删除 -->
                    <template v-else-if="scope.row.classesId">
                        <el-button 
                            type="text" 
                            size="small"
                            @click="handleUpdateSubcategory(scope.row)">
                            <i class="el-icon-edit" style="color: #409EFF"></i>
                            修改
                        </el-button>
                        <el-popconfirm
                            confirm-button-text='确定'
                            cancel-button-text='取消'
                            icon="el-icon-info"
                            icon-color="red"
                            title="确定删除该维修子类别吗？"
                            @confirm="handleDeleteSubcategory(scope.row)"
                        >
                            <el-button type="text" size="small" slot="reference">
                                <i class="el-icon-delete" style="color: red"></i>
                                删除
                            </el-button>
                        </el-popconfirm>
                    </template>
                </template>
            </el-table-column>
        </el-table>

        <!-- 添加维修类别弹窗 -->
        <el-dialog
            :title="categoryDialogTitle"
            :visible.sync="categoryDialogVisible"
            width="500px"
            :before-close="handleCategoryDialogClose">

            <el-form ref="categoryForm" :model="categoryForm" :rules="categoryRules" label-width="120px">
                <el-form-item label="维修员角色" prop="roleId">
                    <el-select v-model="categoryForm.roleId" placeholder="请选择维修员角色" style="width: 100%;" :disabled="isCategoryEdit">
                        <el-option
                            v-for="role in roleOptions"
                            :key="role.id"
                            :label="role.name"
                            :value="role.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="维修类别名称" prop="name">
                    <el-input v-model="categoryForm.name" placeholder="请输入维修类别名称" />
                </el-form-item>
            </el-form>

            <span slot="footer" class="dialog-footer">
                <el-button @click="handleCategoryDialogClose">取 消</el-button>
                <el-button type="primary" @click="submitCategoryForm">确 定</el-button>
            </span>
        </el-dialog>

        <!-- 抽屉式表单（仅用于子类别） -->
        <el-drawer
            :title="drawerTitle"
            :visible.sync="drawerVisible"
            direction="rtl"
            size="400px"
            :before-close="handleDrawerClose">

            <div style="padding: 20px;">
                <el-form ref="form" :model="form" :rules="rules" label-width="120px">
                    <!-- 子类别表单 -->
                    <el-form-item label="维修员角色">
                        <el-input :value="parentRoleName" disabled />
                    </el-form-item>
                    <el-form-item label="所属维修类别">
                        <el-input :value="parentCategoryName" disabled />
                    </el-form-item>
                    <el-form-item label="维修子类别名称" prop="name">
                        <el-input v-model="form.name" placeholder="请输入维修子类别名称" />
                    </el-form-item>
                </el-form>

                <div class="drawer-footer">
                    <el-button @click="handleDrawerClose">取 消</el-button>
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                </div>
            </div>
        </el-drawer>
    </div>
</template>

<script>
    export default {
        name: "Role",
        data() {
            return {
                roles: [],
                roleOptions: [], // 角色选项列表

                // 维修类别弹窗相关
                categoryDialogVisible: false,
                categoryDialogTitle: '',
                isCategoryEdit: false, // 是否为编辑模式

                // 维修类别表单参数
                categoryForm: {
                    id: null,
                    name: '',
                    roleId: null
                },

                // 维修类别表单校验
                categoryRules: {
                    name: [
                        { required: true, message: '请输入维修类别名称', trigger: 'blur' }
                    ],
                    roleId: [
                        { required: true, message: '请选择维修员角色', trigger: 'change' }
                    ]
                },

                // 抽屉相关（仅用于子类别）
                drawerVisible: false,
                drawerTitle: '',
                isEdit: false, // 是否为编辑模式

                // 子类别表单参数
                form: {
                    id: null,
                    name: '',
                    roleId: null,
                    deptId: null
                },

                // 父级信息（用于子类别表单显示）
                parentRoleName: '',
                parentCategoryName: '',
                parentCategoryId: null,
                parentRoleId: null,

                // 子类别表单校验
                rules: {
                    name: [
                        { required: true, message: '请输入维修子类别名称', trigger: 'blur' }
                    ]
                },
            }
        },
        methods: {
            guid2() {
                function S4() {
                    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
                }
                return (S4() + S4() + "-" + S4() + "-" + S4() + "-" + S4() + "-" + S4() + S4() + S4());
            },

            //-------新的方法---------

            // 添加维修类别
            handleAddCategory() {
                this.resetCategoryForm();
                this.isCategoryEdit = false;
                this.categoryDialogTitle = '添加维修类别';
                this.categoryDialogVisible = true;
            },

            // 添加子类别
            handleAddSubcategory(categoryRow) {
                this.resetForm();
                this.formType = 'subcategory';
                this.isEdit = false;
                this.drawerTitle = '添加维修子类别';

                // 设置父级信息
                this.parentCategoryId = categoryRow.deptsId;
                this.parentCategoryName = categoryRow.deptname;
                this.parentRoleId = categoryRow.roleId;
                this.parentRoleName = categoryRow.roleName;

                this.drawerVisible = true;
            },

            // 修改维修类别
            handleUpdateCategory(categoryRow) {
                this.isCategoryEdit = true;
                this.categoryDialogTitle = '修改维修类别';

                this.categoryForm = {
                    id: categoryRow.deptsId,
                    name: categoryRow.deptname,
                    roleId: categoryRow.roleId
                };

                this.categoryDialogVisible = true;
            },

            // 修改维修子类别
            handleUpdateSubcategory(subcategoryRow) {
                this.isEdit = true;
                this.drawerTitle = '修改维修子类别';

                // 从父级数据中获取信息
                const parentCategory = this.findParentCategory(subcategoryRow.classesId);
                if (parentCategory) {
                    this.parentCategoryId = parentCategory.deptsId;
                    this.parentCategoryName = parentCategory.deptname;
                    this.parentRoleId = parentCategory.roleId;
                    this.parentRoleName = parentCategory.roleName;
                }

                this.form = {
                    id: subcategoryRow.classesId,
                    name: subcategoryRow.classesname
                };

                this.drawerVisible = true;
            },

            // 删除类别
            handleDeleteCategory(categoryRow) {
                this.$confirm('确认删除该维修类别吗？删除后其下的所有维修子类别也将被删除。', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$del("/dept/" + categoryRow.deptsId)
                        .then((res) => {
                            this.$message.success("删除成功");
                            this.newList();
                        })
                        .catch(error => {
                            this.$message.error("删除失败：" + error.message);
                        });
                });
            },

            // 删除子类别
            handleDeleteSubcategory(subcategoryRow) {
                this.$confirm('确认删除该维修子类别吗？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$del("/class/" + subcategoryRow.classesId)
                        .then((res) => {
                            this.$message.success("删除成功");
                            this.newList();
                        })
                        .catch(error => {
                            this.$message.error("删除失败：" + error.message);
                        });
                });
            },

            // 提交维修类别表单
            submitCategoryForm() {
                this.$refs["categoryForm"].validate(valid => {
                    if (valid) {
                        this.submitCategory();
                    }
                });
            },

            // 提交子类别表单
            submitForm() {
                this.$refs["form"].validate(valid => {
                    if (valid) {
                        this.submitSubcategory();
                    }
                });
            },

            // 提交维修类别
            submitCategory() {
                if (this.isCategoryEdit) {
                    // 修改维修类别
                    this.$put("/dept", {
                        id: this.categoryForm.id,
                        name: this.categoryForm.name,
                        roleId: this.categoryForm.roleId
                    }).then(() => {
                        this.$message.success("修改维修类别成功");
                        this.categoryDialogVisible = false;
                        this.newList();
                    }).catch(error => {
                        this.$message.error("修改失败：" + error.message);
                    });
                } else {
                    // 添加维修类别
                    this.$post("/dept", {
                        name: this.categoryForm.name,
                        roleId: this.categoryForm.roleId
                    }).then(() => {
                        this.$message.success("添加维修类别成功");
                        this.categoryDialogVisible = false;
                        this.newList();
                    }).catch(error => {
                        this.$message.error("添加失败：" + error.message);
                    });
                }
            },

            // 提交子类别
            submitSubcategory() {
                if (this.isEdit) {
                    // 修改子类别
                    this.$put("/class", {
                        id: this.form.id,
                        name: this.form.name,
                        roleId: this.parentRoleId,
                        deptId: this.parentCategoryId
                    }).then(() => {
                        this.$message.success("修改维修子类别成功");
                        this.drawerVisible = false;
                        this.newList();
                    }).catch(error => {
                        this.$message.error("修改失败：" + error.message);
                    });
                } else {
                    // 添加子类别
                    this.$post("/class", {
                        name: this.form.name,
                        roleId: this.parentRoleId,
                        deptId: this.parentCategoryId
                    }).then(() => {
                        this.$message.success("添加维修子类别成功");
                        this.drawerVisible = false;
                        this.newList();
                    }).catch(error => {
                        this.$message.error("添加失败：" + error.message);
                    });
                }
            },

            // 关闭维修类别弹窗
            handleCategoryDialogClose() {
                this.categoryDialogVisible = false;
                this.resetCategoryForm();
            },

            // 关闭子类别抽屉
            handleDrawerClose() {
                this.drawerVisible = false;
                this.resetForm();
            },

            // 获取数据列表
            newList(){
                this.$get('/role')
                .then(res => {
                    if (res.data && res.data.role) {
                        this.roles = res.data.role;
                        // 提取角色选项，只显示维修员角色
                        this.roleOptions = res.data.role
                            .filter(role => role.name === '维修员')
                            .map(role => ({
                                id: role.id,
                                name: role.name
                            }));
                    } else {
                        this.roles = [];
                        this.roleOptions = [];
                        console.warn('获取角色数据失败，返回数据格式不正确');
                    }
                })
                .catch(error => {
                    console.error('获取角色列表失败:', error);
                    this.roles = [];
                    this.roleOptions = [];
                    this.$message.error('获取角色列表失败，请稍后重试');
                });
            },

            // 维修类别表单重置
            resetCategoryForm() {
                this.categoryForm = {
                    id: null,
                    name: '',
                    roleId: null
                };

                if (this.$refs.categoryForm) {
                    this.$refs.categoryForm.resetFields();
                }
            },

            // 子类别表单重置
            resetForm() {
                this.form = {
                    id: null,
                    name: '',
                    roleId: null,
                    deptId: null
                };
                this.parentRoleName = '';
                this.parentCategoryName = '';
                this.parentCategoryId = null;
                this.parentRoleId = null;

                if (this.$refs.form) {
                    this.$refs.form.resetFields();
                }
            },

            // 辅助方法：根据子类别ID查找父级维修类别
            findParentCategory(classId) {
                for (let dept of this.dataList) {
                    if (dept.children) {
                        for (let child of dept.children) {
                            if (child.classesId === classId) {
                                return dept;
                            }
                        }
                    }
                }
                return null;
            },
        },

        created() {
            this.newList();
        },

        computed:{
            dataList(){
                if (!this.roles || !Array.isArray(this.roles)) {
                    return [];
                }

                let allDepts = [];

                // 遍历所有角色，只收集维修员角色的维修类别
                this.roles.forEach(role => {
                    if (!role || !role.depts || role.name !== '维修员') return;

                    role.depts.forEach(dept => {
                        if (!dept) return;

                        let children = [];
                        const deptClasses = dept['classes'] || [];

                        // 添加子类别
                        deptClasses.forEach(classItem => {
                            if (!classItem) return;

                            children.push({
                                classesId: classItem.id,
                                classesname: classItem.name || '',
                                uuid: this.guid2()
                            });
                        });

                        // 添加维修类别
                        allDepts.push({
                            deptsId: dept.id,
                            deptname: dept.name || '',
                            roleId: role.id, // 保留角色ID用于操作
                            roleName: role.name, // 保留角色名称用于表单显示
                            children: children,
                            uuid: this.guid2()
                        });
                    });
                });

                return allDepts;
            }
        }
    }
</script>

<style scoped lang="less">
    .content {
        padding: 0 1%;
    }

    .top-actions {
        text-align: left;
        margin-bottom: 20px;
    }

    .drawer-footer {
        text-align: right;
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #e8e8e8;
    }

    .drawer-footer .el-button {
        margin-left: 10px;
    }

    // 操作按钮样式优化
    .el-table {
        /deep/ .el-button--text {
            margin-right: 10px;
            &:last-child {
                margin-right: 0;
            }
        }
        /deep/ .el-button [class*="el-icon-"] + span {
            margin-left: 3px;
        }
    }

    // 角色不可操作提示样式
    .el-table .role-no-operation {
        color: #909399;
        font-size: 12px;
        font-style: italic;
    }
</style>
