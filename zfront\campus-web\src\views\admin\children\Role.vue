<template>
    <div class="content">
        <!-- 顶部操作按钮 -->
        <div class="top-actions" style="margin-bottom: 20px;">
            <el-button type="primary" icon="el-icon-plus" @click="handleAddCategory">添加维修类别</el-button>
        </div>

        <el-table
                :data="dataList"
                style="width: 100%;margin-bottom: 20px;"
                row-key="uuid"
                border
                :default-expand-all = 'false'
                :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
            <el-table-column
                    prop="deptname"
                    label="维修类别"
                    sortable
                    width="250">
            </el-table-column>
            <el-table-column
                    prop="classesname"
                    label="维修子类别">
            </el-table-column>

            <el-table-column label="操作" width="280">
                <template slot-scope="scope">
                    <!-- 维修类别级别：可以增加子类别、修改、删除 -->
                    <template v-if="scope.row.deptsId">
                        <el-button
                                icon="el-icon-plus"
                                size="mini"
                                type="primary"
                                @click="handleAddSubcategory(scope.row)">增加维修子类别
                        </el-button>
                        <el-button
                                icon="el-icon-edit"
                                size="mini"
                                @click="handleUpdateCategory(scope.row)">修改
                        </el-button>
                        <el-button
                                icon="el-icon-delete"
                                size="mini"
                                type="danger"
                                @click="handleDeleteCategory(scope.row)">删除
                        </el-button>
                    </template>

                    <!-- 维修子类别级别：可以修改、删除 -->
                    <template v-else-if="scope.row.classesId">
                        <el-button
                                icon="el-icon-edit"
                                size="mini"
                                @click="handleUpdateSubcategory(scope.row)">修改
                        </el-button>
                        <el-button
                                icon="el-icon-delete"
                                size="mini"
                                type="danger"
                                @click="handleDeleteSubcategory(scope.row)">删除
                        </el-button>
                    </template>
                </template>
            </el-table-column>
        </el-table>

        <!-- 抽屉式表单 -->
        <el-drawer
            :title="drawerTitle"
            :visible.sync="drawerVisible"
            direction="rtl"
            size="400px"
            :before-close="handleDrawerClose">

            <div style="padding: 20px;">
                <el-form ref="form" :model="form" :rules="rules" label-width="80px">
                    <!-- 类别表单 -->
                    <template v-if="formType === 'category'">
                        <el-form-item label="所属角色" prop="roleId">
                            <el-select v-model="form.roleId" placeholder="请选择角色" style="width: 100%;" :disabled="isEdit">
                                <el-option
                                    v-for="role in roleOptions"
                                    :key="role.id"
                                    :label="role.name"
                                    :value="role.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="维修类别名称" prop="name">
                            <el-input v-model="form.name" placeholder="请输入维修类别名称" />
                        </el-form-item>
                    </template>

                    <!-- 子类别表单 -->
                    <template v-if="formType === 'subcategory'">
                        <el-form-item label="所属角色">
                            <el-input :value="parentRoleName" disabled />
                        </el-form-item>
                        <el-form-item label="所属维修类别">
                            <el-input :value="parentCategoryName" disabled />
                        </el-form-item>
                        <el-form-item label="维修子类别名称" prop="name">
                            <el-input v-model="form.name" placeholder="请输入维修子类别名称" />
                        </el-form-item>
                    </template>
                </el-form>

                <div class="drawer-footer">
                    <el-button @click="handleDrawerClose">取 消</el-button>
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                </div>
            </div>
        </el-drawer>
    </div>
</template>

<script>
    export default {
        name: "Role",
        data() {
            return {
                roles: [],
                roleOptions: [], // 角色选项列表

                // 抽屉相关
                drawerVisible: false,
                drawerTitle: '',
                formType: '', // 'category' 或 'subcategory'
                isEdit: false, // 是否为编辑模式

                // 表单参数
                form: {
                    id: null,
                    name: '',
                    roleId: null,
                    deptId: null
                },

                // 父级信息（用于子类别表单显示）
                parentRoleName: '',
                parentCategoryName: '',
                parentCategoryId: null,
                parentRoleId: null,

                // 表单校验
                rules: {
                    name: [
                        { required: true, message: '请输入名称', trigger: 'blur' }
                    ],
                    roleId: [
                        { required: true, message: '请选择角色', trigger: 'change' }
                    ]
                },
            }
        },
        methods: {
            guid2() {
                function S4() {
                    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
                }
                return (S4() + S4() + "-" + S4() + "-" + S4() + "-" + S4() + "-" + S4() + S4() + S4());
            },

            //-------新的方法---------

            // 添加类别
            handleAddCategory() {
                this.resetForm();
                this.formType = 'category';
                this.isEdit = false;
                this.drawerTitle = '添加维修类别';
                this.drawerVisible = true;
            },

            // 添加子类别
            handleAddSubcategory(categoryRow) {
                this.resetForm();
                this.formType = 'subcategory';
                this.isEdit = false;
                this.drawerTitle = '添加维修子类别';

                // 设置父级信息
                this.parentCategoryId = categoryRow.deptsId;
                this.parentCategoryName = categoryRow.deptname;
                this.parentRoleId = categoryRow.roleId;
                this.parentRoleName = categoryRow.roleName;

                this.drawerVisible = true;
            },

            // 修改类别
            handleUpdateCategory(categoryRow) {
                this.formType = 'category';
                this.isEdit = true;
                this.drawerTitle = '修改维修类别';

                this.form = {
                    id: categoryRow.deptsId,
                    name: categoryRow.deptname,
                    roleId: categoryRow.roleId
                };

                this.drawerVisible = true;
            },

            // 修改子类别
            handleUpdateSubcategory(subcategoryRow) {
                this.formType = 'subcategory';
                this.isEdit = true;
                this.drawerTitle = '修改维修子类别';

                // 从父级数据中获取信息
                const parentCategory = this.findParentCategory(subcategoryRow.classesId);
                if (parentCategory) {
                    this.parentCategoryId = parentCategory.deptsId;
                    this.parentCategoryName = parentCategory.deptname;
                    this.parentRoleId = parentCategory.roleId;
                    this.parentRoleName = parentCategory.roleName;
                }

                this.form = {
                    id: subcategoryRow.classesId,
                    name: subcategoryRow.classesname
                };

                this.drawerVisible = true;
            },

            // 删除类别
            handleDeleteCategory(categoryRow) {
                this.$confirm('确认删除该维修类别吗？删除后其下的所有维修子类别也将被删除。', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$del("/dept/" + categoryRow.deptsId)
                        .then((res) => {
                            this.$message.success("删除成功");
                            this.newList();
                        })
                        .catch(error => {
                            this.$message.error("删除失败：" + error.message);
                        });
                });
            },

            // 删除子类别
            handleDeleteSubcategory(subcategoryRow) {
                this.$confirm('确认删除该维修子类别吗？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$del("/class/" + subcategoryRow.classesId)
                        .then((res) => {
                            this.$message.success("删除成功");
                            this.newList();
                        })
                        .catch(error => {
                            this.$message.error("删除失败：" + error.message);
                        });
                });
            },

            // 提交表单
            submitForm() {
                this.$refs["form"].validate(valid => {
                    if (valid) {
                        if (this.formType === 'category') {
                            this.submitCategory();
                        } else if (this.formType === 'subcategory') {
                            this.submitSubcategory();
                        }
                    }
                });
            },

            // 提交类别
            submitCategory() {
                if (this.isEdit) {
                    // 修改类别
                    this.$put("/dept", {
                        id: this.form.id,
                        name: this.form.name,
                        roleId: this.form.roleId
                    }).then(() => {
                        this.$message.success("修改维修类别成功");
                        this.drawerVisible = false;
                        this.newList();
                    }).catch(error => {
                        this.$message.error("修改失败：" + error.message);
                    });
                } else {
                    // 添加类别
                    this.$post("/dept", {
                        name: this.form.name,
                        roleId: this.form.roleId
                    }).then(() => {
                        this.$message.success("添加维修类别成功");
                        this.drawerVisible = false;
                        this.newList();
                    }).catch(error => {
                        this.$message.error("添加失败：" + error.message);
                    });
                }
            },

            // 提交子类别
            submitSubcategory() {
                if (this.isEdit) {
                    // 修改子类别
                    this.$put("/class", {
                        id: this.form.id,
                        name: this.form.name,
                        roleId: this.parentRoleId,
                        deptId: this.parentCategoryId
                    }).then(() => {
                        this.$message.success("修改维修子类别成功");
                        this.drawerVisible = false;
                        this.newList();
                    }).catch(error => {
                        this.$message.error("修改失败：" + error.message);
                    });
                } else {
                    // 添加子类别
                    this.$post("/class", {
                        name: this.form.name,
                        roleId: this.parentRoleId,
                        deptId: this.parentCategoryId
                    }).then(() => {
                        this.$message.success("添加维修子类别成功");
                        this.drawerVisible = false;
                        this.newList();
                    }).catch(error => {
                        this.$message.error("添加失败：" + error.message);
                    });
                }
            },

            // 关闭抽屉
            handleDrawerClose() {
                this.drawerVisible = false;
                this.resetForm();
            },

            // 获取数据列表
            newList(){
                this.$get('/role')
                .then(res => {
                    if (res.data && res.data.role) {
                        this.roles = res.data.role;
                        // 提取角色选项
                        this.roleOptions = res.data.role.map(role => ({
                            id: role.id,
                            name: role.name
                        }));
                    } else {
                        this.roles = [];
                        this.roleOptions = [];
                        console.warn('获取角色数据失败，返回数据格式不正确');
                    }
                })
                .catch(error => {
                    console.error('获取角色列表失败:', error);
                    this.roles = [];
                    this.roleOptions = [];
                    this.$message.error('获取角色列表失败，请稍后重试');
                });
            },

            // 表单重置
            resetForm() {
                this.form = {
                    id: null,
                    name: '',
                    roleId: null,
                    deptId: null
                };
                this.parentRoleName = '';
                this.parentCategoryName = '';
                this.parentCategoryId = null;
                this.parentRoleId = null;

                if (this.$refs.form) {
                    this.$refs.form.resetFields();
                }
            },



            // 辅助方法：根据子类别ID查找父级维修类别
            findParentCategory(classId) {
                for (let dept of this.dataList) {
                    if (dept.children) {
                        for (let child of dept.children) {
                            if (child.classesId === classId) {
                                return dept;
                            }
                        }
                    }
                }
                return null;
            },
        },

        created() {
            this.newList();
        },

        computed:{
            dataList(){
                if (!this.roles || !Array.isArray(this.roles)) {
                    return [];
                }

                let allDepts = [];

                // 遍历所有角色，收集所有的维修类别
                this.roles.forEach(role => {
                    if (!role || !role.depts) return;

                    role.depts.forEach(dept => {
                        if (!dept) return;

                        let children = [];
                        const deptClasses = dept['classes'] || [];

                        // 添加子类别
                        deptClasses.forEach(classItem => {
                            if (!classItem) return;

                            children.push({
                                classesId: classItem.id,
                                classesname: classItem.name || '',
                                uuid: this.guid2()
                            });
                        });

                        // 添加维修类别
                        allDepts.push({
                            deptsId: dept.id,
                            deptname: dept.name || '',
                            roleId: role.id, // 保留角色ID用于操作
                            roleName: role.name, // 保留角色名称用于表单显示
                            children: children,
                            uuid: this.guid2()
                        });
                    });
                });

                return allDepts;
            }
        }
    }
</script>

<style scoped lang="less">
    .content {
        padding: 0 1%;
    }

    .top-actions {
        text-align: left;
        margin-bottom: 20px;
    }

    .drawer-footer {
        text-align: right;
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #e8e8e8;
    }

    .drawer-footer .el-button {
        margin-left: 10px;
    }

    // 操作按钮样式优化
    .el-table .el-button--mini {
        margin-right: 5px;
        margin-bottom: 5px;
    }

    // 角色不可操作提示样式
    .el-table .role-no-operation {
        color: #909399;
        font-size: 12px;
        font-style: italic;
    }
</style>
