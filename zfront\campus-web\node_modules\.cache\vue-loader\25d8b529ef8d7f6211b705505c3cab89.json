{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\Task.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\admin\\children\\Task.vue", "mtime": 1748677040096}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7Zm9ybWF0RGF0ZX0gZnJvbSAnQC91dGlsL2RhdGUnOwpleHBvcnQgZGVmYXVsdCB7CiAgICBuYW1lOiAiVGFzayIsCiAgICBtZXRob2RzOiB7CiAgICAgICAgaW5xdWlyeSgpewogICAgICAgICAgICBpZiAodGhpcy5zZWxlY3QgPT0gJ2VudGlyZScpewogICAgICAgICAgICAgICAgdGhpcy5uZXdMaXN0KCk7CiAgICAgICAgICAgIH1lbHNlIHsKICAgICAgICAgICAgICAgIHRoaXMuJHBvc3QoIi90YXNrL2FwaS9maW5kVGFza0J5Um9sZUlkIix7ImlkIjp0aGlzLnNlbGVjdH0pCiAgICAgICAgICAgICAgICAudGhlbihyZXMgPT4gewogICAgICAgICAgICAgICAgICAgIHRoaXMudGFza3MgPSByZXMuZGF0YS50YXNrcwogICAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgfQogICAgICAgIH0sCiAgICAgICAgY2xpY2tTZWFyY2goKSB7CiAgICAgICAgICAgIGlmKHRoaXMuaW5wdXQgPT0gJ+W3suWujOaIkCcpewoKICAgICAgICAgICAgfQogICAgICAgIH0sCgogICAgICAgIGdldFN0YXRlVHlwZShzdGF0ZSkgewogICAgICAgICAgICBjb25zdCB0eXBlcyA9IHsKICAgICAgICAgICAgICAgIDA6ICdpbmZvJywgICAgLy8g5b6F5o6l5Y2VCiAgICAgICAgICAgICAgICAxOiAnd2FybmluZycsIC8vIOi/m+ihjOS4rQogICAgICAgICAgICAgICAgMjogJ3N1Y2Nlc3MnICAvLyDlt7LlrozmiJAKICAgICAgICAgICAgfQogICAgICAgICAgICByZXR1cm4gdHlwZXNbc3RhdGVdIHx8ICdpbmZvJwogICAgICAgIH0sCiAgICAgICAgZ2V0U3RhdGVUZXh0KHN0YXRlKSB7CiAgICAgICAgICAgIGNvbnN0IHRleHRzID0gewogICAgICAgICAgICAgICAgMDogJ+W+heaOpeWNlScsCiAgICAgICAgICAgICAgICAxOiAn6L+b6KGM5LitJywKICAgICAgICAgICAgICAgIDI6ICflt7LlrozmiJAnCiAgICAgICAgICAgIH0KICAgICAgICAgICAgcmV0dXJuIHRleHRzW3N0YXRlXSB8fCAn5pyq55+lJwogICAgICAgIH0sCiAgICAgICAgdHJhbnNmb3JtKHRpbWUpIHsKICAgICAgICAgICAgbGV0IGRhdGUgPSBuZXcgRGF0ZSh0aW1lKTsKICAgICAgICAgICAgcmV0dXJuIGZvcm1hdERhdGUoZGF0ZSwgJ3l5eXktTU0tZGQgaGg6bW0nKTsKICAgICAgICB9LAogICAgICAgIGhhbmRsZURlbGV0ZShyb3cpIHsKICAgICAgICAgICAgdGhpcy4kY29uZmlybSgn56Gu6K6k5Yig6Zmk6K+l5Lu75YqhPycsICfmj5DnpLonLCB7CiAgICAgICAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICAgICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICAgICAgICAgIHRoaXMuJGRlbChgL3Rhc2svJHtyb3cuaWR9YCkudGhlbihyZXMgPT4gewogICAgICAgICAgICAgICAgICAgIGlmIChyZXMuZGF0YS5zdGF0dXMpIHsKICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKTsKICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5uZXdMaXN0KCk7CiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMuZGF0YS5tc2cgfHwgJ+WIoOmZpOWksei0pScpOwogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgIH0pLmNhdGNoKCgpID0+IHt9KQogICAgICAgIH0sCiAgICAgICAgaGFuZGxlVG9wVG9nZ2xlKHJvdykgewogICAgICAgICAgICBjb25zdCBhY3Rpb24gPSByb3cuaXNUb3AgPyAnY2FuY2VsVG9wJyA6ICd0b3AnOwogICAgICAgICAgICBjb25zdCBtZXNzYWdlID0gcm93LmlzVG9wID8gJ+WPlua2iOe9rumhticgOiAn572u6aG2JzsKCiAgICAgICAgICAgIHRoaXMuJHB1dChgL3Rhc2svJHthY3Rpb259LyR7cm93LmlkfWApLnRoZW4ocmVzID0+IHsKICAgICAgICAgICAgICAgIGlmIChyZXMuZGF0YS5zdGF0dXMpIHsKICAgICAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoYCR7bWVzc2FnZX3miJDlip9gKTsKICAgICAgICAgICAgICAgICAgICByb3cuaXNUb3AgPSAhcm93LmlzVG9wOwogICAgICAgICAgICAgICAgICAgIHRoaXMubmV3TGlzdCgpOwogICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5kYXRhLm1zZyB8fCBgJHttZXNzYWdlfeWksei0pWApOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICB9KS5jYXRjaChlcnIgPT4gewogICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcihgJHttZXNzYWdlfeWksei0pTpgLCBlcnIpOwogICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihgJHttZXNzYWdlfeWksei0pe+8jOivt+eojeWQjumHjeivlWApOwogICAgICAgICAgICB9KTsKICAgICAgICB9LAogICAgICAgIG5ld0xpc3QoKSB7CiAgICAgICAgICAgIHRoaXMuJGdldCgiL3Rhc2siKQogICAgICAgICAgICAudGhlbigocnMpID0+IHsKICAgICAgICAgICAgICAgIGlmIChycy5kYXRhLnN0YXR1cykgewogICAgICAgICAgICAgICAgICAgIHRoaXMudGFza3MgPSBycy5kYXRhLnRhc2suc29ydCgoYSwgYikgPT4gewogICAgICAgICAgICAgICAgICAgICAgICBpZiAoYS5pc1RvcCAmJiAhYi5pc1RvcCkgcmV0dXJuIC0xOwogICAgICAgICAgICAgICAgICAgICAgICBpZiAoIWEuaXNUb3AgJiYgYi5pc1RvcCkgcmV0dXJuIDE7CiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBuZXcgRGF0ZShiLmNyZWF0ZVRpbWUpIC0gbmV3IERhdGUoYS5jcmVhdGVUaW1lKTsKICAgICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihycy5kYXRhLm1zZyB8fCAn6I635Y+W5Lu75Yqh5YiX6KGo5aSx6LSlJyk7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0pCiAgICAgICAgICAgIC5jYXRjaChlcnIgPT4gewogICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W5Lu75Yqh5YiX6KGo5aSx6LSlOicsIGVycik7CiAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfojrflj5bku7vliqHliJfooajlpLHotKXvvIzor7fnqI3lkI7ph43or5UnKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgfQogICAgfSwKICAgIGRhdGEoKSB7CiAgICAgICAgcmV0dXJuIHsKICAgICAgICAgICAgLy/lrabmoKEKICAgICAgICAgICAgcm9sZXM6W10sCiAgICAgICAgICAgIHRhc2tzOiBbXSwKICAgICAgICAgICAgcnVsZUZvcm06IHsKICAgICAgICAgICAgICAgIGlkOiAwLAogICAgICAgICAgICAgICAgc3RhdGU6IHRydWUKICAgICAgICAgICAgfSwKICAgICAgICAgICAgb3B0aW9uczogWwogICAgICAgICAgICAgICAge2xhYmVsOiAi5q2j5bi4IiwgdmFsdWU6ICIwIn0sCiAgICAgICAgICAgICAgICB7bGFiZWw6ICLnpoHnlKgiLCB2YWx1ZTogIjEifQoKICAgICAgICAgICAgXSwKICAgICAgICAgICAgaW5wdXQ6IiIsCiAgICAgICAgICAgIHNlbGVjdDogJ2VudGlyZScKICAgICAgICB9CiAgICB9LAogICAgY3JlYXRlZCgpIHsKICAgICAgICB0aGlzLm5ld0xpc3QoKTsKICAgICAgICB0aGlzLiRnZXQoInJvbGUiKQogICAgICAgIC50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgIHRoaXMucm9sZXMgPSByZXMuZGF0YS5yb2xlCiAgICAgICAgfSkKICAgIH0sCgogICAgZmlsdGVyczogewogICAgICAgIGZvcm1hdERhdGUodGltZSkgewogICAgICAgICAgICBsZXQgZGF0ZSA9IG5ldyBEYXRlKHRpbWUpOwogICAgICAgICAgICByZXR1cm4gZm9ybWF0RGF0ZShkYXRlLCAneXl5eS1NTS1kZCBoaDptbScpOwogICAgICAgIH0KICAgIH0KCn0K"}, {"version": 3, "sources": ["Task.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA", "file": "Task.vue", "sourceRoot": "src/views/admin/children", "sourcesContent": ["<template>\n    <div class=\"content\">\n<!--        搜索栏-->\n        <!-- <div class=\"center\">\n            <el-input placeholder=\"请输入内容\" v-model=\"input\" class=\"input-with-select\" @keydown.enter.native=\"clickSearch\">\n                <el-select v-model=\"select\" slot=\"prepend\" placeholder=\"请选择\" value=\"1\" @change=\"inquiry\">\n                    <el-option value=\"entire\" label=\"全部\"></el-option>\n                    <el-option :value=\"item.id\" v-for=\"item in roles\" :label=\"item.name\"></el-option>\n                </el-select>\n                <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"clickSearch\"></el-button>\n            </el-input>\n        </div> -->\n        <div class=\"bottom\">\n            <el-table\n                    :data=\"tasks\"\n                    :resizable=\"false\"\n                    style=\"width: 100%  \">\n                <el-table-column\n                        prop=\"publish.username\"\n                        label=\"发布人\"\n                        min-width=\"140\">\n                </el-table-column>\n                <el-table-column\n                        label=\"维修员\"\n                        min-width=\"140\">\n                    <template slot-scope=\"scope\">\n                        {{scope.row.accept ? scope.row.accept.username : '暂无服务'}}\n                    </template>\n                </el-table-column>\n                <el-table-column\n                        prop=\"reward\"\n                        label=\"任务金额\"\n                        min-width=\"110\">\n                </el-table-column>\n                <el-table-column\n                        label=\"任务所在类别\"\n                        min-width=\"120\">\n                    <template slot-scope=\"scope\">\n                        {{scope.row.dept ? scope.row.dept.name : '未分类'}}\n                    </template>\n                </el-table-column>\n                <el-table-column\n                        label=\"任务子类别\"\n                        min-width=\"120\">\n                    <template slot-scope=\"scope\">\n                        {{scope.row.type ? scope.row.type.name : '未分类'}}\n                    </template>\n                </el-table-column>\n                <el-table-column\n                        label=\"地址\"\n                        min-width=\"180\">\n                    <template slot-scope=\"scope\">\n                        <span v-if=\"scope.row.province\">\n                            {{scope.row.province}} {{scope.row.city}} {{scope.row.district}}\n                        </span>\n                        <span v-else>未设置</span>\n                    </template>\n                </el-table-column>\n                <el-table-column\n                        label=\"详细地址\"\n                        min-width=\"180\">\n                    <template slot-scope=\"scope\">\n                        {{scope.row.address || '未设置'}}\n                    </template>\n                </el-table-column>\n                <el-table-column\n                        prop=\"taskTitle\"\n                        label=\"标题\"\n                        min-width=\"150\">\n                </el-table-column>\n                <el-table-column\n                        label=\"发布时间\"\n                        min-width=\"140\">\n                    <template slot-scope=\"scope\">\n                        {{scope.row.createTime ? transform(scope.row.createTime) : '暂无时间'}}\n                    </template>\n                </el-table-column>\n                <el-table-column\n                        label=\"服务时间\"\n                        min-width=\"140\">\n                    <template slot-scope=\"scope\">\n                        {{scope.row.orderTime ? transform(scope.row.orderTime) : '暂无时间'}}\n                    </template>\n                </el-table-column>\n                <el-table-column\n                        prop=\"balance\"\n                        label=\"完成时间\"\n                        min-width=\"140\">\n                    <template slot-scope=\"scope\">\n                        {{scope.row.endTime ? transform(scope.row.endTime) : '暂无时间'}}\n                    </template>\n                </el-table-column>\n                <el-table-column\n                        prop=\"state\"\n                        label=\"任务状态\"\n                        min-width=\"90\">\n                    <template slot-scope=\"scope\">\n                        <el-tag :type=\"getStateType(scope.row.state)\">\n                            {{ getStateText(scope.row.state) }}\n                        </el-tag>\n                    </template>\n                </el-table-column>\n                <el-table-column\n                        label=\"操作\"\n                        width=\"180\">\n                    <template slot-scope=\"scope\">\n                        <el-button-group>\n                            <!-- <el-button\n                                    size=\"mini\"\n                                    :type=\"scope.row.isTop ? 'warning' : 'success'\"\n                                    @click=\"handleTopToggle(scope.row)\">\n                                {{ scope.row.isTop ? '取消置顶' : '置顶' }}\n                            </el-button> -->\n                            <el-button\n                                    size=\"mini\"\n                                    type=\"danger\"\n                                    @click=\"handleDelete(scope.row)\">\n                                删除\n                            </el-button>\n                        </el-button-group>\n                    </template>\n                </el-table-column>\n            </el-table>\n\n        </div>\n    </div>\n</template>\n\n<script>\n    import {formatDate} from '@/util/date';\n    export default {\n        name: \"Task\",\n        methods: {\n            inquiry(){\n                if (this.select == 'entire'){\n                    this.newList();\n                }else {\n                    this.$post(\"/task/api/findTaskByRoleId\",{\"id\":this.select})\n                    .then(res => {\n                        this.tasks = res.data.tasks\n                    })\n                }\n            },\n            clickSearch() {\n                if(this.input == '已完成'){\n\n                }\n            },\n\n            getStateType(state) {\n                const types = {\n                    0: 'info',    // 待接单\n                    1: 'warning', // 进行中\n                    2: 'success'  // 已完成\n                }\n                return types[state] || 'info'\n            },\n            getStateText(state) {\n                const texts = {\n                    0: '待接单',\n                    1: '进行中',\n                    2: '已完成'\n                }\n                return texts[state] || '未知'\n            },\n            transform(time) {\n                let date = new Date(time);\n                return formatDate(date, 'yyyy-MM-dd hh:mm');\n            },\n            handleDelete(row) {\n                this.$confirm('确认删除该任务?', '提示', {\n                    confirmButtonText: '确定',\n                    cancelButtonText: '取消',\n                    type: 'warning'\n                }).then(() => {\n                    this.$del(`/task/${row.id}`).then(res => {\n                        if (res.data.status) {\n                            this.$message.success('删除成功');\n                            this.newList();\n                        } else {\n                            this.$message.error(res.data.msg || '删除失败');\n                        }\n                    })\n                }).catch(() => {})\n            },\n            handleTopToggle(row) {\n                const action = row.isTop ? 'cancelTop' : 'top';\n                const message = row.isTop ? '取消置顶' : '置顶';\n\n                this.$put(`/task/${action}/${row.id}`).then(res => {\n                    if (res.data.status) {\n                        this.$message.success(`${message}成功`);\n                        row.isTop = !row.isTop;\n                        this.newList();\n                    } else {\n                        this.$message.error(res.data.msg || `${message}失败`);\n                    }\n                }).catch(err => {\n                    console.error(`${message}失败:`, err);\n                    this.$message.error(`${message}失败，请稍后重试`);\n                });\n            },\n            newList() {\n                this.$get(\"/task\")\n                .then((rs) => {\n                    if (rs.data.status) {\n                        this.tasks = rs.data.task.sort((a, b) => {\n                            if (a.isTop && !b.isTop) return -1;\n                            if (!a.isTop && b.isTop) return 1;\n                            return new Date(b.createTime) - new Date(a.createTime);\n                        });\n                    } else {\n                        this.$message.error(rs.data.msg || '获取任务列表失败');\n                    }\n                })\n                .catch(err => {\n                    console.error('获取任务列表失败:', err);\n                    this.$message.error('获取任务列表失败，请稍后重试');\n                });\n            }\n        },\n        data() {\n            return {\n                //学校\n                roles:[],\n                tasks: [],\n                ruleForm: {\n                    id: 0,\n                    state: true\n                },\n                options: [\n                    {label: \"正常\", value: \"0\"},\n                    {label: \"禁用\", value: \"1\"}\n\n                ],\n                input:\"\",\n                select: 'entire'\n            }\n        },\n        created() {\n            this.newList();\n            this.$get(\"role\")\n            .then(res => {\n                this.roles = res.data.role\n            })\n        },\n\n        filters: {\n            formatDate(time) {\n                let date = new Date(time);\n                return formatDate(date, 'yyyy-MM-dd hh:mm');\n            }\n        }\n\n    }\n</script>\n\n<style scoped lang=\"less\">\n    .content {\n        padding: 0 1%;\n\n    }\n\n    .center {\n        width: 80%;\n        margin-bottom: 30px;\n    }\n\n    /deep/ .el-select .el-input {\n        width: 200px;\n    }\n\n    /deep/ .input-with-select .el-input-group__prepend {\n        background-color: #fff;\n    }\n\n\n\n    .form {\n        margin: 0 22px;\n    }\n\n    .el-button-group {\n        .el-button {\n            margin-left: 0;\n            margin-right: 0;\n\n            &:first-child {\n                border-right: 1px solid rgba(255, 255, 255, 0.5);\n            }\n        }\n    }\n</style>"]}]}