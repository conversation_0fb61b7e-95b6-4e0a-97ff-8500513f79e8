{"remainingRequest": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\ending\\250426\\zfront\\campus-web\\src\\views\\user\\children\\Published.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\ending\\250426\\zfront\\campus-web\\src\\views\\user\\children\\Published.vue", "mtime": 1748711704364}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1737774013978}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1737774013990}, {"path": "D:\\ending\\250426\\zfront\\campus-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1737774014071}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7bWFwU3RhdGV9IGZyb20gInZ1ZXgiCmltcG9ydCB7Zm9ybWF0RGF0ZX0gZnJvbSAnQC91dGlsL2RhdGUnOwppbXBvcnQgeyBhZGRSZW1hcmssIH0gZnJvbSAiQC9hcGkvcmVtYXJrL3JlbWFyayI7CgpleHBvcnQgZGVmYXVsdCB7CiAgICBuYW1lOiAiUHVibGlzaGVkIiwKICAgIGRhdGEoKSB7CiAgICAgICAgcmV0dXJuIHsKICAgICAgICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCCiAgICAgICAgICAgIG9wZW46IGZhbHNlLAogICAgICAgICAgICBhY3RpdmVOYW1lczogWycxJywgJzInLCAnMycsICc0JywgJzUnLCAnNicsICc3J10sCiAgICAgICAgICAgIGRyYXdlck5hbWVzOiBbJzEnLCAnMicsICczJywgJzQnLCAnNSddLAogICAgICAgICAgICB0YXNrczogW10sCiAgICAgICAgICAgIGRyYXdlcjogZmFsc2UsCiAgICAgICAgICAgIHJlY2lwaWVudEluZm9ybWF0aW9uOiBbXSwKICAgICAgICAgICAgLy8g5b2T5YmN6YCJ5Lit55qE54q25oCBCiAgICAgICAgICAgIGFjdGl2ZVN0YXR1czogJy0xJywKICAgICAgICAgICAgLy8g6KGo5Y2V5Y+C5pWwCiAgICAgICAgICAgIGZvcm06IHt9LAogICAgICAgICAgICAvLyDooajljZXmoKHpqowKICAgICAgICAgICAgcnVsZXM6IHsKICAgICAgICAgICAgfSwKICAgICAgICAgICAgLy8g5by55Ye65bGC5qCH6aKYCiAgICAgICAgICAgIHRpdGxlOiAiIiwKICAgICAgICAgICAgLy8g5b2T5YmN6YCJ5Lit55qE5Lu75YqhCiAgICAgICAgICAgIGN1cnJlbnRUYXNrOiBudWxsLAogICAgICAgIH07CiAgICB9LAogICAgY29tcHV0ZWQ6IHsKICAgICAgICAuLi5tYXBTdGF0ZSgndXNlcicsIFsndXNlciddKSwKCiAgICAgICAgLy8g5oyJ54q25oCB5YiG57uE55qE5Lu75YqhCiAgICAgICAgdGFza3NCeVN0YXR1cygpIHsKICAgICAgICAgICAgLy8g5a6a5LmJ54q25oCB5pig5bCECiAgICAgICAgICAgIGNvbnN0IHN0YXR1c01hcCA9IHsKICAgICAgICAgICAgICAgICctMSc6IHsgbmFtZTogJ+WFqOmDqCcsIHRhc2tzOiBbXSB9LAogICAgICAgICAgICAgICAgJzAnOiB7IG5hbWU6ICflvoXmjqXljZUnLCB0YXNrczogW10gfSwKICAgICAgICAgICAgICAgICcxJzogeyBuYW1lOiAn5pyN5Yqh5LitJywgdGFza3M6IFtdIH0sCiAgICAgICAgICAgICAgICAnMic6IHsgbmFtZTogJ+W3suWujOaIkCcsIHRhc2tzOiBbXSB9CiAgICAgICAgICAgIH07CgogICAgICAgICAgICAvLyDmt7vliqAi5YWo6YOoIuWIhuexuwogICAgICAgICAgICBzdGF0dXNNYXBbJy0xJ10udGFza3MgPSB0aGlzLnRhc2tzOwoKICAgICAgICAgICAgLy8g5oyJ54q25oCB5YiG57uECiAgICAgICAgICAgIHRoaXMudGFza3MuZm9yRWFjaCh0YXNrID0+IHsKICAgICAgICAgICAgICAgIGNvbnN0IHN0YXRlID0gdGFzay5zdGF0ZSAhPT0gbnVsbCAmJiB0YXNrLnN0YXRlICE9PSB1bmRlZmluZWQgPyB0YXNrLnN0YXRlLnRvU3RyaW5nKCkgOiAnMCc7CiAgICAgICAgICAgICAgICBpZiAoc3RhdHVzTWFwW3N0YXRlXSkgewogICAgICAgICAgICAgICAgICAgIHN0YXR1c01hcFtzdGF0ZV0udGFza3MucHVzaCh0YXNrKTsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSk7CgogICAgICAgICAgICAvLyDovazmjaLkuLrmlbDnu4TmoLzlvI/vvIzmlrnkvr/lnKjmqKHmnb/kuK3kvb/nlKgKICAgICAgICAgICAgcmV0dXJuIE9iamVjdC5lbnRyaWVzKHN0YXR1c01hcCkubWFwKChbc3RhdHVzLCBkYXRhXSkgPT4gKHsKICAgICAgICAgICAgICAgIHN0YXR1czogcGFyc2VJbnQoc3RhdHVzKSwKICAgICAgICAgICAgICAgIHN0YXR1c05hbWU6IGRhdGEubmFtZSwKICAgICAgICAgICAgICAgIHRhc2tzOiBkYXRhLnRhc2tzCiAgICAgICAgICAgIH0pKTsKICAgICAgICB9CiAgICB9LAogICAgY3JlYXRlZCgpIHsKICAgICAgICB0aGlzLnJldHJpZXZlRGF0YSgpCiAgICB9LAogICAgbWV0aG9kczogewogICAgICAgIHJldHJpZXZlRGF0YSgpIHsKICAgICAgICAgICAgdGhpcy4kZ2V0KCIvdGFzay9wdWJsaXNoZWQiLCB7aWQ6IHRoaXMudXNlci5pZH0pLnRoZW4ocmVzID0+IHsKICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKHJlcy5kYXRhLnRhc2spCiAgICAgICAgICAgICAgICB0aGlzLnRhc2tzID0gcmVzLmRhdGEudGFzawogICAgICAgICAgICB9KQogICAgICAgIH0sCiAgICAgICAgcmVjZWl2ZXIodmFsKSB7CiAgICAgICAgICAgIGNvbnNvbGUubG9nKHZhbCkKICAgICAgICAgICAgdGhpcy5yZWNpcGllbnRJbmZvcm1hdGlvbiA9IHZhbC5hY2NlcHQ7CiAgICAgICAgICAgIC8vIGNvbnNvbGUubG9nKHRoaXMucmVjaXBpZW50SW5mb3JtYXRpb24pCiAgICAgICAgICAgIHRoaXMuZHJhd2VyID0gdHJ1ZQogICAgICAgIH0sCgogICAgICAgIHRyYW5zZm9ybSh0aW1lKSB7CiAgICAgICAgICAgIGxldCBkYXRlID0gbmV3IERhdGUodGltZSk7CiAgICAgICAgICAgIHJldHVybiBmb3JtYXREYXRlKGRhdGUsICd5eXl5LU1NLWRkIGhoOm1tJyk7CiAgICAgICAgfSwKCiAgICAgICAgY2FuY2VsKGlkKSB7CiAgICAgICAgICAgIHRoaXMuJGRlbCgiL3Rhc2svIiArIGlkKQogICAgICAgICAgICAgICAgLnRoZW4ocmVzID0+IHsKICAgICAgICAgICAgICAgICAgICB0aGlzLnJldHJpZXZlRGF0YSgpCiAgICAgICAgICAgICAgICAgICAgdGhpcy4kbm90aWZ5TXNnKCfmiJDlip8nLCByZXMuZGF0YS5tc2csICJzdWNjZXNzIik7CiAgICAgICAgICAgICAgICB9KQogICAgICAgIH0sCiAgICAgICAgY29tcGxldGVUYXNrKGlkKSB7CiAgICAgICAgICAgIHRoaXMuJG1zZ2JveCh7CiAgICAgICAgICAgICAgICB0aXRsZTogJ+aPkOekuicsCiAgICAgICAgICAgICAgICBtZXNzYWdlOiAn56Gu5a6a6K+l57u05L+u5ZGY5a6M5oiQ5q2k5Lu75Yqh5LqG5ZCX77yfJywKICAgICAgICAgICAgICAgIHNob3dDYW5jZWxCdXR0b246IHRydWUsCiAgICAgICAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICAgICAgICAgIGJlZm9yZUNsb3NlOiAoKGFjdGlvbiwgaW5zdGFuY2UsIGRvbmUpID0+IHsKICAgICAgICAgICAgICAgICAgICBpZiAoYWN0aW9uID09ICdjb25maXJtJykgewogICAgICAgICAgICAgICAgICAgICAgICAvLyBpbnN0YW5jZS5jb25maXJtQnV0dG9uTG9hZGluZyA9IHRydWU7CiAgICAgICAgICAgICAgICAgICAgICAgIGluc3RhbmNlLmNvbmZpcm1CdXR0b25UZXh0ID0gJ+aJp+ihjOS4rS4uLic7CiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJHB1dCgndGFzay8nICsgaWQpCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAudGhlbigocmVzKSA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZG9uZSgpOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGluc3RhbmNlLmNvbmZpcm1CdXR0b25Mb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbXNnKHJlcy5kYXRhLm1zZywgInN1Y2Nlc3MiKTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnJldHJpZXZlRGF0YSgpOwoKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyDlpoLmnpzov5Tlm57kuobmm7TmlrDlkI7nmoTnlKjmiLfkv6Hmga/vvIzmm7TmlrDlvZPliY3nlKjmiLfmlbDmja4KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAocmVzLmRhdGEuZGF0YSAmJiByZXMuZGF0YS5kYXRhLmlkKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIOajgOafpei/lOWbnueahOeUqOaIt+aYr+WQpuaYr+W9k+WJjeeZu+W9leeUqOaItwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAocmVzLmRhdGEuZGF0YS5pZCA9PT0gdGhpcy51c2VyLmlkKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn5pu05paw5b2T5YmN55So5oi35L2Z6aKd5L+h5oGvOicsIHJlcy5kYXRhLmRhdGEpOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kc3RvcmUuY29tbWl0KCd1c2VyL3NldFVzZXInLCByZXMuZGF0YS5kYXRhKTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgICAgICAgZG9uZSgpOwogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICAgICAgfSkKICAgICAgICB9LAogICAgICAgIC8vIOivhOS7t+eUqOaItwogICAgICAgIHJlbWFyayh0YXNrKXsKICAgICAgICAgICAgdGhpcy5jdXJyZW50VGFzayA9IHRhc2s7CiAgICAgICAgICAgIHRoaXMub3BlbiA9IHRydWU7CiAgICAgICAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg6K+E5Lu3IjsKICAgICAgICB9LAoKICAgICAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovCiAgICAgICAgc3VibWl0Rm9ybSgpIHsKICAgICAgICAgICAgaWYodGhpcy5mb3JtLnN0YXI9PW51bGwpewogICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSgi6K+36L6T5YWl5pif57qnIik7CiAgICAgICAgICAgICAgICByZXR1cm47CiAgICAgICAgICAgIH0KCiAgICAgICAgICAgIGlmKHRoaXMuZm9ybS5yZW1hcms9PW51bGwgfHwgdGhpcy5mb3JtLnJlbWFyay50cmltKCkgPT09ICcnKXsKICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoIuivt+i+k+WFpeivhOS7t+WGheWuuSIpOwogICAgICAgICAgICAgICAgcmV0dXJuOwogICAgICAgICAgICB9CgogICAgICAgICAgICBpZighdGhpcy5jdXJyZW50VGFzayB8fCAhdGhpcy5jdXJyZW50VGFzay5hY2NlcHQpewogICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5Lu75Yqh5L+h5oGv5LiN5a6M5pW077yM5peg5rOV5o+Q5Lqk6K+E5Lu3Iik7CiAgICAgICAgICAgICAgICByZXR1cm47CiAgICAgICAgICAgIH0KCiAgICAgICAgICAgIGNvbnN0IGFpZCA9IHRoaXMuY3VycmVudFRhc2suYWNjZXB0LmlkOwogICAgICAgICAgICBjb25zdCB0YXNraWQgPSB0aGlzLmN1cnJlbnRUYXNrLmlkOwogICAgICAgICAgICBjb25zdCBwaWQgPSB0aGlzLmN1cnJlbnRUYXNrLnB1Ymxpc2guaWQ7CgogICAgICAgICAgICBjb25zb2xlLmxvZygn5o+Q5Lqk6K+E5Lu3OicsIGFpZCwgdGFza2lkLCBwaWQpOwoKICAgICAgICAgICAgYWRkUmVtYXJrKHsKICAgICAgICAgICAgICAgICJzdGFyIjogdGhpcy5mb3JtLnN0YXIsCiAgICAgICAgICAgICAgICAicmVtYXJrIjogdGhpcy5mb3JtLnJlbWFyaywKICAgICAgICAgICAgICAgICJhY2NlcHRJZCI6IGFpZCwKICAgICAgICAgICAgICAgICJwdWJsaXNoSWQiOiBwaWQsCiAgICAgICAgICAgICAgICAidGFza0lkIjogdGFza2lkLAogICAgICAgICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi6K+E5Lu35o+Q5Lqk5oiQ5YqfIik7CiAgICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgICAgICAgICAgIHRoaXMucmV0cmlldmVEYXRhKCk7IC8vIOWIt+aWsOS7u+WKoeWIl+ihqAogICAgICAgICAgICB9KS5jYXRjaChlcnJvciA9PiB7CiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCfor4Tku7fmj5DkuqTlpLHotKU6JywgZXJyb3IpOwogICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi6K+E5Lu35o+Q5Lqk5aSx6LSl77yM6K+356iN5ZCO6YeN6K+VIik7CiAgICAgICAgICAgIH0pOwogICAgICAgIH0sCgogICAgICAgIC8vIOWPlua2iOaMiemSrgogICAgICAgIGV4aXQoKSB7CiAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICAgICAgICB0aGlzLnJlc2V0KCk7CiAgICAgICAgICAgIHRoaXMuY3VycmVudFRhc2sgPSBudWxsOwogICAgICAgIH0sCgogICAgICAgIC8vIOihqOWNlemHjee9rgogICAgICAgIHJlc2V0KCkgewogICAgICAgICAgICB0aGlzLmZvcm0gPSB7CiAgICAgICAgICAgICAgICBpZDogbnVsbCwKICAgICAgICAgICAgICAgIHN0YXI6IG51bGwsCiAgICAgICAgICAgICAgICByZW1hcms6IG51bGwsCiAgICAgICAgICAgIH07CiAgICAgICAgfSwKICAgIH0sCiAgICBmaWx0ZXJzOiB7CiAgICAgICAgZm9ybWF0RGF0ZSh0aW1lKSB7CiAgICAgICAgICAgIGxldCBkYXRlID0gbmV3IERhdGUodGltZSk7CiAgICAgICAgICAgIHJldHVybiBmb3JtYXREYXRlKGRhdGUsICd5eXl5LU1NLWRkIGhoOm1tJyk7CiAgICAgICAgfQogICAgfQp9Cg=="}, {"version": 3, "sources": ["Published.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkJA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Published.vue", "sourceRoot": "src/views/user/children", "sourcesContent": ["<template>\n    <div class=\"content\">\n        <el-card class=\"box-card\">\n            <div slot=\"header\" class=\"clearfix\">\n                <span>已发布任务</span>\n            </div>\n\n            <!-- 状态分类标签 -->\n            <div class=\"status-tabs\">\n                <el-tabs v-model=\"activeStatus\" type=\"card\">\n                    <el-tab-pane\n                        v-for=\"group in tasksByStatus\"\n                        :key=\"group.status\"\n                        :label=\"group.statusName + ' (' + group.tasks.length + ')'\"\n                        :name=\"group.status.toString()\"\n                    >\n                        <el-card\n                            class=\"box-card\"\n                            v-for=\"item in group.tasks\"\n                            :key=\"item.id\"\n                            style=\"margin-top: 20px\"\n                        >\n                <div slot=\"header\" class=\"clearfix\"\n                     style=\"display: flex; align-items: center; justify-content: space-between\">\n                        <span style=\"display: flex;align-items: center\">\n                            <el-tag :type=\"item.state == 0 ? 'danger':(item.state == 1 ? 'warning':'success')\"\n                                    style=\"margin-right: 5px\">{{item.state == 0 ? '待解决':(item.state == 1 ? '服务中':'已完成')}}</el-tag>\n                            {{item.taskTitle}}\n                        </span>\n\n                    <!-- 评价按钮 -->\n                    <el-button v-show=\"item.state == 2\"\n                    style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"remark(item)\">订单评价</el-button>\n\n\n                    <el-button style=\"float: right; padding: 3px 0\" type=\"text\" v-show=\"item.state != 0\"\n                               @click=\"receiver(item)\">查看维修员信息\n                    </el-button>\n                    <template>\n<!--                        <i class=\"el-icon-edit\" style=\"cursor: pointer; color: #66b1ff\" v-show=\"item.state == 0\"/>-->\n                        <el-popconfirm title=\"确定取消任务吗？\" @confirm=\"cancel(item.id)\" v-show=\"item.state == 0\">\n                            <el-button style=\"float: right; padding: 3px 0\" type=\"text\" slot=\"reference\">取消任务\n                            </el-button>\n                        </el-popconfirm>\n                    </template>\n                </div>\n\n                <el-steps :active=\"item.state + 1\" finish-status=\"success\">\n                    <el-step title=\"发布成功\" :description=\"item.createTime | formatDate\"></el-step>\n                    <el-step title=\"服务中\" :description=\"item.orderTime ? transform(item.orderTime):'暂时没人服务'\"></el-step>\n                    <el-step title=\"完成时间\" :description=\"item.endTime ? transform(item.endTime):''\"></el-step>\n                </el-steps>\n\n                <el-collapse style=\"margin-top: 20px\" v-model=\"activeNames\">\n                    <el-collapse-item title=\"任务内容\" name=\"1\">\n                        <div>{{item.taskContext}}</div>\n                    </el-collapse-item>\n                    <el-collapse-item title=\"任务金额\" name=\"2\">\n                        <div><i class=\"el-icon-money\" style=\"color: red;\"> {{item.reward}}元</i></div>\n                    </el-collapse-item>\n                    <el-collapse-item title=\"发布时间\" name=\"3\">\n                        <div>{{item.createTime | formatDate}}</div>\n                    </el-collapse-item>\n                    <el-collapse-item title=\"类别\" name=\"4\">\n                        <div>{{item.dept ? item.dept.name : '未设置'}}</div>\n                    </el-collapse-item>\n                    <el-collapse-item title=\"子类别\" name=\"5\">\n                        <div>{{item.type ? item.type.name : '未设置'}}</div>\n                    </el-collapse-item>\n                    <el-collapse-item title=\"地址\" name=\"6\" v-if=\"item.province\">\n                        <div>{{item.province}} {{item.city}} {{item.district}}</div>\n                    </el-collapse-item>\n                    <el-collapse-item title=\"详细地址\" name=\"7\" v-if=\"item.address\">\n                        <div>{{item.address}}</div>\n                    </el-collapse-item>\n                </el-collapse>\n\n                <el-button type=\"primary\" style=\"float: right;margin:10px 0;\" @click=\"completeTask(item.id)\"\n                           v-show=\"item.state==1\">完成任务\n                </el-button>\n\n                        </el-card>\n\n                        <!-- 当前状态下没有任务时显示 -->\n                        <div style=\"text-align: center; margin-top: 20px;\" v-if=\"group.tasks.length === 0\">\n                            <span><i class=\"el-icon-refresh-right\"></i>该状态下暂无发布任务</span>\n                        </div>\n                    </el-tab-pane>\n                </el-tabs>\n            </div>\n\n            <!-- 没有任何任务时显示 -->\n            <div style=\"text-align: center\" v-if=\"tasks.length === 0\">\n                <span><i class=\"el-icon-refresh-right\"></i>暂无发布任务</span>\n            </div>\n        </el-card>\n\n        <el-drawer\n                title=\"维修员信息\"\n                :visible.sync=\"drawer\"\n                direction=\"rtl\">\n            <div class=\"content_drawer\">\n                <el-card class=\"box-card\" v-if=\"recipientInformation != ''\">\n                    <el-collapse v-model=\"drawerNames\">\n                        <el-collapse-item title=\"姓名\" name=\"1\">\n                            <div>{{recipientInformation.username}}</div>\n                        </el-collapse-item>\n                        <el-collapse-item title=\"电话\" name=\"2\">\n                            <div>{{recipientInformation.phone}}</div>\n                        </el-collapse-item>\n                        <el-collapse-item title=\"角色\" name=\"3\">\n                            <div>{{recipientInformation.role.name}}</div>\n                        </el-collapse-item>\n                        <el-collapse-item title=\"类别\" name=\"4\">\n                            <div>{{recipientInformation.dept.name}}</div>\n                        </el-collapse-item>\n                        <el-collapse-item title=\"子类别\" name=\"5\">\n                            <div>{{recipientInformation.type.name}}</div>\n                        </el-collapse-item>\n                    </el-collapse>\n                </el-card>\n            </div>\n        </el-drawer>\n\n        <!-- 添加或修改remark对话框 -->\n        <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\n        <el-form ref=\"form\" :model=\"form\"  :rules=\"rules\" label-width=\"80px\" >\n            <el-form-item label=\"星级\" prop=\"star\">\n                <el-rate\n                    v-model=\"form.star\"\n                    show-text>\n                </el-rate>\n            </el-form-item>\n            <el-form-item label=\"评价内容\" prop=\"remark\">\n            <el-input v-model=\"form.remark\" placeholder=\"请输入评价内容\" />\n            </el-form-item>\n        </el-form>\n        <div slot=\"footer\" class=\"dialog-footer\">\n            <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n            <el-button @click=\"exit\">取 消</el-button>\n        </div>\n        </el-dialog>\n    </div>\n</template>\n\n<script>\n    import {mapState} from \"vuex\"\n    import {formatDate} from '@/util/date';\n    import { addRemark, } from \"@/api/remark/remark\";\n\n    export default {\n        name: \"Published\",\n        data() {\n            return {\n                // 是否显示弹出层\n                open: false,\n                activeNames: ['1', '2', '3', '4', '5', '6', '7'],\n                drawerNames: ['1', '2', '3', '4', '5'],\n                tasks: [],\n                drawer: false,\n                recipientInformation: [],\n                // 当前选中的状态\n                activeStatus: '-1',\n                // 表单参数\n                form: {},\n                // 表单校验\n                rules: {\n                },\n                // 弹出层标题\n                title: \"\",\n                // 当前选中的任务\n                currentTask: null,\n            };\n        },\n        computed: {\n            ...mapState('user', ['user']),\n\n            // 按状态分组的任务\n            tasksByStatus() {\n                // 定义状态映射\n                const statusMap = {\n                    '-1': { name: '全部', tasks: [] },\n                    '0': { name: '待接单', tasks: [] },\n                    '1': { name: '服务中', tasks: [] },\n                    '2': { name: '已完成', tasks: [] }\n                };\n\n                // 添加\"全部\"分类\n                statusMap['-1'].tasks = this.tasks;\n\n                // 按状态分组\n                this.tasks.forEach(task => {\n                    const state = task.state !== null && task.state !== undefined ? task.state.toString() : '0';\n                    if (statusMap[state]) {\n                        statusMap[state].tasks.push(task);\n                    }\n                });\n\n                // 转换为数组格式，方便在模板中使用\n                return Object.entries(statusMap).map(([status, data]) => ({\n                    status: parseInt(status),\n                    statusName: data.name,\n                    tasks: data.tasks\n                }));\n            }\n        },\n        created() {\n            this.retrieveData()\n        },\n        methods: {\n            retrieveData() {\n                this.$get(\"/task/published\", {id: this.user.id}).then(res => {\n                    console.log(res.data.task)\n                    this.tasks = res.data.task\n                })\n            },\n            receiver(val) {\n                console.log(val)\n                this.recipientInformation = val.accept;\n                // console.log(this.recipientInformation)\n                this.drawer = true\n            },\n\n            transform(time) {\n                let date = new Date(time);\n                return formatDate(date, 'yyyy-MM-dd hh:mm');\n            },\n\n            cancel(id) {\n                this.$del(\"/task/\" + id)\n                    .then(res => {\n                        this.retrieveData()\n                        this.$notifyMsg('成功', res.data.msg, \"success\");\n                    })\n            },\n            completeTask(id) {\n                this.$msgbox({\n                    title: '提示',\n                    message: '确定该维修员完成此任务了吗？',\n                    showCancelButton: true,\n                    confirmButtonText: '确定',\n                    cancelButtonText: '取消',\n                    beforeClose: ((action, instance, done) => {\n                        if (action == 'confirm') {\n                            // instance.confirmButtonLoading = true;\n                            instance.confirmButtonText = '执行中...';\n                            this.$put('task/' + id)\n                                .then((res) => {\n                                    done();\n                                    instance.confirmButtonLoading = false;\n                                    this.$msg(res.data.msg, \"success\");\n                                    this.retrieveData();\n\n                                    // 如果返回了更新后的用户信息，更新当前用户数据\n                                    if (res.data.data && res.data.data.id) {\n                                        // 检查返回的用户是否是当前登录用户\n                                        if (res.data.data.id === this.user.id) {\n                                            console.log('更新当前用户余额信息:', res.data.data);\n                                            this.$store.commit('user/setUser', res.data.data);\n                                        }\n                                    }\n                                })\n                        } else {\n                            done();\n                        }\n                    })\n                }).catch(() => {\n                })\n            },\n            // 评价用户\n            remark(task){\n                this.currentTask = task;\n                this.open = true;\n                this.title = \"添加评价\";\n            },\n\n            /** 提交按钮 */\n            submitForm() {\n                if(this.form.star==null){\n                    this.$message(\"请输入星级\");\n                    return;\n                }\n\n                if(this.form.remark==null || this.form.remark.trim() === ''){\n                    this.$message(\"请输入评价内容\");\n                    return;\n                }\n\n                if(!this.currentTask || !this.currentTask.accept){\n                    this.$message.error(\"任务信息不完整，无法提交评价\");\n                    return;\n                }\n\n                const aid = this.currentTask.accept.id;\n                const taskid = this.currentTask.id;\n                const pid = this.currentTask.publish.id;\n\n                console.log('提交评价:', aid, taskid, pid);\n\n                addRemark({\n                    \"star\": this.form.star,\n                    \"remark\": this.form.remark,\n                    \"acceptId\": aid,\n                    \"publishId\": pid,\n                    \"taskId\": taskid,\n                }).then(() => {\n                    this.$message.success(\"评价提交成功\");\n                    this.open = false;\n                    this.reset();\n                    this.retrieveData(); // 刷新任务列表\n                }).catch(error => {\n                    console.error('评价提交失败:', error);\n                    this.$message.error(\"评价提交失败，请稍后重试\");\n                });\n            },\n\n            // 取消按钮\n            exit() {\n                this.open = false;\n                this.reset();\n                this.currentTask = null;\n            },\n\n            // 表单重置\n            reset() {\n                this.form = {\n                    id: null,\n                    star: null,\n                    remark: null,\n                };\n            },\n        },\n        filters: {\n            formatDate(time) {\n                let date = new Date(time);\n                return formatDate(date, 'yyyy-MM-dd hh:mm');\n            }\n        }\n    }\n</script>\n\n<style scoped lang=\"less\">\n    .content {\n        background: #FFf;\n        margin: 0 15px;\n        padding: 15px;\n    }\n\n    .status-tabs {\n        margin-bottom: 20px;\n\n        /deep/ .el-tabs__header {\n            margin-bottom: 15px;\n        }\n\n        /deep/ .el-tabs__item {\n            height: 40px;\n            line-height: 40px;\n            font-size: 14px;\n            color: #606266;\n\n            &.is-active {\n                color: #409EFF;\n                font-weight: bold;\n            }\n        }\n    }\n</style>\n"]}]}