# 支付宝沙箱配置说明

## 1. 支付宝开放平台沙箱配置

### 1.1 登录支付宝开放平台
访问：https://openhome.alipay.com/develop/sandbox/app

### 1.2 获取配置信息
在沙箱应用中获取以下信息：
- **APPID**：应用ID
- **应用私钥**：您生成的RSA2私钥
- **支付宝公钥**：支付宝提供的公钥

### 1.3 生成RSA2密钥对
1. 下载支付宝密钥生成工具
2. 选择密钥长度：2048
3. 选择密钥格式：PKCS8(Java适用)
4. 生成密钥对
5. 将公钥上传到支付宝开放平台

## 2. 项目配置

### 2.1 更新PayUtil.java配置
修改 `src/main/java/com/yqn/common/utils/PayUtil.java` 文件中的以下配置：

```java
// 替换为您的沙箱APPID
private final String APP_ID = "您的APPID";

// 替换为您生成的RSA2私钥
private final String APP_PRIVATE_KEY = "您的应用私钥";

// 替换为支付宝提供的公钥
private final String ALIPAY_PUBLIC_KEY = "支付宝公钥";

// 替换为您的natapp域名
private final String NOTIFY_URL = "http://您的域名.natappfree.cc/api/alipay/notify";
```

### 2.2 配置内网穿透
1. 注册并下载natapp：https://natapp.cn/
2. 获取免费隧道或购买付费隧道
3. 启动natapp，映射本地8080端口
4. 将获得的公网域名配置到NOTIFY_URL中

## 3. 支付宝沙箱环境配置

### 3.1 在支付宝开放平台配置回调地址
在应用配置中设置：
- **授权回调地址**：http://您的域名.natappfree.cc/api/alipay/notify
- **网关地址**：https://openapi-sandbox.dl.alipaydev.com/gateway.do

### 3.2 沙箱账号
支付宝会为您提供沙箱买家账号和卖家账号用于测试

## 4. 测试流程

### 4.1 启动项目
1. 启动Spring Boot应用（端口8080）
2. 启动前端应用（端口8848）
3. 启动natapp内网穿透

### 4.2 测试充值
1. 登录系统（使用role_id=14的用户）
2. 点击"充值余额"按钮
3. 输入充值金额
4. 跳转到支付宝沙箱支付页面
5. 使用沙箱买家账号完成支付
6. 支付成功后自动跳转回系统
7. 检查用户余额是否增加

## 5. 常见问题

### 5.1 签名验证失败
- 检查应用私钥和支付宝公钥是否正确
- 确保密钥格式正确（PKCS8）
- 检查字符编码是否为UTF-8

### 5.2 回调地址无法访问
- 确保natapp正常运行
- 检查防火墙设置
- 确保回调地址配置正确

### 5.3 支付后余额未更新
- 检查异步通知是否正常接收
- 查看后台日志确认处理流程
- 检查数据库充值记录表

## 6. 数据库表结构

充值记录表已自动创建：
```sql
CREATE TABLE `recharge_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `order_no` varchar(64) NOT NULL,
  `trade_no` varchar(64) DEFAULT NULL,
  `amount` decimal(10,2) NOT NULL,
  `status` tinyint(4) NOT NULL DEFAULT '0',
  `payment_method` varchar(20) NOT NULL DEFAULT 'alipay',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `pay_time` datetime DEFAULT NULL,
  `remark` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
);
```

## 7. API接口

### 7.1 创建支付订单
- **URL**: POST /api/alipay/create
- **参数**: 
  ```json
  {
    "userId": 1,
    "totalAmount": 100.00,
    "subject": "用户充值"
  }
  ```

### 7.2 支付宝异步通知
- **URL**: POST /api/alipay/notify
- **说明**: 支付宝回调接口，无需手动调用

### 7.3 支付宝同步返回
- **URL**: GET /api/alipay/return
- **说明**: 支付完成后跳转接口

### 7.4 查询支付状态
- **URL**: GET /api/alipay/query/{orderNo}
- **说明**: 查询订单支付状态

## 8. 安全注意事项

1. **私钥安全**：应用私钥不要泄露，生产环境建议使用配置文件或环境变量
2. **签名验证**：必须验证支付宝回调的签名
3. **金额校验**：回调时必须校验订单金额
4. **重复处理**：避免重复处理同一笔订单
5. **日志记录**：记录所有支付相关操作的日志

## 9. 生产环境部署

生产环境需要修改以下配置：
- 网关地址改为：https://openapi.alipay.com/gateway.do
- 使用正式的APPID和密钥
- 配置正式的回调域名
- 申请正式的支付宝应用
